import React from 'react';
import {Provider as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>} from 'mobx-react/native';
import {NavigationContainer} from '@react-navigation/native';
import NavStacks from '@/NavStacks';
import {MobxStates, MobxActions} from '@/mobx';
import i18n from './src/i18n';
import {LogBox} from 'react-native';
import {routeListener} from '@utils/log';
import * as Sentry from '@sentry/react-native';
import pkg from './package.json';
import {SENTRY_DSN} from '@config/index.js';

/**
 * @typedef {import('@config/types').InitialParamsData} InitialParamsData
 */

const routingInstrumentation = new Sentry.ReactNavigationInstrumentation();

class App extends React.Component {
  /**
   * @type {InitialParamsData}
   */
  props;

  constructor(props) {
    super(props);
    LogBox.ignoreLogs([
      'Non-serializable values were found in the navigation state',
      "ViewPropTypes will be removed from React Native, along with all other PropTypes. We recommend that you migrate away from PropTypes and switch to a type system like TypeScript. If you need to continue using ViewPropTypes, migrate to the 'deprecated-react-native-prop-types' package.",
    ]);
    this.initSentry();
    this.changeLanguageBasedOnProps();
  }

  initSentry = () => {
    const {name, version} = pkg;
    const nodeEnv = process.env.NODE_ENV;
    const {region, env = nodeEnv, userId} = this.props;

    if (nodeEnv === 'development') {
      return;
    }

    Sentry.init({
      dsn: SENTRY_DSN,
      release: `${name}@${version}`,
      environment: `${env}_${region}`,
      // Set tracesSampleRate to 1.0 to capture 100% of transactions for performance monitoring.
      // We recommend adjusting this value in production.
      tracesSampleRate: 0.8,
      integrations: [
        new Sentry.ReactNativeTracing({
          enableUserInteractionTracing: true,
          routingInstrumentation,
        }),
      ],
    });

    Sentry.setTag('user_id', userId);
  };

  changeLanguageBasedOnProps = () => {
    const {lang = 'en', region = 'NA'} = this.props;

    if (region === 'NA' && lang === 'en') {
      i18n.changeLanguage('en-US');
    } else if (region === 'EU' && lang === 'en') {
      i18n.changeLanguage('en-GB');
    } else {
      i18n.changeLanguage(lang);
    }
  };

  render() {
    return (
      <MobxProvider {...MobxStates} {...MobxActions}>
        <NavContainer {...this.props} />
      </MobxProvider>
    );
  }
}

const NavContainer = ({...props}) => {
  const navigation = React.useRef();
  return (
    <NavigationContainer
      ref={navigation}
      onStateChange={routeListener}
      onReady={() => {
        routingInstrumentation.registerNavigationContainer(navigation);
      }}>
      <NavStacks {...props} />
    </NavigationContainer>
  );
};

export default Sentry.withProfiler(App);
