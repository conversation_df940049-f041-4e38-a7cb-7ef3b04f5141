{
  "presets": [
    "module:metro-react-native-babel-preset",
    "@babel/preset-react"
  ],
  "plugins": [
    [
      "@babel/plugin-proposal-decorators",
      {
        "legacy": true
      }
    ],
    ["module-resolver", {
      "root": ["./"],
      "alias": {
        "@": "./src",
        "@containers": "./src/containers",
        "@api": "./src/api",
        "@components": "./src/components",
        "@config": "./src/config",
        "@i18n": "./src/i18n",
        "@assets": "./src/assets",
        "@utils": "./src/utils",
        "@pages": "./src/pages",
        "@base": "./src/base",
        "IOTRN": "./",
        "@cvn-icon": "./src/utils/cvn-icon"
      }
    }],
    "react-native-reanimated/plugin"
  ],
  "env": {
    "production": {
      "plugins": [
        // "transform-remove-console"
        ["transform-remove-console", { "exclude": [ "error", "warn"] }]
      ]
    }
  }
}