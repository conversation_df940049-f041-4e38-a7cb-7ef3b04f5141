import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {inject, observer} from 'mobx-react/native';
import Routes from '@pages/Routes';
import {omit} from 'lodash-es';

/**
 * @typedef {import('@/mobx').panelStores} panelStores
 * @typedef {import('@/mobx').panelActions} panelActions
 * @typedef {import('react-i18next').TFunction} TFunction
 */

/**
 * @typedef {Object} InjectedProps
 * @property {panelStores} panel - MobX Store
 * @property {panelActions} panelActions - Mobx Store Action
 * @property {TFunction} t - i18next translation function
 */

const Stack = createStackNavigator();

@inject('panelActions')
@observer
export default class NavStacks extends React.Component {
  /**
   * @type {InjectedProps}
   */
  props;

  constructor(props) {
    super(props);
    this.props.panelActions.initParams(omit(props, ['panelActions', 'panel']));
  }

  render() {
    const routes = Object.keys(Routes);
    return (
      <Stack.Navigator
        screenOptions={() => {
          return {
            // ...screenOptions,
            headerShown: false,
            // 可以控制手势返回，暂时全部关闭
            gestureEnabled: false,
          };
        }}>
        {routes.map(routeName => {
          const Component = Routes[routeName];
          return (
            <Stack.Screen
              key={routeName}
              name={routeName}
              component={Component}
              options={{title: routeName}}
            />
          );
        })}
      </Stack.Navigator>
    );
  }
}
