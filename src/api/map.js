/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2024-11-25 10:26:20
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-11-25 10:29:01
 * @FilePath: /react-native-panels/src/api/map.js
 * @Description: api 接口请求封装
 *
 */
import {CVNSdk} from '@cvn/rn-panel-kit';
import {Platform} from 'react-native';
const apiMap = {
  deviceDetail: '/device/rn/detail',
  deviceEdit: '/device/edit',
  faultInfo: '/device/rn/faultInfo',
};

export default apiMap;
/**
 * 拉取首页故障信息接口
 * @param {Object} panel
 * @param {Function} callBack
 * @returns {Promise} 供外部调用
 */
export const fetchFaultInfo = (panel = {}, callback = () => {}) => {
  return CVNSdk.apiRequest(
    apiMap.faultInfo,
    {
      deviceId: panel.deviceId,
    },
    {showLoading: false},
  ).then(res => {
    console.log('fetchFaultInfo response', res);
    callback(res.entry || {});
  });
};

/**
 * @description: 设备详情接口
 * @param {Object} options
 * @param {Object}  panel
 * @return {Promise} 供外部调用
 */
export const getDetail = ({showLoading = false}, panel = {}) => {
  const {deviceId, appType = Platform.OS, appVersion = '1.1.0'} = panel;
  const params = {
    deviceId,
    appType,
    appVersion,
  };
  return CVNSdk.apiRequest(apiMap.deviceDetail, params, {showLoading});
};

/**
   * 设备编辑
   * @param {Object} params
   * @return {Promise} 供外部调用
   */
export const deviceEditRequest = params => {
  return CVNSdk.apiRequest(apiMap.deviceEdit, params);
};