import {CVNSdk} from '@cvn/rn-panel-kit';
const apiMap = {
  deviceDetail: '/device/rn/detail',
  deviceEdit: '/device/edit',
  faultInfo: '/device/rn/faultInfo',
};

export default apiMap;

/**
 * 拉取首页故障信息接口
 * @param {Object} panel
 * @param {Function} callBack
 * @returns {Promise} 供外部调用
 */
export const fetchFaultInfo = (panel = {}, callback = () => {}) => {
  console.log('fetchFaultInfo-panel.deviceId--', panel.deviceId);

  return CVNSdk.apiRequest(
    apiMap.faultInfo,
    {
      deviceId: panel.deviceId,
    },
    {showLoading: false},
  ).then(res => {
    console.log('fetchFaultInfo---', res);
    callback(res.entry || {});
  });
};
/**
 * 设备编辑
 * @param {Object} params
 * @return {Promise} 供外部调用
 */
export const deviceEditRequest = params => {
  return CVNSdk.apiRequest(apiMap.deviceEdit, params);
};
