/**
 * 因 此处 文件和物模型强绑定，故不抽离到cvn-panel-kit，防止新增或修改物模型，
 * 导致cvn-panel-kit库的发版，不方便，此处作为方法的本地抽离
 *  */
import {NumberUtils, DpUtils, StringUtils} from 'cvn-panel-kit/src/utils';
import {isArray, isString} from 'lodash-es';
import ModelMap, {dpTypeMap, dp_len_1, dp_len_4} from '@utils/model';

const {getDplen} = DpUtils;

/**
 *
 * @param {包含dp_id和dp_data的对象} resObj
 * @param {属性时：传入的dp_data；事件或服务时：param_data_value } data
 * @returns
 */
/**
 * @description: 1 解析成十进制的值，raw类型跳过解析
 * 2 resObj: 包含dp_id和dp_data的对象,
 * 3 data:属性时：传入的dp_data；事件或服务时：param_data_value
 * @param {Object} resObj
 * @param {Object} data
 * @return {Number | String} 解析之后的值
 */
export const parseDpDataInner = (
  resObj: {dp_id: string; dp_type: string},
  data: string | string[],
) => {
  const {dp_id, dp_type} = resObj;
  if (!data) {
    console.warn('parseDpDataInner--data exception');
    return;
  }
  let realData = data;
  // 直接用新的安装包测试
  if (dp_type === '09' && data.length >= 6) {
    // 前两个字节(字符串长度4)舍弃
    realData = data.slice(4);
  }
  const tmpdpData = NumberUtils.dataConversion(realData);
  let result: number | boolean = Number.parseInt(tmpdpData, 16);
  /**
   * string 单独处理，转换成明文string
   */
  switch (`${dp_id}`) {
    case ModelMap.gps_coordinate:
    case ModelMap.modify_password:
      result = StringUtils.hexToString(realData);
      break;
    case ModelMap.reset_pwd:
      result = realData === '01';
      break;
    default:
      break;
  }
  return result;
};
/**
 * @description: 解析dpdata,保证页面使用时维持原有逻辑不变
 * @param {Object} resObj
 * @param {Object} item
 * @return {Array} 最后得到的dpdata
 */
export const parseDpData = (
  resObj: {dp_id: string; dp_type: string},
  item: {dp_data: {[key: string]: string}[]},
) => {
  let result: string | number | boolean = '';
  /**
   * 1 小端转成大端  只有int，其他一个字节转换不影响,
   * 2 raw,params 需要单独处理, 且不需要大小端转换
   * 3 isString，判定为属性值的返回，按照原有逻辑解析，
   * 4 isArray 事件和服务返回是数组，对应多参'05'类型
   */
  if (isString(item.dp_data)) {
    result = parseDpDataInner(resObj, item.dp_data) ?? '';
  } else if (isArray(item.dp_data) && item.dp_data.length > 0) {
    /**
     * 取值 param_data_value 作为原先的dp_data，如后面多参数场景，再进行更改代码处理,目前只是对做了多参的处理，
     * 但是并没有实际多参的场景
     * [
          {
              "dp_id":"003D",
              "dp_type":"05",  // 多参数以后，传固定值05 parmaKey
              "dp_len":"0001", // 暂时按照原来逻辑不动
              "dp_data":[
                  {
                      "param_id":"",
                      "param_type":"",
                      "param_data_len":"",
                      "param_data_value":""
                  }
              ]
          }
      ]
     */
    const obj = item.dp_data[0];
    if (obj && 'param_data_value' in obj) {
      result = parseDpDataInner(resObj, obj.param_data_value) ?? '';
    }
  }
  return result;
};

/**
 * 单参数改成多参数 12.12
 * 旧的调用方法，无需更改，下面统一处理，有新的物模型出现时（尤其是设计多个参数时，需要调用新方法V2）
 *  */
/**
   * 最终调用原生的数据结构如下
   * [
        {
            "dp_id":"003D",
            "dp_type":"05",  // 多参数以后，传固定值05 parmaKey
            "dp_len":"0001", // 暂时按照原来逻辑不动
            "dp_data":[
                {
                    "param_id":"",
                    "param_type":"",
                    "param_data_len":"",
                    "param_data_value":""
                }
            ]
        }
    ]
   */

/**
 * @description: 蓝牙通道设置属性
 * @param {Object} propertyData
 * @param {Function} callBack
 */
export const editPropertyWithBle = (
  propertyData: {[key: string]: number | undefined},
  callBack: (
    _res: {
      dp_id: string;
      dp_type: string;
      dp_data: {
        param_id: string;
        param_type: string;
        param_data_len: string;
        param_data_value: string;
      }[];
      dp_len: string;
    }[],
  ) => void,
) => {
  /**
         * 原来业务调用方法：
         * {"1": true}  // propertyData
         * 新的业务调用方法：
         * {
            "1":[
             {
                  "paramId":"",
                  "paramData": true,   // 和原来直接调用  {"1": true} 一样
              }
            ]
          }
         *  */
  const data = Object.keys(propertyData).map(key => {
    const dp_id = NumberUtils.numToHexString(Number(key), 4).toUpperCase();
    const dp_type = '05'; // 有无影响,无
    const newData = propertyData[key];
    let dp_data;
    if (isArray(newData)) {
      dp_data = getDpData(newData, key);
    } else {
      // 兼容旧的传输过程对应的解析,默认单个数组
      dp_data = getDpData([{paramData: newData ?? 0, paramId: '1'}], key);
    }
    const dp_len = getDplen(dp_data); // 根据dp_data算出来
    return {
      dp_id,
      dp_type,
      dp_data,
      dp_len,
    };
  });
  const resultData = data?.filter(item => {
    return item.dp_id !== undefined;
  });
  callBack(resultData);
};

/**
 * @description: 新的获取dpData方法
 * @param {Array} newData,  eg：[{paramData: newData, paramId: 1}]
 * @param {String} key
 * @return {Array} 新的dpData
 */
export const getDpData = (
  newData: {paramId: string; paramData: number | boolean}[],
  key: string,
) => {
  const result = newData.map(
    (item: {paramId: string; paramData: number | boolean}) => {
      return getDpDataObj(item, key);
    },
  );
  return result;
};
/**
 * @description: 获取dpData 每一项的方法
 * @param {Object} item
 * @param {String} key
 * @return {Object} result
 */
export const getDpDataObj = (
  item: {paramId: string; paramData: number | boolean},
  key: string,
) => {
  // item内的数据时原始数据比如 paramId：1, paramData: true
  const {paramData} = item;
  // 数字转成hexstring
  const tmpParamId = NumberUtils.numToHexString(1, 4).toUpperCase();
  const result = {
    param_id: tmpParamId,
    param_type: '',
    param_data_len: '',
    param_data_value: '',
  };
  // 预留参数paramId，和key一起确定哪个key下的param，对照物模型
  result.param_type = getParamType(key);
  result.param_data_len = getParamlen(key);
  result.param_data_value = getParamData(paramData, key);
  return result;
};

/**
 * @description: 多参数时用
 * @param {String} dpId
 * @return {String} 类型
 */
export const getParamType = (dpId: string) => {
  let result = dpTypeMap.enumKey;
  switch (dpId) {
    case ModelMap.remote_control_angle:
    case ModelMap.time_zone:
      result = dpTypeMap.valueKey;
      break;
    case ModelMap.modify_password:
      result = dpTypeMap.stringKey;
      break;
    case ModelMap.remote_lock:
    case ModelMap.reset_pwd:
      result = dpTypeMap.boolKey;
      break;
    default:
      break;
  }
  // 大端转小端
  return result;
};

/**
 * @description: 获取param 长度
 * @param {String} dpId
 * @param {String} paramId
 * @return {String} hexString表示的长度
 */
export const getParamlen = (dpId: string) => {
  let result = dp_len_1;
  switch (dpId) {
    case ModelMap.total_working_time:
    case ModelMap.remote_control_angle:
    case ModelMap.modify_password:
    case ModelMap.time_zone:
      result = dp_len_4;
      break;
    default:
      break;
  }
  return result;
};

/**
 * @description: 获取dpData
 * @param {Boolean | Number} paramData
 * @param {String} dpId
 * @param {String} paramId
 * @return {Boolean | Number | String} 最内层的data
 */
export const getParamData = (paramData: number | boolean, dpId: string) => {
  // 处理 true false ==> 转成 1 、0
  let dataNumber = paramData || 0;
  if (typeof paramData === 'boolean') {
    dataNumber = paramData === true ? 1 : 0;
  }
  let result = NumberUtils.numToHexString(dataNumber, 2).toUpperCase();
  switch (dpId) {
    // Int类型
    case ModelMap.remote_control_angle:
    case ModelMap.time_zone: {
      // 4个字节，长度8，
      const tmpResult = NumberUtils.numToHexString(dataNumber, 8).toUpperCase();
      result = NumberUtils.dataConversion(tmpResult);
      break;
    }
    // raw和string类型 不需要大小端转换
    // 重置密码待调试
    case ModelMap.modify_password:
      // ‘1234’  --> '31323334'
      result = StringUtils.stringToHexString(paramData);
      break;
    default:
      break;
  }
  return result;
};

const Utils = {
  editPropertyWithBle,
  parseDpData,
};
export default Utils;
