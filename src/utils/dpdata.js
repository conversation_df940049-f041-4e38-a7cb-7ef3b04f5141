import {isObject} from 'lodash-es';

/**
 * 解析物模型数据
 * @param {Object} item
 * @param {String} item.dp_id
 * @param {String} item.dp_data
 * @returns {Number|String|Array}
 */
export const parseDpData = item => {
  const {dp_data} = item;
  // 优先取值 多参
  if (isObject(dp_data) && dp_data['1'] !== undefined) {
    return dp_data['1'];
  }
  return dp_data;
};

const Utils = {
  parseDpData,
};
export default Utils;
