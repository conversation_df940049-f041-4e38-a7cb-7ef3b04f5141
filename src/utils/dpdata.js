import {chunk, isArray, isString, split} from 'lodash-es';
import ModelMap, {dpTypeMap, dp_len_1, dpTypeWithDpid} from '@config/model.js';
import {
  dataConversion,
  numToHexString,
  getDpType,
  getDplen,
} from '@utils/tools.js';
import dayjs from 'dayjs';

/**
 * 1 小端转成大端  只有int，其他一个字节转换不影响,
 * 2 raw,params 需要单独处理, 且不需要大小端转换 todo
 * 3 isString，判定为属性值的返回，按照原有逻辑解析，
 * 4 isArray 事件和服务返回是数组，按照文档来！！
 * @param {Object} item {dp_id, dp_type, dp_data}
 * @param {number} item.dp_id
 * @param {number} item.dp_type
 * @param {string} dp_data
 * @returns {Array|number|string}
 */
export const parseDpData = item => {
  const {dp_id, dp_data} = item;
  // usage_history (1-12月份)使用时长，每个4个字节, unit:s
  if (dp_id === Number(ModelMap.usage_history)) {
    return parseUsageHistory(dp_data);
  }
  if (isString(dp_data)) {
    return parseStringDpData(dp_data);
  } else if (isArray(dp_data)) {
    /**
     * 取值 param_data_value 作为原先的dp_data，如后面多参数场景，再进行更改代码处理,目前只是对做了多参的处理，
     * 但是并没有实际多参的场景，比如原来 开关 物模型 '4': true,如果是多个怎么弄 '4': {'1': true,'2':false}
     * 由dp_id和param_id共同确定某个值
     * [
          {
              "dp_id":"003D",
              "dp_type":"05",  // 多参数以后，传固定值05 parmaKey
              "dp_len":"0001", // 暂时按照原来逻辑不动
              "dp_data":[
                  {
                      "param_id":"",
                      "param_type":"",
                      "param_data_len":"",
                      "param_data_value":""
                  }
              ]
          }
      ]
     */
    return parseStringDpData(dp_data?.[0]?.param_data_value || '');
  } else {
    return dp_data;
  }
};

/**
 * 解析历史记录数据
 * 年(第一个字节), 月(第二个字节), 12个月的数据(剩下12个字节)
 * @param {string} data
 */
export const parseUsageHistory = data => {
  // const year = Number.parseInt(data.substring(0, 2 * 1), 16);
  // const month = Number.parseInt(data.substring(2 * 1, 2 * 2), 16);

  const year = Number.parseInt(
    dataConversion(data.substring(2 * 2, 2 * 4)),
    16,
  ); // 2023
  const month = Number.parseInt(dataConversion(data.substring(0, 2 * 2)), 16);
  const list = parseChunksData(data.substring(2 * 4, data.length - 1), 8).map(
    second => Number((second / 3600).toFixed(2)),
  );
  console.log('👠xxxx', year, month, list);
  const currentDate = dayjs();
  const currentYear = Number(currentDate.format('YYYY'));
  const currentMonth = Number(currentDate.format('M'));
  console.log('👠 current', currentYear, currentMonth);

  if (year > currentYear) {
    return Array(12).fill(0);
  }
  if (year < currentYear) {
    const pastMonth = (currentYear - year) * 12 + (currentMonth - month);
    return pastMonth >= 12
      ? Array(12).fill(0)
      : [...list.slice(pastMonth - 12), ...Array(pastMonth).fill(0)];
  }

  if (month === currentMonth) {
    return [...list.slice(month), ...list.slice(0, month)];
  } else if (month < currentMonth) {
    const diff = currentMonth - month;
    return [
      ...list.slice(currentMonth - 12),
      ...list.slice(0, month),
      ...Array(diff).fill(0),
    ];
  } else {
    return Array(12).fill(0);
  }
};

/**
 * 解析多chunks数据
 * @param {String} data
 * @param {Number} chunksCount
 * @returns {Array}
 */
const parseChunksData = (data = '', chunksCount = 1) => {
  return chunk(split(data, ''), chunksCount).map(cur =>
    parseStringDpData(cur.join('')),
  );
};

/**
 * 解析 int 类型的数据
 * @param {String} data
 * @returns {Number}
 */
export const parseStringDpData = (data = '') => {
  return Number.parseInt(dataConversion(data), 16);
};

/**
 * 单参数改成多参数 12.12
 * 旧的调用方法，无需更改，下面统一处理，有新的物模型出现时（尤其是设计多个参数时，需要调用新方法V2）
 *  */
/**
 * 最终调用原生的数据结构如下
 * [
 {
            "dp_id":"003D",
            "dp_type":"05",  // 多参数以后，传固定值05 parmaKey
            "dp_len":"0001", // 暂时按照原来逻辑不动
            "dp_data":[
                {
                    "param_id":"",
                    "param_type":"",
                    "param_data_len":"",
                    "param_data_value":""
                }
            ]
        }
 ]
 */
export const editPropertyWithBle = (propertyData, callBack = () => {}) => {
  /**
   * 原来业务调用方法：
   * {"1": true}  // propertyData
   * 新的业务调用方法：
   * {
            "1":[
             {
                  "paramId":"",
                  "paramData": true,   // 和原来直接调用  {"1": true} 一样
              }
            ]
          }
   *  */
  const data = Object.keys(propertyData).map(key => {
    let dp_id = numToHexString(Number(key), 4).toUpperCase();
    let dp_type = getDpType(key); // 有无影响,无
    let newData = propertyData[key];
    let dp_data;
    if (isArray(newData)) {
      dp_data = getDpData(newData, key);
      console.log('dp_data is array ', dp_data);
    } else {
      // 兼容旧的传输过程对应的解析,默认单个数组
      dp_data = getDpData([{paramData: newData, paramId: 1}], key);
      console.log('dp_data not array ', dp_data);
    }
    let dp_len = getDplen(dp_data); // 根据dp_data算出来
    return {
      dp_id,
      dp_type,
      dp_data,
      dp_len,
    };
  });
  const resultData = data.filter(item => {
    return item.dp_id !== undefined;
  });
  console.log('蓝牙命令----resultData--', JSON.stringify(resultData));

  // 多个物模型同时调用马力已经支持
  // 外面调用 setLocalProperty
  callBack(resultData);
  // this.setLocalProperty(resultData).then(res => {});
};

// 新的获取dpData方法
export const getDpData = (newData, key) => {
  const result = newData.map(item => {
    return getDpDataObj(item, key);
  });
  return result;
};

// 获取dpData 每一项的方法
export const getDpDataObj = (item, key) => {
  // item内的数据时原始数据比如 paramId：1, paramData: true
  const {paramId, paramData} = item;
  // 数字转成hexstring
  const tmpParamId = numToHexString(1, 4).toUpperCase();
  const result = {
    param_id: tmpParamId,
  };
  // 预留参数paramId，和key一起确定哪个key下的param，对照物模型
  result.param_type = dpTypeWithDpid(key);
  result.param_data_value = getParamData(paramData, key, paramId);
  // result.param_data_len = getParamlen(key, paramId);
  result.param_data_len = numToHexString(
    result.param_data_value.length / 2,
    4,
  ).toUpperCase();

  return result;
};

// 获取param 长度
export const getParamlen = (dpId, paramId) => {
  let result = dp_len_1;
  return result;
};
export const getParamData = (paramData, dpId, paramId) => {
  // console.log('====== paramData', paramData, 'dpid :', dpId, 'paramid :', paramId);
  // 处理 true false ==> 转成 1 、0
  let dataNumber = paramData || 0;
  if (typeof paramData === 'boolean') {
    dataNumber = paramData === true ? 1 : 0;
  }
  let dpType = dpTypeWithDpid(dpId);
  let dataLength = 2;
  let result;
  switch (dpType) {
    // raw和params 不需要大小端转换
    case dpTypeMap.boolKey:
      dataLength = 2;
      result = numToHexString(dataNumber, dataLength).toUpperCase();
      break;
    case dpTypeMap.enumKey:
      dataLength = 2;
      result = numToHexString(dataNumber, dataLength).toUpperCase();
      break;
    case dpTypeMap.valueKey:
      dataLength = 8;
      result = numToHexString(dataNumber, dataLength).toUpperCase();
      result = dataConversion(result);
      console.log('===== conversion :', result);
      break;
    default:
      break;
  }
  // console.log('====== NumberUtils result', result);
  // console.log('====== result', result);

  return result;
};

const Utils = {
  parseDpData,
  editPropertyWithBle,
};
export default Utils;
