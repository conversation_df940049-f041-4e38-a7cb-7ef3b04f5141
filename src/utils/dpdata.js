import {chunk, isArray, isString, split} from 'lodash-es';
import ModelMap, {dpTypeMap, dp_len_1, getParamType} from '@config/model.js';
import {
  dataConversion,
  hexToString,
  numToHexString,
  getDpType,
  getDplen,
} from '@utils/tools.js';

/**
 * 1 小端转成大端  只有int，其他一个字节转换不影响,
 * 2 raw,params 需要单独处理, 且不需要大小端转换 todo
 * 3 isString，判定为属性值的返回，按照原有逻辑解析，
 * 4 isArray 事件和服务返回是数组，按照文档来！！
 * @param {Object} item {dp_id, dp_type, dp_data}
 * @param {string} item.dp_id
 * @param {number} item.dp_type
 * @param {string} dp_data
 * @returns {Array|number|string}
 * @returns
 */
export const parseDpData = (item = {}) => {
  const {dp_id, dp_data} = item;
  if (dp_id === undefined || dp_data === undefined) {
    return undefined;
  }
  const parseMap = [
    {
      condition: () => dp_id === ModelMap.lfp_battery_status,
      result: () => parseLfpBatteryStatus(dp_data),
    },
    {
      condition: () =>
        [
          ModelMap.first_portable_battery_status,
          ModelMap.second_portable_battery_status,
        ].includes(dp_id),
      result: () => parsePortableBatteryStatus(dp_data),
    },
    {
      condition: () => dp_id === ModelMap.remaining_battery_of_batteries,
      result: () => parseRemainingBatteryOfBatteries(dp_data),
    },
    {
      condition: () => dp_id === ModelMap.rated_capacity_of_batteries,
      result: () => parseRatedCapacityOfBatteries(dp_data),
    },
    {
      condition: () =>
        dp_id === ModelMap.dc_charging_remaining_time_of_batteries,
      result: () => parseDcChargingRemainingTimeOfBatteries(dp_data),
    },
    {
      condition: () => dp_id === ModelMap.past_week_charging_energy_list,
      result: () => parsePastWeekChargingEnergyList(dp_data),
    },
    {
      condition: () => dp_id === ModelMap.past_week_discharge_energy_list,
      result: () => parsePastWeekDischargeEnergyList(dp_data),
    },
    {
      condition: () => dp_id === ModelMap.past_year_charging_energy_list,
      result: () => parsePastYearChargingEnergyList(dp_data),
    },
    {
      condition: () => dp_id === ModelMap.past_year_discharging_energy_list,
      result: () => parsePastYearDischargeEnergyList(dp_data),
    },
    {
      condition: () => isString(dp_data),
      result: () => parseStringDpData(dp_data),
    },
    /**
     * 取值 param_data_value 作为原先的dp_data，如后面多参数场景，再进行更改代码处理,目前只是对做了多参的处理，
     * 但是并没有实际多参的场景，比如原来 开关 物模型 '4': true,如果是多个怎么弄 '4': {'1': true,'2':false}
     * 由dp_id和param_id共同确定某个值
     * [
          {
              "dp_id":"003D",
              "dp_type":"05",  // 多参数以后，传固定值05 parmaKey
              "dp_len":"0001", // 暂时按照原来逻辑不动
              "dp_data":[
                  {
                      "param_id":"",
                      "param_type":"",
                      "param_data_len":"",
                      "param_data_value":""
                  }
              ]
          }
      ]
     */
    {
      condition: () => isArray(dp_data),
      result: () => parseStringDpData(dp_data?.[0]?.param_data_value || ''),
    },
  ];
  const target = parseMap.find(map => map.condition());
  if (target) {
    return target.result();
  }
  console.warn('parseDpData failed', item);
  return undefined;
};

/**
 * 解析lfp大包仓位状态数据
 * 数据结构：仓位状态（1字节）
 * @param {String} data
 * @returns {Number}
 */
const parseLfpBatteryStatus = (data = '') => {
  const batteryStatus = data.substring(0, 2 * 1);
  return Number.parseInt(batteryStatus, 16);
};

/**
 * 解析小包仓位1状态数据
 * 数据结构：仓位状态（1字节）+ 电池deviceId（18字节）
 * @param {String} data
 * @returns {Object} {batteryStatus, deviceId}
 */
const parsePortableBatteryStatus = (data = '') => {
  const batteryStatus = data.substring(0, 2 * 1);
  const deviceId = data.substring(2 * 1, 2 * 18);
  return {
    batteryStatus: Number.parseInt(batteryStatus, 16),
    deviceId: hexToString(deviceId),
  };
};

/**
 * 解析各电池包剩余电量数据
 * 数据结构：大包（4字节）+ 小包1（4字节）+ 小包2（4字节）
 * @param {String} data
 * @returns {Array} [大包剩余电量, 小包1剩余电量, 小包2剩余电量]
 */
const parseRemainingBatteryOfBatteries = (data = '') => {
  return parseChunksData(data, 2 * 4);
};

/**
 * 解析各电池包额定容量数据
 * 数据结构：大包（4字节）+ 小包1（4字节）+ 小包2（4字节）
 * @param {String} data
 * @returns {Array} [大包额定容量, 小包1额定容量, 小包2额定容量]
 */
const parseRatedCapacityOfBatteries = (data = '') => {
  return parseChunksData(data, 2 * 4);
};

/**
 * 解析各电池包直流充电剩余时间数据
 * 数据结构：小包1（4字节）+ 小包2（4字节）
 * @param {String} data
 * @returns {Array} [小包1直流充电剩余时间, 小包2直流充电剩余时间]
 */
const parseDcChargingRemainingTimeOfBatteries = (data = '') => {
  return parseChunksData(data, 2 * 4);
};

/**
 * 解析过去一周充电能量数据
 * 数据结构：（uint16, 2字节）* 7
 * @param {String} data
 * @returns {Array} [xx充电能量, xx充电能量, xx充电能量, 大前天充电能量, 前天充电能量, 昨天充电能量, 今天充电能量]
 */
const parsePastWeekChargingEnergyList = (data = '') => {
  return parseChunksData(data, 2 * 2);
};

/**
 * 解析过去一周放电能量数据
 * 数据结构：（uint16, 2字节）* 7
 * @param {String} data
 * @returns {Array} [xx放电能量, xx放电能量, xx放电能量, 大前天放电能量, 前天放电能量, 昨天放电能量, 今天放电能量]
 */
const parsePastWeekDischargeEnergyList = (data = '') => {
  return parseChunksData(data, 2 * 2);
};

/**
 * 解析过去12个月充电能量数据
 * 数据结构：（uint16, 2字节）* 12
 * @param {String} data
 * @returns {Array} [xx月充电能量，xx月充电能量，xx月充电能量，xx月充电能量，xx月充电能量，xx月充电能量，xx月充电能量，xx月充电能量，xx月充电能量，上上月月充电能量，上月充电能量，本月月充电能量]
 */
const parsePastYearChargingEnergyList = (data = '') => {
  return parseChunksData(data, 2 * 2);
};

/**
 * 解析过去12个月放电能量数据
 * 数据结构：（uint16, 2字节）* 12
 * @param {String} data
 * @returns {Array} [xx月放电能量，xx月放电能量，xx月放电能量，xx月放电能量，xx月放电能量，xx月放电能量，xx月放电能量，xx月放电能量，xx月放电能量，上上月放电能量，上月放电能量，本月放电能量]
 */
const parsePastYearDischargeEnergyList = (data = '') => {
  return parseChunksData(data, 2 * 2);
};

/**
 * 解析多chunks数据
 * @param {String} data
 * @param {Number} chunksCount
 * @returns {Array}
 */
const parseChunksData = (data = '', chunksCount = 1) => {
  return chunk(split(data, ''), chunksCount).map(cur =>
    parseStringDpData(cur.join('')),
  );
};

/**
 * 解析 int 类型的数据
 * @param {String} data
 * @returns {Number}
 */
export const parseStringDpData = (data = '') => {
  return Number.parseInt(dataConversion(data) || 0, 16);
};

/**
 * 单参数改成多参数 12.12
 * 旧的调用方法，无需更改，下面统一处理，有新的物模型出现时（尤其是设计多个参数时，需要调用新方法V2）
 *  */
/**
 * 最终调用原生的数据结构如下
 * [
 {
            "dp_id":"003D",
            "dp_type":"05",  // 多参数以后，传固定值05 parmaKey
            "dp_len":"0001", // 暂时按照原来逻辑不动
            "dp_data":[
                {
                    "param_id":"",
                    "param_type":"",
                    "param_data_len":"",
                    "param_data_value":""
                }
            ]
        }
 ]
 */
export const editPropertyWithBle = (propertyData, callBack = () => {}) => {
  /**
   * 原来业务调用方法：
   * {"1": true}  // propertyData
   * 新的业务调用方法：
   * {
            "1":[
             {
                  "paramId":"",
                  "paramData": true,   // 和原来直接调用  {"1": true} 一样
              }
            ]
          }
   *  */
  const data = Object.keys(propertyData).map(key => {
    let dp_id = numToHexString(Number(key), 4).toUpperCase();
    let dp_type = getDpType(key); // 有无影响,无
    let newData = propertyData[key];
    let dp_data;
    if (isArray(newData)) {
      dp_data = getDpData(newData, key);
    } else {
      // 兼容旧的传输过程对应的解析,默认单个数组
      dp_data = getDpData([{paramData: newData, paramId: 1}], key);
    }
    let dp_len = getDplen(dp_data); // 根据dp_data算出来
    return {
      dp_id,
      dp_type,
      dp_data,
      dp_len,
    };
  });
  const resultData = data.filter(item => {
    return item.dp_id !== undefined;
  });
  console.log('蓝牙命令----resultData--', JSON.stringify(resultData));

  // 多个物模型同时调用马力已经支持
  // 外面调用 setLocalProperty
  callBack(resultData);
  // this.setLocalProperty(resultData).then(res => {});
};

// 新的获取dpData方法
export const getDpData = (newData, key) => {
  const result = newData.map(item => {
    return getDpDataObj(item, key);
  });
  return result;
};

// 获取dpData 每一项的方法
export const getDpDataObj = (item, key) => {
  // item内的数据时原始数据比如 paramId：1, paramData: true
  const {paramData} = item;
  // 数字转成hexstring
  const tmpParamId = numToHexString(1, 4).toUpperCase();
  const result = {
    param_id: tmpParamId,
  };
  // 预留参数paramId，和key一起确定哪个key下的param，对照物模型
  result.param_type = getParamType(key);
  result.param_data_value = getParamData(paramData, key);
  // result.param_data_len = getParamlen(key, paramId);
  result.param_data_len = numToHexString(
    result.param_data_value.length / 2,
    4,
  ).toUpperCase();

  return result;
};

// 获取param 长度
export const getParamlen = () => {
  let result = dp_len_1;
  return result;
};
export const getParamData = (paramData, dpId, paramId) => {
  // 处理 true false ==> 转成 1 、0
  let dataNumber = paramData || 0;
  if (typeof paramData === 'boolean') {
    dataNumber = paramData === true ? 1 : 0;
  }
  let dpType = getParamType(dpId);
  let dataLength = 2;
  let result;
  switch (dpType) {
    // raw和params 不需要大小端转换
    case dpTypeMap.boolKey:
      dataLength = 2;
      result = numToHexString(dataNumber, dataLength).toUpperCase();
      break;
    case dpTypeMap.enumKey:
      dataLength = 2;
      result = numToHexString(dataNumber, dataLength).toUpperCase();
      break;
    case dpTypeMap.valueKey:
      dataLength = 8;
      result = numToHexString(dataNumber, dataLength).toUpperCase();
      result = dataConversion(result);
      break;
    default:
      break;
  }

  return result;
};

const Utils = {
  parseDpData,
  editPropertyWithBle,
};
export default Utils;
