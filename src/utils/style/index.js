import {StyleSheet} from 'react-native';
import dp2px from './dp2px';

const CustomStyleSheet = {
  create(_style) {
    let style = {..._style};
    let list = [
      'width',
      'minWidth',
      'maxWidth',
      'height',
      'minHeight',
      'maxHeight',
      'margin',
      'marginTop',
      'marginBottom',
      'marginLeft',
      'marginRight',
      'marginStart',
      'marginEnd',
      'marginHorizontal',
      'marginVertical',
      'padding',
      'paddingLeft',
      'paddingRight',
      'paddingStart',
      'paddingEnd',
      'paddingTop',
      'paddingBottom',
      'paddingHorizontal',
      'paddingVertical',
      'shadowRadius',
      'shadowOpacity',
      'borderRadius',
      'top',
      'right',
      'bottom',
      'left',
      'start',
      'end',
      'fontSize',
      'lineHeight',
    ];
    for (const outKey of Object.keys(style)) {
      for (const innerKey of Object.keys(style[outKey])) {
        if (
          list.includes(innerKey) &&
          typeof style[outKey][innerKey] === 'number'
        ) {
          style[outKey][innerKey] = dp2px(style[outKey][innerKey]);
        }
      }
    }
    return StyleSheet.create(style);
  },
};

export default CustomStyleSheet;
