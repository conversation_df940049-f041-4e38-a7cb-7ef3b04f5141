import dayjs from 'dayjs';

/**
 * get next half hour time
 * 最近下一个以半小时为单位的时间，比如现在是 15:12，那么最近下一个半小时时间就是 15:30
 * @returns {Number}

 * format: HH:mm
 * @returns {String}
 */
export const getNextHalfHourTimeStr = () => {
  const currentTime = dayjs();
  let hour = currentTime.hour();
  let minute = currentTime.minute();
  if (hour === 23 && minute > 30) {
    hour = 0;
  }
  if (minute > 30) {
    minute = 0;
    hour = hour + 1;
  } else {
    minute = 30;
  }
  const hourStr = String(hour).padStart(2, '0');
  const minuteStr = String(minute).padStart(2, '0');
  return `${hourStr}:${minuteStr}`;
};

/**
 * 解析时间戳
 * format: HH:mm
 * @param {Number} timeStamp
 * @returns {String}
 */
export const timestampToTimeStr = timeStamp => {
  return dayjs(timeStamp * 1000).format('HH:mm');
};

/**
 * convert time string to timestamp
 * @param {string} timeStr
 * @returns {Number}
 */
export const timeStrToTimestamp = timeStr => {
  const dateStr = dayjs().format('YYYY/MM/DD'); // '2019/01/25'
  return dayjs(`${dateStr} ${timeStr}`, 'YYYY/MM/DD HH:mm').unix();
};

/**
 * @param {string} time
 * @example
 * '' -> ''
 * 00:23 -> 12:23:AM
 * 06:23 -> 6:23:AM
 * 12:23 -> 12:23:PM
 * 15:23 -> 03:23:PM
 * 23:23 -> 11:23:PM
 * @returns {string}
 */
export const convertTo12HourFormat = time => {
  // 可能是空字符串，直接返回
  if (!time) {
    return time;
  }
  const [hour, minute] = time.split(':');
  const convertedHour = Number(hour);
  const isPM = convertedHour >= 12;
  const hour12 =
    convertedHour > 12
      ? convertedHour - 12
      : convertedHour === 0
      ? 12
      : convertedHour;
  return `${hour12}:${minute} ${isPM ? 'PM' : 'AM'}`;
};

/**
 * @param {string} time
 * @returns {string}
 */
export const convertTo24HourFormat = time => {
  // 可能是空字符串，直接返回
  if (!time) {
    return time;
  }
  const [hour, minutePeriod] = time.split(':');
  const [minute, period] = minutePeriod.split(' ');
  let convertedHour = Number(hour);
  if (period === 'PM' && convertedHour !== 12) {
    convertedHour += 12;
  } else if (period === 'AM' && convertedHour === 12) {
    convertedHour = 0;
  }
  return `${String(convertedHour).padStart(2, '0')}:${minute}`;
};
