import {CDN_DOMAIN_URL} from '@config/index.js';

/**
 * 拼接图片cdn地址
 * @param {string} imagePathStr
 * @returns {string}
 */
export const getImageUrl = imagePathStr => {
  const IMG_REGEX = /.\.(jpg|jpeg|gif|bmp|png)$/i;
  if (IMG_REGEX.test(imagePathStr)) {
    return `${CDN_DOMAIN_URL}/${imagePathStr}`;
  }
  return `${CDN_DOMAIN_URL}/${imagePathStr}.png`;
};

export const arrowRightImgBase64 =
  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAiCAYAAABfqvm9AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAADUSURBVHgB7dW9DYMwEAXgO1eUjMAIWSGjpGQiJBpGYYSswAj0CDs+KYUBY+6HkichZD/xSQYZAzyxBtNB3/ffEELtvX+3bTuBIm43nuPVOOfGrusaUGQDruv6ibfJguJ+ghDCCCVcunzMTVpQPCu0KJZKDVoENeglKEVZoARlg1xUBHJQB8LQw4RAsqPSXgxSqqqaEXHOdWJwGIZ6WZYx/pVe8F9y2oveYQ5TfxQOxga5GAuUYJegFCuCGuwU1GJZ0IIdQCtG2ewUK3YA6ZC3YE/uyQ+Kiho+geaRzgAAAABJRU5ErkJggg==';
