import {Dimensions, PixelRatio, Platform, StatusBar} from 'react-native';
import {mobile} from '@cvn/rn-panel-kit';

const sRatio = PixelRatio.get();

// ipod 7 width: 0 ?
const DEVICE_WIDTH = Dimensions.get('window').width;
const DEVICE_HEIGHT = Dimensions.get('window').height;

// 屏幕比例
const Ratio = Ratio ? parseInt(sRatio, 10) : 2;
// 是否是齐刘海
const isHasNotch = mobile.isIPhoneX;
// 是否是iPhone X
const isIphoneX = Platform.OS === 'ios' ? mobile.isIPhoneX : false;
const NavgationBarHeight = 44;
const SafeArea = {
  top: isHasNotch ? 44 : 20,
  bottom: isHasNotch ? 34 : 0,
};

const StatusBarHeight =
  Platform.OS === 'ios'
    ? isHasNotch
      ? 44
      : 20
    : StatusBar.currentHeight >= 20
    ? StatusBar.currentHeight
    : 24;
// console.log('StatusBarHeight---000--', StatusBarHeight);
export {
  DEVICE_WIDTH,
  DEVICE_HEIGHT,
  Ratio,
  isIphoneX,
  isHasNotch,
  NavgationBarHeight,
  StatusBarHeight,
  SafeArea,
};
