import {Dimensions, PixelRatio, Platform, StatusBar} from 'react-native';
import {mobile} from '@cvn/rn-panel-kit';

const sRatio = PixelRatio.get();
const dimen = Dimensions.get('window');

// ipod 7 width: 0 ?
const DEVICE_WIDTH = dimen.width;
const DEVICE_HEIGHT = dimen.height;

// 屏幕比例
const Ratio = Ratio ? parseInt(sRatio, 10) : 2;
// 是否是iPhone X
const isIphoneX = Platform.OS === 'ios' ? mobile.isIPhoneX : false;
// 14pro
// 宽高：393*852
// 14promax
// 宽高：430*932
// 428*926  12promax
const hasHole =
  (DEVICE_WIDTH === 393 && DEVICE_HEIGHT === 852) ||
  (DEVICE_WIDTH === 430 && DEVICE_HEIGHT === 932);
// ||
// // test
// (DEVICE_WIDTH === 428 && DEVICE_HEIGHT === 926);
const NavgationBarHeight = 44;
const SafeArea = {
  top: isIphoneX ? 44 : 20,
  bottom: isIphoneX ? 34 : 0,
};

const StatusBarHeight =
  Platform.OS === 'ios'
    ? isIphoneX
      ? 60
      : 20
    : StatusBar.currentHeight >= 20
    ? StatusBar.currentHeight
    : 24;
const scale = Dimensions.get('window').scale;
export {
  DEVICE_WIDTH,
  DEVICE_HEIGHT,
  Ratio,
  isIphoneX,
  NavgationBarHeight,
  StatusBarHeight,
  SafeArea,
  scale,
  hasHole,
};
