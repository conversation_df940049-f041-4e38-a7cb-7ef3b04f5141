/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-10-12 14:04:33
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-07-19 19:10:41
 * @FilePath: /61004/src/utils/log/index
 * @Description: 路由监听
 */
import {routeListener as rListener, LogUtils} from 'cvn-panel-kit/src/utils';
import {tracer, pageIdMap} from '../tracer';
import {NavigationState, PartialState} from '@react-navigation/native';

export const routeListener = (
  res:
    | Readonly<{
        key: string;
        index: number;
        routeNames: string[];
        history?: unknown[];
        routes: (Readonly<{key: string; name: string; path?: string}> &
          Readonly<{params?: Readonly<object | undefined>}> & {
            state?: NavigationState | PartialState<NavigationState>;
          })[];
        type: string;
        stale: false;
      }>
    | undefined,
) => {
  rListener(res, pageIdMap, () => {
    if (LogUtils.currentPage !== 'ViewBattery') {
      tracer.page();
    }
  });
};
