/**
 * secondToHour
 * @param {Number} second
 * @returns {Number}
 */
export const secondToHour = second => {
  return Number((second / 3600).toFixed(2));
};

/**
 * second to hour and min
 * @param {Number} second
 * @returns {Object}
 */
export const secondToHourAndMin = second => {
  return {
    hour: String(Math.floor(second / 3600)).padStart(2, '0'),
    min: String(Math.floor((second % 3600) / 60)).padStart(2, '0'),
  };
};

/**
 * minutes to hour and min
 * @param {number} minutes
 */
export const minutesToHourAndMin = minutes => {
  return {
    hour: String(Math.floor(minutes / 60)).padStart(2, '0'),
    min: String(Math.floor(minutes % 60)).padStart(2, '0'),
  };
};

/**
 * milliam value to value, like mAh to Ah
 * @param {Number} milliam
 * @returns {Number}
 */
export const milliamValueToValue = milliam => {
  return Number((milliam / 1000).toFixed(2));
};

/**
 * validate device name
 * @param {string} name
 * @returns {boolean}
 */
export const validateDeviceName = name => {
  return /^.{1,50}$/.test(name);
};
