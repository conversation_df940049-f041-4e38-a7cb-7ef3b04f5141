/**
 * 小端转大端
 * @param {string} _data
 * @returns {string}
 */
export const dataConversion = _data => {
  const data = _data.replace(/\s/g, '').replace(/(.{2})/g, '$1 ');
  return data.split(' ').reverse().join('');
};

/**
 * convent hex to string
 * @param {string} hex
 * @returns {string}
 */
export const hexToString = function (hex) {
  let arr = hex.split('');
  let out = '';
  for (let i = 0; i < arr.length / 2; i++) {
    let tmp = '0x' + arr[i * 2] + arr[i * 2 + 1];
    let charValue = String.fromCharCode(tmp);
    charValue = charValue.replace('\u0000', ''); // 去除空格
    out += charValue;
  }
  return out;
};

/**
 * @description: dpType
 * @param {String} dpId
 * @return {String}
 */
export const getDpType = dpId => {
  let result = '05';
  // 大端转小端
  return result;
};
/**
 * @description: dpLen
 * @param {Array} dpData
 * @return {String} hexString
 */
export const getDplen = dpData => {
  if (dpData.length === 0) {
    console.warn('dpData个数不正确');
    return 0;
  }
  let totalLen = 0;
  dpData.forEach(item => {
    // 蓝牙协议约定： param_id (2) + param_type (1) + param_data_len (2) + param_data_value 的字节长度
    totalLen = totalLen + 5 + item.param_data_value.length / 2; // 最终是字节长度
  });
  const result = numToHexString(totalLen, 4).toUpperCase();
  return result;
};

/**
 * convent number to hex string
 * @param {number|string} num
 * @param {number} padding
 * @returns {string}
 */
export const numToHexString = (num, padding = 2) => {
  const hex = Number(num).toString(16);
  return toFixed(hex, padding);
};

/**
 * fixed to count
 * @param {string} str
 * @param {number} count
 * @returns {string}
 */
const toFixed = (str, count) => {
  return `${'0'.repeat(count)}${str}`.slice(-1 * count);
};

/**
 * secondToHour
 * @param {Number} second
 * @returns {Number}
 */
export const secondToHour = second => {
  return Number((second / 3600).toFixed(2));
};

/**
 * @typedef {Object} HourAndMinute
 * @property {number} hour
 * @property {number} min
 */

/**
 * Converts seconds to hours and minutes.
 * @param {number} second - The number of seconds to convert.
 * @returns {HourAndMinute} An object with the hour and min properties.
 */
export const secondToHourAndMin = (second = 0) => {
  return {
    hour: Math.floor(second / 3600),
    min: Math.floor((second % 3600) / 60),
  };
};

/**
 * milliam value to value, like mAh to Ah
 * @param {Number} milliam
 * @returns {Number}
 */
export const milliamValueToValue = milliam => {
  return Number((milliam / 1000).toFixed(2));
};

/**
 * @param {number} hour
 * @param {number} minute
 * @param {string} hourUnit
 * @param {string} minuteUnit
 * @returns
 */
export const formatTimeByHourAndMinute = (
  hour,
  minute,
  hourUnit = 'hr',
  minuteUnit = 'min',
) => {
  return hour > 0 && minute > 0
    ? `${hour} ${hourUnit} ${minute} ${minuteUnit}`
    : hour > 0
    ? `${hour} ${hourUnit}`
    : minute > 0
    ? `${minute} ${minuteUnit}`
    : '--';
};

/**
 * validate device name
 * @param {string} name
 * @returns {boolean}
 */
export const validateDeviceName = name => {
  return /^.{1,50}$/.test(name);
};
