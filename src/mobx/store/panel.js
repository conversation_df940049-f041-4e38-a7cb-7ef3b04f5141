import {observable, computed} from 'mobx';
import ModelMap, {NO_ERROR_CODE} from '@config/model.js';
import {secondToHourAndMin, formatTimeByHourAndMinute} from '@utils/tools.js';

/**
 * @typedef {import('@config/types').DeviceDetailData} DeviceDetailData
 */

/**
 * @typedef {import('@config/types').InitialParamsData} InitialParamsData
 */

const {
  // property
  remaining_battery,
  battery_status,
  remaining_charging_time,

  battery_capacity,
  usage_history,

  total_charging_time,
  total_discharging_time,

  battery_health_condition,
  battery_cycle_count,

  fault_message,
} = ModelMap;

export default class PanelStore {
  /**
   * 初始化数据
   * @type {InitialParamsData}
   */
  @observable initialParams = {};

  /**
   * 设备详情
   * @type {DeviceDetailData}
   */
  @observable deviceDetail = {};

  /**
   * ota 升级
   */
  @observable showRed = false; // 升级小红点
  @observable otaVersion = ''; // 升级版本

  /**
   * ble 数据返回统一处理，首页用的数据
   */
  @observable resWithBle = {
    ...this.defaultRes,
  };

  /**
   * 蓝牙是否已经连接
   */
  @observable bleConnected = false;

  /**
   * 调用了原生pop操作标识
   */
  @observable isNativeGoback = false;

  /**
   * 是否需要强制升级
   */
  @observable isForceUpdate = false;

  /**
   * 是否在 RN 容器中
   */
  @observable isInRNContainerVC = true;
  /**
   * 首页故障数据源
   */
  @observable faultInfo = {};

  /**
   * 设备ID，设备的识别码
   * @returns {string}
   */
  @computed get deviceId() {
    const {deviceId} = this.initialParams;
    return deviceId;
  }
  /**
   * appType, ios || android
   * @returns {string}
   */
  @computed get appType() {
    const {appType} = this.initialParams;
    return appType;
  }
  /**
   * appVersion, 当前app版本号(原生)
   * @returns {string}
   */
  @computed get appVersion() {
    const {appVersion} = this.initialParams;
    return appVersion;
  }

  /**
   * 设备 mac 地址
   * @returns {string}
   */
  @computed get mac() {
    const {mac} = this.initialParams;
    return mac;
  }

  /**
   * 当前环境 sit/pre/prd
   * @returns {'SIT'|'PRE'|'PRD'}
   */
  @computed get env() {
    const {env} = this.initialParams;
    return env;
  }

  /**
   * 地区 NA/EU
   * NA:北美  EU:欧洲
   * @returns {'NA'|'EU'}
   */
  @computed get region() {
    const {region} = this.initialParams;
    return region;
  }

  /**
   * 设备名称，优先取接口查出来的名称，如果没有则取原生传递的初始名称
   * @returns {string}
   */
  @computed get deviceName() {
    const {deviceName: initialDeviceName} = this.initialParams;
    const {nickName = '', deviceName = ''} = this.deviceDetail;

    return nickName || deviceName || initialDeviceName;
  }

  /**
   * PID，开发平台创建的每一个产品都会产生一个唯一的产品编号
   * @returns {string}
   */
  @computed get productId() {
    let {productId} = this.initialParams;
    if (!productId || productId.length === 0) {
      productId = this.deviceDetail?.productId;
    }
    return productId;
  }

  /**
   * app 小时制设置
   * 12小时制 ｜ 24小时制
   * @returns {'12hours'|'24hours'}
   */
  @computed get appSettingOfHour() {
    const {appSettingOfHour} = this.initialParams;
    if (appSettingOfHour) {
      return appSettingOfHour;
    }
    return this.region === 'NA' ? '12hours' : '24hours';
  }

  /**
   * app 单位制设置
   * 公制: metric 英制: imperial
   * @returns {'metric'|'imperial'}
   */
  @computed get appSettingOfUnit() {
    const {appSettingOfUnit} = this.initialParams;
    if (appSettingOfUnit) {
      return appSettingOfUnit;
    }
    return this.region === 'NA' ? 'imperial' : 'metric';
  }

  /**
   * app 日期制设置
   * dd-mm-yyyy | mm-dd-yyyy
   * @returns {'dayMonthYear'|'monthDayYear'}
   */
  @computed get appSettingOfDate() {
    const {appSettingOfDate} = this.initialParams;
    if (appSettingOfDate) {
      return appSettingOfDate;
    }
    return this.region === 'NA' ? 'monthDayYear' : 'dayMonthYear';
  }

  /**
   * 月使用记录
   */
  @computed get usageData() {
    const {resWithBle: res} = this;
    return res[usage_history];
  }

  /**
   * 是否无故障
   */
  @computed get hasNoError() {
    const {resWithBle: res} = this;
    const code = res[fault_message];
    return code === NO_ERROR_CODE;
  }

  /**
   * home首页使用到的数据，包括 电池容量百分比、剩余容量、总使用时长等
   */
  @computed get home() {
    const {resWithBle: res} = this;
    const remainingBattery = res[remaining_battery];
    const batteryStatus = res[battery_status];
    const batteryCapacity = res[battery_capacity];

    const _totalDischargingTime = res[total_discharging_time];
    const {hour: dischargingHour, min: dischargingMin} =
      _totalDischargingTime > 0
        ? secondToHourAndMin(_totalDischargingTime)
        : {
            hour: _totalDischargingTime,
            min: _totalDischargingTime,
          };

    const _remainingChargingTime = res[remaining_charging_time];
    const remainingChargingTime =
      _remainingChargingTime > 0
        ? secondToHourAndMin(_remainingChargingTime)
        : {
            hour: _remainingChargingTime,
            min: _remainingChargingTime,
          };

    return {
      remainingBattery,
      batteryStatus,
      remainingChargingTime,
      batteryCapacity,
      totalDischargingTime: formatTimeByHourAndMinute(
        dischargingHour,
        dischargingMin,
      ),
    };
  }

  @computed get usageStatistics() {
    const {resWithBle: res} = this;
    const _totalChargingTime = res[total_charging_time];
    const _totalDischargingTime = res[total_discharging_time];
    const batteryHealthCondition = res[battery_health_condition];
    const batteryCycleCount = res[battery_cycle_count];

    const {hour: chargingHour, min: chargingMin} =
      _totalChargingTime > 0
        ? secondToHourAndMin(_totalChargingTime)
        : {
            hour: _totalChargingTime,
            min: _totalChargingTime,
          };

    const {hour: dischargingHour, min: dischargingMin} =
      _totalDischargingTime > 0
        ? secondToHourAndMin(_totalDischargingTime)
        : {
            hour: _totalDischargingTime,
            min: _totalDischargingTime,
          };

    return {
      totalChargingTime: formatTimeByHourAndMinute(chargingHour, chargingMin),
      totalDischargingTime: formatTimeByHourAndMinute(
        dischargingHour,
        dischargingMin,
      ),
      batteryHealthCondition,
      batteryCycleCount,
    };
  }

  /**
   * 默认显示数据
   */
  defaultRes = {
    // property
    [remaining_battery]: -1,
    [battery_status]: -1, // default for empty
    [remaining_charging_time]: -1,

    [battery_capacity]: 40, // 一般固定为40Ah，为了保证后续更新电池容量，绑定此物模型数据
    [usage_history]: [],

    [total_charging_time]: -1,
    [total_discharging_time]: -1,

    [battery_health_condition]: -1,
    [battery_cycle_count]: -1,

    [fault_message]: NO_ERROR_CODE,
  };
}
