import {observable, computed} from 'mobx';
import ModelMap, {NO_ERROR_CODE} from '@config/model.js';
import {timestampToTimeStr, convertTo12HourFormat} from '@utils/time.js';
import {minutesToHourAndMin} from '@utils/tools.js';

/**
 * @typedef {import('@config/types').MessageData} MessageData
 */

/**
 * @typedef {import('@config/types').OtaResult} OtaResult
 */

/**
 * @typedef {import('@config/types').InitialParamsData} InitialParamsData
 */

/**
 * @typedef {import('@config/types').DeviceDetailData} DeviceDetailData
 */

const {
  // property
  charging_progress,
  charging_status,
  remaining_charging_time,
  ready_batterys_power,
  ready_batterys_count,
  charging_batterys_count,
  standby_batterys_count,
  empty_batterys_count,
  three_port_adaptors_count,
  dc_dc_chargers_count,
  total_charging_time,

  // service
  schedule_charging_switch,
  completion_reminder_switch,
  dc_dc_priority_charging_switch,
  optimized_battery_charging_switch,

  schedule_charging_start_time,
  completion_reminder_value,

  // event
  error_status_code,
} = ModelMap;

export default class PanelStore {
  /**
   * 初始化数据
   * @type {InitialParamsData}
   */
  @observable initialParams = {};

  /**
   * 设备详情
   * @type {DeviceDetailData}
   */
  @observable deviceDetail = {};

  /**
   * wifi 连接状态
   * @type {boolean|undefined}
   */
  @observable isWifiConnected = undefined;

  /**
   * ota 升级结果
   * @type {OtaResult}
   */
  @observable otaResult = {};

  /**
   * 是否在RN容器内
   * @type {boolean}
   */
  @observable isInRNContainerVC = true;

  /**
   * 控制 offline popup 只弹一次
   * @type {boolean}
   */
  @observable isOfflinePopupPoped = false;

  /**
   * powerhub 物模型数据
   * @type {object}
   */
  @observable hubModelData = {
    ...this.defaultModelData,
  };
  /**
   * 首页故障数据源
   */
  @observable faultInfo = {};

  /**
   * topic 返回的时间戳
   */
  connectChangeTimestamp = undefined;

  /**
   * 升级小红点
   * @type {boolean}
   */
  @computed get showRed() {
    return this.otaResult?.showRed || false;
  }

  /**
   * 设备ID，设备的识别码
   * @returns {string}
   */
  @computed get deviceId() {
    return this.initialParams?.deviceId;
  }

  /**
   * 设备 mac 地址
   * @returns {string}
   */
  @computed get mac() {
    const {mac} = this.initialParams;
    return mac;
  }

  /**
   * 地区 NA/EU
   * NA:北美  EU:欧洲
   * @returns {'NA'|'EU'}
   */
  @computed get region() {
    const {region} = this.initialParams;
    return region;
  }

  /**
   * 设备名称，优先取接口查出来的名称，如果没有则取原生传递的初始名称
   * @returns {string}
   */
  @computed get deviceName() {
    const {deviceName: initialDeviceName} = this.initialParams;
    const {deviceName} = this.deviceDetail;

    return deviceName || initialDeviceName;
  }

  /**
   * 设备持续离线天数
   * @returns {number}
   */
  @computed get deviceOfflineDays() {
    return Number(this.deviceDetail?.offlineDays) || 0;
  }

  /**
   * PID，开发平台创建的每一个产品都会产生一个唯一的产品编号
   * @returns {string}
   */
  @computed get productId() {
    let {productId} = this.initialParams;
    if (!productId || productId.length === 0) {
      productId = this.deviceDetail?.productId;
    }
    return productId;
  }

  /**
   * app 小时制设置
   * 12小时制 ｜ 24小时制
   * @returns {'12hours'|'24hours'}
   */
  @computed get appSettingOfHour() {
    const {appSettingOfHour} = this.initialParams;
    if (appSettingOfHour) {
      return appSettingOfHour;
    }
    return this.region === 'NA' ? '12hours' : '24hours';
  }

  /**
   * app 单位制设置
   * 公制: metric 英制: imperial
   * @returns {'metric'|'imperial'}
   */
  @computed get appSettingOfUnit() {
    const {appSettingOfUnit} = this.initialParams;
    if (appSettingOfUnit) {
      return appSettingOfUnit;
    }
    return this.region === 'NA' ? 'imperial' : 'metric';
  }

  /**
   * app 日期制设置
   * dd-mm-yyyy | mm-dd-yyyy
   * @returns {'dayMonthYear'|'monthDayYear'}
   */
  @computed get appSettingOfDate() {
    const {appSettingOfDate} = this.initialParams;
    if (appSettingOfDate) {
      return appSettingOfDate;
    }
    return this.region === 'NA' ? 'monthDayYear' : 'dayMonthYear';
  }

  /**
   * 是否无故障
   * 如果是 undefined，可以认为是初始值，表现为无故障
   * @returns {boolean}
   */
  @computed get hasNoError() {
    const {hubModelData: res} = this;
    const code = res[error_status_code];
    return code === NO_ERROR_CODE;
  }

  /**
   * @typedef {Object} HomeData
   * @property {number} chargingProgress
   * @property {number} chargingStatus
   * @property {number} remainingChargingTime
   * @property {number} readyBatterysPower
   * @property {number} readyBatterysCount
   * @property {number} chargingBatterysCount
   * @property {number} stanbyBatterysCount
   * @property {number} emptyBatterysCount
   * @property {number} totalBatterysCount
   * @property {number} threePortAdaptorCount
   * @property {number} dcDcChargerCount
   */

  /**
   * home首页使用到的数据，包括 power-hub 总电量、充电状态，剩余充电时间、ready 状态的电池电量、ready 状态充电电池数量、充电中电池数量、待机电池数量、空电池数量、子设备列表
   * @returns {HomeData}
   */
  @computed get home() {
    const {hubModelData: res} = this;
    const chargingProgress = res[charging_progress];
    const chargingStatus = res[charging_status];
    const _remainingChargingTime = res[remaining_charging_time];
    const readyBatterysPower = res[ready_batterys_power];
    const readyBatterysCount = res[ready_batterys_count];
    const chargingBatterysCount = res[charging_batterys_count];
    const stanbyBatterysCount = res[standby_batterys_count];
    const emptyBatterysCount = res[empty_batterys_count];
    const totalBatterysCount =
      readyBatterysCount + chargingBatterysCount + stanbyBatterysCount;

    const threePortAdaptorCount = res[three_port_adaptors_count];
    const dcDcChargerCount = res[dc_dc_chargers_count];

    const remainingChargingTime =
      _remainingChargingTime > 0
        ? minutesToHourAndMin(_remainingChargingTime)
        : {hour: '--', min: '--'};

    return {
      chargingProgress,
      chargingStatus,
      remainingChargingTime,
      readyBatterysPower,
      readyBatterysCount,
      chargingBatterysCount,
      stanbyBatterysCount,
      emptyBatterysCount,
      totalBatterysCount,

      threePortAdaptorCount,
      dcDcChargerCount,
    };
  }

  /**
   * 首页Function Setting用到的状态
   */
  @computed get setting() {
    const {hubModelData: res} = this;
    const scheduleChargingSwitch = res[schedule_charging_switch];
    const completionReminderSwitch = res[completion_reminder_switch];
    const dcDcPrioritySwitch = res[dc_dc_priority_charging_switch];
    const optimizedBatteryChargingSwitch =
      res[optimized_battery_charging_switch];

    const _chargingScheduleStartTime = res[schedule_charging_start_time];

    const chargingScheduleStartTimeIn24HourFormat =
      scheduleChargingSwitch && _chargingScheduleStartTime
        ? timestampToTimeStr(_chargingScheduleStartTime)
        : '';
    const completionReminderValue = res[completion_reminder_value];

    const is12HourFormat = this.appSettingOfHour === '12hours';

    return {
      scheduleChargingSwitch,
      completionReminderSwitch,
      dcDcPrioritySwitch,
      optimizedBatteryChargingSwitch,
      chargingScheduleStartTime: is12HourFormat
        ? convertTo12HourFormat(chargingScheduleStartTimeIn24HourFormat)
        : chargingScheduleStartTimeIn24HourFormat,
      completionReminderValue,
    };
  }

  /**
   * 使用统计数据
   */
  @computed get usageStatistics() {
    const {hubModelData: res} = this;
    const totalChargingTime = res[total_charging_time];

    return {
      totalChargingTime,
    };
  }

  /**
   * 默认物模型数据
   */
  defaultModelData = {
    // property
    [charging_progress]: 0,
    [charging_status]: 0,
    [remaining_charging_time]: 0,
    [ready_batterys_power]: 0,
    [ready_batterys_count]: 0,
    [charging_batterys_count]: 0,
    [standby_batterys_count]: 0,
    [empty_batterys_count]: 0,
    [three_port_adaptors_count]: 0,
    [dc_dc_chargers_count]: 0,

    [total_charging_time]: 0,

    // service
    [schedule_charging_switch]: false,
    [completion_reminder_switch]: false,
    [dc_dc_priority_charging_switch]: false,
    [optimized_battery_charging_switch]: false,

    [schedule_charging_start_time]: 0,
    [completion_reminder_value]: 0,

    // event
    [error_status_code]: undefined,
  };
}
