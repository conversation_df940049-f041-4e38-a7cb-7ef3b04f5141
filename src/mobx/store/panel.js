import {observable, computed} from 'mobx';
import ModelMap, {NO_ERROR_CODE} from '@config/model.js';
import {
  secondToHour,
  secondToHourAndMinString,
  milliamValueToValue,
} from '@utils/tools.js';

/**
 * @typedef {import('@config/types').DeviceDetailData} DeviceDetailData
 */

/**
 * @typedef {import('@config/types').InitialParamsData} InitialParamsData
 */

const {
  // property
  working_status,
  ac_charging_progress,
  dc_charging_progress,
  ac_charging_remaining_time,
  remaining_battery_of_batteries,
  rated_capacity_of_batteries,
  dc_charging_remaining_time_of_batteries,
  total_ac_charging_time,
  total_dc_charging_time,
  total_charging_energy,
  total_discharging_energy,
  // past_week_charging_energy_list,
  // past_year_charging_energy_list,
  // past_week_discharging_energy_list,
  // past_year_discharging_energy_list,
  // 仓位状态
  lfp_battery_status,
  first_portable_battery_status,
  second_portable_battery_status,

  // service
  dc_dc_mode_switch,
  customize_charging_sequence,
  battery_optimization_switch,

  fault_message,
} = ModelMap;

export default class PanelStore {
  /**
   * 初始化数据
   * @type {InitialParamsData}
   */
  @observable initialParams = {};

  /**
   * 设备详情
   * @type {DeviceDetailData}
   */
  @observable deviceDetail = {};

  /**
   * ota 升级
   */
  @observable showRed = false; // 升级小红点
  @observable otaVersion = ''; // 升级版本

  /**
   * ble 数据返回统一处理，首页用的数据
   */
  @observable resWithBle = {
    ...this.defaultRes,
  };

  /**
   * 蓝牙是否已经连接
   */
  @observable bleConnected = false;

  /**
   * 调用了原生pop操作标识
   */
  @observable isNativeGoback = false;

  /**
   * 是否需要强制升级
   */
  @observable isForceUpdate = false;

  /**
   * 是否在 RN 容器中
   */
  @observable isInRNContainerVC = true;
  // 故障消息
  @observable faultInfo = {};

  /**
   * 设备ID，设备的识别码
   * @returns {string}
   */
  @computed get deviceId() {
    const {deviceId} = this.initialParams;
    return deviceId;
  }

  /**
   * 设备 mac 地址
   * @returns {string}
   */
  @computed get mac() {
    const {mac} = this.initialParams;
    return mac;
  }

  /**
   * 当前环境 sit/pre/prd
   * @returns {'SIT'|'PRE'|'PRD'}
   */
  @computed get env() {
    const {env} = this.initialParams;
    return env;
  }

  /**
   * 地区 NA/EU
   * NA:北美  EU:欧洲
   * @returns {'NA'|'EU'}
   */
  @computed get region() {
    const {region} = this.initialParams;
    return region;
  }

  /**
   * 设备名称，优先取接口查出来的名称，如果没有则取原生传递的初始名称
   * @returns {string}
   */
  @computed get deviceName() {
    const {deviceName: initialDeviceName} = this.initialParams;
    const {nickName = '', deviceName = ''} = this.deviceDetail;

    return nickName || deviceName || initialDeviceName;
  }

  /**
   * PID，开发平台创建的每一个产品都会产生一个唯一的产品编号
   * @returns {string}
   */
  @computed get productId() {
    let {productId} = this.initialParams;
    if (!productId || productId.length === 0) {
      productId = this.deviceDetail?.productId;
    }
    return productId;
  }
  /**
   * appType, ios || android
   * @returns {string}
   */
  @computed get appType() {
    const {appType} = this.initialParams;
    return appType;
  }
  /**
   * appVersion, 当前app版本号(原生)
   * @returns {string}
   */
  @computed get appVersion() {
    const {appVersion} = this.initialParams;
    return appVersion;
  }

  /**
   * app 小时制设置
   * 12小时制 ｜ 24小时制
   * @returns {'12hours'|'24hours'}
   */
  @computed get appSettingOfHour() {
    const {appSettingOfHour} = this.initialParams;
    if (appSettingOfHour) {
      return appSettingOfHour;
    }
    return this.region === 'NA' ? '12hours' : '24hours';
  }

  /**
   * app 单位制设置
   * 公制: metric 英制: imperial
   * @returns {'metric'|'imperial'}
   */
  @computed get appSettingOfUnit() {
    const {appSettingOfUnit} = this.initialParams;
    if (appSettingOfUnit) {
      return appSettingOfUnit;
    }
    return this.region === 'NA' ? 'imperial' : 'metric';
  }

  /**
   * app 日期制设置
   * dd-mm-yyyy | mm-dd-yyyy
   * @returns {'dayMonthYear'|'monthDayYear'}
   */
  @computed get appSettingOfDate() {
    const {appSettingOfDate} = this.initialParams;
    if (appSettingOfDate) {
      return appSettingOfDate;
    }
    return this.region === 'NA' ? 'monthDayYear' : 'dayMonthYear';
  }

  /**
   * 子设备的 deviceId（挂载的两个小包）
   */
  @computed get subDeviceIds() {
    const {resWithBle: res} = this;
    const firstPortableDeviceId = res[first_portable_battery_status]?.deviceId;
    const secondPortableDeviceId =
      res[second_portable_battery_status]?.deviceId;

    return [firstPortableDeviceId, secondPortableDeviceId];
  }

  // ota升级页，主设备card数据源
  @computed get updateDeviceData() {
    const {otaResultArray, deviceDetail} = this;
    const {version, deviceName} = deviceDetail;
    const result =
      otaResultArray.find(item => item.deviceId === this.deviceId) || {};
    result.version = version;
    result.deviceName = deviceName;
    console.log('updateDeviceData-result--', JSON.stringify(result));
    return result;
  }

  @computed get subsetDevicesDetailArray() {
    const {otaUpdateViewValues: values, otaResultArray} = this;

    // 处理子设备数据源，组装成 view需要的数据
    const resultArray = values.map(item => {
      const {entry} = item;
      let result = {...entry};
      otaResultArray.forEach(otaObj => {
        if (otaObj.deviceId === result.deviceId) {
          result = {...result, ...otaObj};
        }
      });
      return result;
    });
    console.log('subsetDevicesDetailArray---', JSON.stringify(resultArray));
    return resultArray;
  }

  /**
   * 是否无故障
   */
  @computed get hasNoError() {
    const {resWithBle: res} = this;
    const code = res[fault_message];
    return code === NO_ERROR_CODE;
  }

  /**
   * home首页使用到的数据，包括 电池容量百分比、剩余容量、总使用时长等
   */
  @computed get home() {
    const {resWithBle: res} = this;
    // console.log('home:', res);
    /**
     * @type {number}
     */
    const workingStatus = res[working_status];
    const acChargingProgress = res[ac_charging_progress];
    const dcChargingProgress = res[dc_charging_progress];
    const _acChargingRemainingTime = res[ac_charging_remaining_time];
    const remainingBatteryOfBatteries = res[remaining_battery_of_batteries];
    const ratedCapacityOfBatteries = res[rated_capacity_of_batteries];
    const dcChargingRemainingTimeOfBatteries = res[
      dc_charging_remaining_time_of_batteries
    ].map(item => {
      const {hour, min} =
        item > 0 ? secondToHourAndMinString(item) : {hour: '--', min: '--'};
      return `${hour}h ${min}min`;
    });

    // 根据上报的电池现有容量和额定容量动态计算standby电量百分比
    let standbyChargingProgress = 0;
    const ratedCapacity = ratedCapacityOfBatteries.reduce(
      (acc, cur) => acc + cur,
      0,
    );
    if (ratedCapacity === 0) {
      // to avoid NaN, cuz */0 is NaN
      standbyChargingProgress = 0;
    } else {
      const remainingCapacity = remainingBatteryOfBatteries.reduce(
        (acc, cur) => acc + cur,
        0,
      );
      standbyChargingProgress = Math.round(
        (remainingCapacity / ratedCapacity) * 100,
      );
    }

    const lfpBatteryStatus = res[lfp_battery_status];
    const firstPortableBatteryStatus = res[first_portable_battery_status];
    const secondPortableBatteryStatus = res[second_portable_battery_status];

    const acChargingRemainingTime =
      _acChargingRemainingTime > 0
        ? secondToHourAndMinString(_acChargingRemainingTime)
        : {hour: '--', min: '--'};

    return {
      workingStatus,
      acChargingProgress,
      dcChargingProgress,
      standbyChargingProgress,
      acChargingRemainingTime,
      remainingBatteryOfBatteries,
      ratedCapacityOfBatteries,
      dcChargingRemainingTimeOfBatteries,
      lfpBatteryStatus,
      firstPortableBatteryStatus,
      secondPortableBatteryStatus,
    };
  }

  @computed get functionSetting() {
    const {resWithBle: res} = this;
    const dcDcModeSwitch = res[dc_dc_mode_switch];
    const customizeChargingSequence = res[customize_charging_sequence];
    const batteryOptimizationSwitch = res[battery_optimization_switch];

    return {
      dcDcModeSwitch: dcDcModeSwitch === 1,
      customizeChargingSequence,
      batteryOptimizationSwitch: batteryOptimizationSwitch === 1,
    };
  }

  /**
   * usage statistics，使用统计页面要用的统计数据
   */
  @computed get usageStatistics() {
    const {resWithBle: res} = this;
    const _totalAcChargingTime = res[total_ac_charging_time];
    const _totalDcChargingTime = res[total_dc_charging_time];
    const _totalChargingEnergy = res[total_charging_energy];
    const _totalDischargingEnergy = res[total_discharging_energy];

    // const pastWeekChargingEnergyList = res[past_week_charging_energy_list];
    // const pastWeekDischargingEnergyList = res[past_week_discharging_energy_list];
    // const pastYearChargingEnergyList = res[past_year_charging_energy_list];
    // const pastYearDischargingEnergyList = res[past_year_discharging_energy_list];

    const totalAcChargingTime =
      _totalAcChargingTime > 0
        ? secondToHour(_totalAcChargingTime)
        : _totalAcChargingTime; // second to hour
    const totalDcChargingTime =
      _totalDcChargingTime > 0
        ? secondToHour(_totalDcChargingTime)
        : _totalDcChargingTime; // second to hour

    const totalChargingEnergy =
      _totalChargingEnergy > 0
        ? milliamValueToValue(_totalChargingEnergy)
        : _totalChargingEnergy;
    const totalDischargingEnergy =
      _totalDischargingEnergy > 0
        ? milliamValueToValue(_totalDischargingEnergy)
        : _totalDischargingEnergy;

    return {
      totalAcChargingTime,
      totalDcChargingTime,
      totalChargingEnergy,
      totalDischargingEnergy,

      // pastWeekChargingEnergyList,
      // pastYearChargingEnergyList,

      // pastWeekDischargingEnergyList,
      // pastYearDischargingEnergyList,
    };
  }

  /**
   * 默认显示数据
   * 由于移除子设备ota功能，两个小包电池的 deviceId 这里暂时忽略，
   */
  defaultRes = {
    // property
    [working_status]: 0,
    [ac_charging_progress]: 0,
    [dc_charging_progress]: 0,
    [ac_charging_remaining_time]: 0,
    [remaining_battery_of_batteries]: [0, 0, 0],
    [rated_capacity_of_batteries]: [40000, 5000, 5000],
    [dc_charging_remaining_time_of_batteries]: [0, 0],
    // prperty for statistics
    [total_ac_charging_time]: -1,
    [total_dc_charging_time]: -1,
    [total_charging_energy]: -1,
    [total_discharging_energy]: -1,
    // [past_week_charging_energy_list]: [],
    // [past_week_discharging_energy_list]: [],
    // [past_year_charging_energy_list]: [],
    // [past_year_discharging_energy_list]: [],

    [lfp_battery_status]: 0,
    [first_portable_battery_status]: {
      batteryStatus: observable.box(0),
      deviceId: observable.box(''),
    },
    [second_portable_battery_status]: {
      batteryStatus: observable.box(0),
      deviceId: observable.box(''),
    },

    // service
    [dc_dc_mode_switch]: 0,
    [customize_charging_sequence]: 0,
    [battery_optimization_switch]: 0,

    [fault_message]: undefined,
  };
}
