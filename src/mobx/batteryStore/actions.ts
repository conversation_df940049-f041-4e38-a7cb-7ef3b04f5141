import {computed} from 'mobx';
import BatteryManagementState from './state';
import ModelMap from '@utils/model';
import {RootStore} from '../rootStore';
import {propertyMap, hexPropertyMap} from '../../utils/model';
import {StringUtils} from 'cvn-panel-kit/src/utils';
import logger from '../../utils/logger';
import {
  BatteryStatus,
  ChargeStatus,
  DeviceWorkStatus,
  BatteryErrorStatus,
  BatteryDetailInfo,
  TotalChargeInfo,
  BATTERY_CONSTANTS,
  BluetoothBatteryDataItem,
  DATA_PARSING_CONSTANTS,
  REGEX_PATTERNS,
  TIME_CONSTANTS,
} from '../../types';
import {safeJsonParse} from '../../utils/jsonParser';
// 业务操作

export default class BatteryManagementActions {
  state: BatteryManagementState;
  rootStore: RootStore;
  constructor(state: BatteryManagementState, rootStore: RootStore) {
    this.state = state;
    this.rootStore = rootStore;
  }
  /**
   * 电池详情数据源
   */
  @computed
  get batteryDetailArray(): BatteryDetailInfo[] {
    const batteryLevel = this.state.deviceBatteryLevelList;
    const batteryStatus = this.state.deviceBatteryStatusList;
    const batteryInfoList: BatteryDetailInfo[] = batteryLevel.map(
      (battery, index) => {
        return {
          didHave: battery !== BATTERY_CONSTANTS.EMPTY_BATTERY_VALUE,
          value:
            battery === BATTERY_CONSTANTS.EMPTY_BATTERY_VALUE
              ? battery
              : Number(battery),
          isError: Number(batteryStatus[index]) === BatteryStatus.ERROR,
          overTemp: Number(batteryStatus[index]) === BatteryStatus.HOT,
        };
      },
    );
    if (batteryInfoList.length < BATTERY_CONSTANTS.REQUIRED_BATTERY_COUNT) {
      return [];
    }
    return batteryInfoList;
  }

  /**
   * 电池详情数据源 封装一层 data
   */
  @computed get batteryDetail() {
    const result = {
      data: this.batteryDetailArray,
    };
    return result;
  }

  // 获取电池百分比，确保在有效范围内
  @computed get getBatteryPercentage() {
    const {battery_percentage} = ModelMap;
    const batteryValue =
      this.rootStore.deviceConnectionStore.state.resWithNew[battery_percentage];
    const batteryPercentage = Number.isNaN(Number(batteryValue))
      ? 0
      : this.clampPercentage(Number(batteryValue));
    return batteryPercentage;
  }

  // ✅ 私有方法：辅助功能
  private clampPercentage(percentage: number): number {
    return Math.min(
      Math.max(percentage, BATTERY_CONSTANTS.MIN_BATTERY_PERCENTAGE),
      BATTERY_CONSTANTS.MAX_BATTERY_PERCENTAGE,
    );
  }

  // 电池仓位是否为空
  @computed get dcStorageIsEmpty() {
    const {dc_dc_storage_battery} = ModelMap;
    const result =
      Number(
        this.rootStore.deviceConnectionStore.state.resWithNew[
          dc_dc_storage_battery
        ] ?? BATTERY_CONSTANTS.MIN_BATTERY_PERCENTAGE,
      ) === BATTERY_CONSTANTS.EMPTY_BATTERY_VALUE;
    return result;
  }

  /**
   * @description: 设置 电池仓电量
   * @param {Boolean} value
   */
  setDeviceBatteryLevelList = (value: number[]) => {
    this.state.deviceBatteryLevelList = value;
  };

  /**
   * @description: 设置 电池仓状态
   * @param {Boolean} value
   */
  setDeviceBatteryStatusList = (value: number[]) => {
    this.state.deviceBatteryStatusList = value;
  };

  /**
   * @description: 设置DC-DC是否在充电
   * @param {Boolean} dcStatus
   */
  setDCDCChargingState = (dcStatus: boolean) => {
    this.state.DCBatteryCharging = dcStatus;
  };

  /**
   * @description: 设置DC-DC充电剩余时间
   * @param {int} remainTime
   */
  setDCDCChargeRemainTime = (remainTime: number) => {
    this.state.DCBatteryChargeRemainTime = remainTime;
  };

  /**
   * 电池仓位页面-整车充电相关聚合数据源
   */
  @computed get totalBatteryChargeSource(): TotalChargeInfo {
    const {full_vehicle_remaining_time} = ModelMap;
    const {ztrWorkStatus} = this;
    let isCharging = false;
    const remainChargeTime = Number(
      this.rootStore.deviceConnectionStore.state.resWithNew[
        full_vehicle_remaining_time
      ] ?? BATTERY_CONSTANTS.MIN_BATTERY_PERCENTAGE,
    ); // 分钟
    if (ztrWorkStatus === DeviceWorkStatus.CHARGING) {
      isCharging = true;
    }
    const totalRemainTimeHour =
      Math.floor(remainChargeTime / TIME_CONSTANTS.MINUTES_PER_HOUR) ===
      BATTERY_CONSTANTS.MIN_BATTERY_PERCENTAGE
        ? undefined
        : String(
            Math.floor(remainChargeTime / TIME_CONSTANTS.MINUTES_PER_HOUR),
          ).padStart(
            TIME_CONSTANTS.TIME_STRING_LENGTH,
            TIME_CONSTANTS.TIME_PAD_CHAR,
          );
    const totalRemainTimeMinute = String(
      remainChargeTime % TIME_CONSTANTS.MINUTES_PER_HOUR,
    ).padStart(TIME_CONSTANTS.TIME_STRING_LENGTH, TIME_CONSTANTS.TIME_PAD_CHAR);
    return {
      totalRemainTimeHour,
      totalRemainTimeMinute,
      isTotalCharging: isCharging,
    };
  }

  /**
   * 获取电池充电状态
      4.16日修改，描述更新了
      0：保留
      1：充电中
      3：充满
      5：充电等待中（EMS不允许充电）
      6：过温
      7：充电故障
      8：空仓
   */
  @computed get batteryIsCharging() {
    const {battery_charge_status} = ModelMap;
    const statusNum = Number(
      this.rootStore.deviceConnectionStore.state.resWithNew[
        battery_charge_status
      ] ?? ChargeStatus.RESERVED,
    );
    const result =
      statusNum !== ChargeStatus.RESERVED && statusNum !== ChargeStatus.EMPTY;
    return result;
  }
  /**
   * DC在仓&&电池在充电
   */
  @computed get DCInAndBatteryIsCharging() {
    const {batteryIsCharging, dcStorageIsEmpty} = this;
    const result = !dcStorageIsEmpty && batteryIsCharging;
    return result;
  }

  /**
   * 首页电池状态
   * normal: 正常  0
   * hot: 过热  1
   * error: 故障  2
   */
  @computed get batteryErrorStatus(): BatteryErrorStatus {
    const {all_battery_status} = ModelMap;
    const batteryStatusValue = Number(
      this.rootStore.deviceConnectionStore.state.resWithNew[all_battery_status],
    );
    let result: BatteryErrorStatus = 'normal';
    if (batteryStatusValue === BatteryStatus.HOT) {
      result = 'hot';
    }
    if (batteryStatusValue === BatteryStatus.ERROR) {
      result = 'error';
    }
    // 如果两种都存在，以错误为主
    if (batteryStatusValue === BatteryStatus.COMBINED_ERROR) {
      result = 'error';
    }
    return result;
  }
  /**
   *  * 0 - 空闲、1 - 自走、2 - 割草、3 - 充电、4 - 移动
   */
  @computed get ztrWorkStatus() {
    const {ztr_work_status} = ModelMap;
    const result = Number(
      this.rootStore.deviceConnectionStore.state.resWithNew[ztr_work_status] ??
        DeviceWorkStatus.IDLE,
    );
    return result;
  }

  // 电池详情页显示 电池充电状态与否
  @computed get batteryIsShowStatus() {
    const {ztrWorkStatus, DCInAndBatteryIsCharging} = this;

    // 5004 物模型,和home中 showStatus 判断类似
    if (ztrWorkStatus === DeviceWorkStatus.MOWING) {
      return false;
    } else if (
      ztrWorkStatus === DeviceWorkStatus.CHARGING ||
      DCInAndBatteryIsCharging
    ) {
      return true;
    } else {
      return false;
    }
  }

  /**
   * 蓝牙wifi信息解析
   */
  parseBlueToothWifiData = (cmdResponse: string[]) => {
    const cmdData = cmdResponse.slice(
      DATA_PARSING_CONSTANTS.BLUETOOTH.WIFI_CMD_START_INDEX,
      DATA_PARSING_CONSTANTS.BLUETOOTH.WIFI_CMD_END_OFFSET,
    );
    const data = cmdData.slice(
      DATA_PARSING_CONSTANTS.VALIDATION.INITIAL_COUNT,
      DATA_PARSING_CONSTANTS.BLUETOOTH.WIFI_DATA_LENGTH,
    );
    const wifiName = StringUtils.hexToString(data) ?? '';
    this.rootStore.deviceStore.actions.setWifiName(wifiName);
  };

  /**
   * 蓝牙通道电池数据相关解析
   * @param blueData - 蓝牙数据字符串
   */
  parseBlueToothBatteryData = (blueData: string): void => {
    // 输入验证
    if (!this.validateBluetoothInput(blueData)) {
      return;
    }

    try {
      const parsedData = this.parseBluetoothData(blueData);
      if (parsedData) {
        this.parseBlueArrayData(parsedData);
      }
    } catch (error) {
      this.handleParsingError(error as Error, blueData);
    }
  };

  /**
   * 验证蓝牙输入数据
   */
  private validateBluetoothInput(blueData: string): boolean {
    if (!blueData || typeof blueData !== 'string') {
      logger.error('[BatteryManagement] Invalid bluetooth data input:', {
        type: typeof blueData,
        value: blueData,
      });
      return false;
    }

    if (
      blueData.trim().length ===
      DATA_PARSING_CONSTANTS.VALIDATION.MIN_ARRAY_LENGTH
    ) {
      logger.warn('[BatteryManagement] Empty bluetooth data received');
      return false;
    }

    return true;
  }

  /**
   * 解析蓝牙数据JSON
   */
  private parseBluetoothData(
    blueData: string,
  ): BluetoothBatteryDataItem[] | null {
    // 使用统一的安全JSON解析工具
    const parseResult = safeJsonParse<BluetoothBatteryDataItem[]>(blueData, {
      defaultValue: [] as BluetoothBatteryDataItem[],
      context: 'BatteryManagement.parseBluetoothData',
      validator: (data): data is BluetoothBatteryDataItem[] =>
        Array.isArray(data),
      enableDetailedLogging: true,
    });

    if (!parseResult.success || !parseResult.data) {
      return null;
    }

    const parsedData = parseResult.data;

    if (
      parsedData.length === DATA_PARSING_CONSTANTS.VALIDATION.MIN_ARRAY_LENGTH
    ) {
      logger.warn('[BatteryManagement] Empty bluetooth data array received');
      return null;
    }

    return parsedData;
  }

  /**
   * 处理解析错误
   */
  private handleParsingError(error: Error, originalData: string): void {
    logger.error('[BatteryManagement] Bluetooth data parsing failed:', {
      error: error.message,
      stack: error.stack,
      dataLength: originalData.length,
      dataPreview: originalData.substring(
        DATA_PARSING_CONSTANTS.VALIDATION.INITIAL_COUNT,
        DATA_PARSING_CONSTANTS.LOGGING.MAX_ERROR_PREVIEW_LENGTH,
      ),
    });

    // 可以在这里添加错误上报逻辑
    // errorReportService.reportError({...});
  }

  /**
   * 解析蓝牙数组电池数据
   */
  parseBlueArrayData = (dataList: BluetoothBatteryDataItem[]): void => {
    if (!Array.isArray(dataList)) {
      logger.error('[BatteryManagement] Invalid data list format:', {
        type: typeof dataList,
        value: dataList,
      });
      return;
    }

    let processedCount = DATA_PARSING_CONSTANTS.VALIDATION.INITIAL_COUNT;
    let errorCount = DATA_PARSING_CONSTANTS.VALIDATION.INITIAL_COUNT;

    dataList.forEach((item, index) => {
      try {
        if (!this.validateDataItem(item, index)) {
          errorCount++;
          return;
        }

        const {dp_id, dp_data} = item;

        if (this.isBatteryRelatedData(dp_id)) {
          this.handleBlueBatteryData(dp_data, dp_id);
          processedCount++;
        }
      } catch (error) {
        errorCount++;
        logger.error('[BatteryManagement] Error processing data item:', {
          index,
          item,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    });

    // 记录处理结果
    logger.info('[BatteryManagement] Data processing completed:', {
      total: dataList.length,
      processed: processedCount,
      errors: errorCount,
    });
  };

  /**
   * 验证数据项格式
   */
  private validateDataItem(
    item: BluetoothBatteryDataItem,
    index: number,
  ): boolean {
    if (!item || typeof item !== 'object') {
      logger.error('[BatteryManagement] Invalid data item at index:', {
        index,
        type: typeof item,
        value: item,
      });
      return false;
    }

    const {dp_id, dp_data} = item;

    if (typeof dp_id !== 'string') {
      logger.error('[BatteryManagement] Invalid dp_id format:', {
        index,
        dp_id,
        type: typeof dp_id,
      });
      return false;
    }

    if (typeof dp_data === 'undefined') {
      logger.error('[BatteryManagement] Missing dp_data:', {
        index,
        dp_id,
      });
      return false;
    }

    return true;
  }

  /**
   * 检查是否为电池相关数据
   */
  private isBatteryRelatedData(dp_id: string): boolean {
    return (
      dp_id === hexPropertyMap.ztr_battery_level ||
      dp_id === hexPropertyMap.ztr_battery_status
    );
  }

  groupString = (str: string) => {
    const result = [];
    for (
      let i = DATA_PARSING_CONSTANTS.VALIDATION.INITIAL_COUNT;
      i < str.length;
      i += DATA_PARSING_CONSTANTS.BLUETOOTH.HEX_GROUP_SIZE
    ) {
      result.push(
        str.slice(i, i + DATA_PARSING_CONSTANTS.BLUETOOTH.HEX_GROUP_SIZE),
      ); // 截取每两字符
    }
    return result;
  };

  /**
   * 蓝牙通道电池仓电量数据解析
   */
  handleBlueBatteryData = (dp_data: string | string[], dp_id: string): void => {
    try {
      if (!this.validateBatteryDataFormat(dp_data, dp_id)) {
        return;
      }

      const dataString = dp_data as string;
      const powerStr = dataString.substring(
        DATA_PARSING_CONSTANTS.BLUETOOTH.BATTERY_DATA_HEADER_LENGTH,
      );
      const powerArray = this.parseHexStringToNumbers(powerStr);

      if (powerArray === null) {
        logger.error('[BatteryManagement] Failed to parse hex string:', {
          dp_id,
          powerStr,
        });
        return;
      }

      this.updateBatteryData(dp_id, powerArray);

      logger.debug('[BatteryManagement] Battery data updated successfully:', {
        dp_id,
        dataLength: powerArray.length,
        values: powerArray,
      });
    } catch (error) {
      logger.error('[BatteryManagement] Error handling battery data:', {
        dp_id,
        dp_data,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  };

  /**
   * 验证电池数据格式
   */
  private validateBatteryDataFormat(
    dp_data: string | string[],
    dp_id: string,
  ): boolean {
    if (typeof dp_data !== 'string') {
      logger.error('[BatteryManagement] Invalid data type for battery data:', {
        dp_id,
        type: typeof dp_data,
        value: dp_data,
      });
      return false;
    }

    if (
      dp_data.length !== DATA_PARSING_CONSTANTS.BLUETOOTH.BATTERY_DATA_LENGTH
    ) {
      logger.error(
        '[BatteryManagement] Invalid data length for battery data:',
        {
          dp_id,
          expectedLength: DATA_PARSING_CONSTANTS.BLUETOOTH.BATTERY_DATA_LENGTH,
          actualLength: dp_data.length,
          value: dp_data,
        },
      );
      return false;
    }

    // 验证是否为有效的十六进制字符串
    if (!REGEX_PATTERNS.HEX_STRING.test(dp_data)) {
      logger.error('[BatteryManagement] Invalid hex format:', {
        dp_id,
        value: dp_data,
      });
      return false;
    }

    return true;
  }

  /**
   * 解析十六进制字符串为数字数组
   */
  private parseHexStringToNumbers(hexString: string): number[] | null {
    try {
      const hexGroups = this.groupString(hexString);
      const numbers = hexGroups.map(hexStr => {
        const num = parseInt(
          hexStr,
          DATA_PARSING_CONSTANTS.BLUETOOTH.HEX_RADIX,
        );
        if (isNaN(num)) {
          throw new Error(`Invalid hex value: ${hexStr}`);
        }
        return num;
      });

      return numbers;
    } catch (error) {
      logger.error('[BatteryManagement] Error parsing hex string:', {
        hexString,
        error: error instanceof Error ? error.message : String(error),
      });
      return null;
    }
  }

  /**
   * 更新电池数据
   */
  private updateBatteryData(dp_id: string, powerArray: number[]): void {
    if (dp_id === hexPropertyMap.ztr_battery_level) {
      this.setDeviceBatteryLevelList(powerArray);
    } else if (dp_id === hexPropertyMap.ztr_battery_status) {
      this.setDeviceBatteryStatusList(powerArray);
    } else {
      logger.warn('[BatteryManagement] Unknown battery data type:', {
        dp_id,
        powerArray,
      });
    }
  }

  /**
   * @description WIFI通道电池仓电量数据解析
   */
  handleWIFIBatteryData = (dp_data: number[], dp_id: string) => {
    const dataArray = Object.values(dp_data);
    if (
      dataArray.length <= DATA_PARSING_CONSTANTS.VALIDATION.MIN_ARRAY_LENGTH ||
      !Array.isArray(dataArray)
    ) {
      logger.error(
        '[BatteryManagement] WIFI channel battery data format error:',
        {
          dp_id,
          value: dp_data,
          arrayLength: dataArray.length,
        },
      );
    } else {
      if (dp_id === propertyMap.battery_level) {
        this.setDeviceBatteryLevelList(dataArray);
      }
      if (dp_id === propertyMap.battery_status) {
        this.setDeviceBatteryStatusList(dataArray);
      }
    }
  };
}
