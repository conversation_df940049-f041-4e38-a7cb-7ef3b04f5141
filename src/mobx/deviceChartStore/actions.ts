import {computed} from 'mobx';
import DeviceCharState from './state';
import Strings from 'IOTRN/src/i18n';
import {
  convertMeterToMiles,
  mathRoundToFixed,
  squareMeterToAcreStr,
} from 'IOTRN/src/utils/transform';
import {DeviceEventEmitter} from 'react-native';
import ModelMap from '@utils/model';
import {
  formatCoordinate,
  getChartData,
  getDefaultTimeStamp,
} from '../utils/help';
import {gramsToKilograms, gramsToPounds} from 'IOTRN/src/utils/grams';
import {StringUtils, GPSUtils} from 'cvn-panel-kit/src/utils';
import {isHexString} from 'IOTRN/src/utils/string';
import {convertSecondsToTimeInfo} from 'IOTRN/src/utils/time';
import {mToKm, mToMiles} from 'IOTRN/src/utils/unit';
import {RootStore} from '../rootStore';
import DeviceConnectionStore from '../deviceConnectionStore';
import DeviceStore from '../deviceStore';
import {EntryProps, PowerConsumptionProps} from 'IOTRN/src/types';

export default class DeviceCharActions {
  state: DeviceCharState;
  deviceConnectionStore: DeviceConnectionStore;
  deviceStore: DeviceStore;
  constructor(state: DeviceCharState, rootStore: RootStore) {
    this.state = state;
    this.deviceConnectionStore = rootStore.deviceConnectionStore;
    this.deviceStore = rootStore.deviceStore;
  }
  /**
   * 获取单个功率消耗
   *
   * @param res 包含功率消耗信息的对象
   * @returns 格式化后的功率消耗字符串
   */
  getSinglePowerConsumption(res: {[x: string]: PowerConsumptionProps}) {
    const {single_power_consumption} = ModelMap;
    const powerConsumption = Number(res[single_power_consumption] ?? 0) / 1000;
    let result = '';
    const decimal = this.keepDecimal(powerConsumption);
    result = `${mathRoundToFixed(powerConsumption, decimal)} ${Strings.getLang(
      'rn_common_unit_kwh_textview_text',
    )}`;
    return result;
  }

  /**
   * @description: 首页折线图数据处理
   * @param {Object} value
   */
  setHomeUsageHistoryData = (value: EntryProps) => {
    const result = getChartData(value, {isImperial: this.isImperial});
    this.state.homeChartData = result;
  };

  // 判断是否是英制:imperial,公制：metric
  @computed get isImperial() {
    const {appSettingOfUnit} = this.deviceStore.state.initialParams;
    if (appSettingOfUnit === 'imperial') {
      return true;
    } else if (appSettingOfUnit === 'metric') {
      return false;
    } else {
      return true;
    }
  }

  /**
   * @description: 使用历史，折线图数据处理
   * @param {Array} value
   */
  setUsageHistoryData = (value: EntryProps) => {
    const result = getChartData(value, {isImperial: this.isImperial});
    this.state.chartData = result;
  };

  /**
   * @description: 获取数据统计的二氧化碳排放量
   * @param {Array} value
   */
  setStaticsTotalCo2Reduced = (value: EntryProps) => {
    const {totalValue} = getChartData(value, {isImperial: this.isImperial});
    const pounds = gramsToPounds(Number(totalValue ?? 0));
    this.state.staticsTotalCo2Reduced = `${Math.round(
      pounds,
    )} ${Strings.getLang('rn_61004_usagehistory_lbsunit_textview_text')}`;
  };

  /**
   * @description: 设置默认的时间戳，并触发scrollView默认值，schedule使用
   */
  setDefaultTimeStamp = () => {
    this.state.defaultTimeStamp = getDefaultTimeStamp();
    // 触发scrollView滚动
    DeviceEventEmitter.emit('switchOpenListener', {});
  };

  // 解析小车位置数据
  handlePosition = (reported: Record<string, object>) => {
    // 获取原始数据hexString
    const gpsHexStr = reported[ModelMap.gps_coordinate] ?? '';
    const gpsStr = String(gpsHexStr);
    this.refreshLocation(gpsStr);
  };

  /**
   * @description: 每次进入设备面板拿一次设备端返回的值，蓝牙或者wifi通道,赋值一次
   * @param {Number} value
   */
  setInitialTimeStampFromDevice = (value = 0) => {
    this.state.initialTimeStampFromDevice = value - 0;
  };
  /**
   * @description: 本地记录local 时间戳,秒级,判断 isTomrrow的基准值
   * @param {Number} value
   */
  setLocalTimeStamp = (value = 0) => {
    this.state.localTimeStamp = value;
  };

  /**
   * 原始字符串(hexString或’0.0,0.0‘) ==> {latitude, longitude}
   * @param {String} gpsRawStr 原始字符串
   * @returns {latitude, longitude}
   */
  formatCoordinate = (gpsRawStr = '') => {
    // 判断是否是hexString，如果是就解析
    let gpsStr = gpsRawStr;
    if (isHexString(gpsRawStr)) {
      gpsStr = StringUtils.hexToString(gpsRawStr) ?? '';
    }
    const newGpsStr = gpsStr.replace(/\\s/g, '');
    const [longitude, latitude] = newGpsStr.split(',');
    return [longitude, latitude];
  };

  /**
   * 刷新小车位置
   * @param {*} gpsRawStr 兼容传递的是hexString和string类型
   */
  refreshLocation = (gpsRawStr = '') => {
    const [longitude, latitude] = formatCoordinate(gpsRawStr);
    const result = {
      latitude: parseFloat(latitude),
      longitude: parseFloat(longitude),
    };

    // 坐标偏移后，设置新的坐标
    this.setCarPosition(
      GPSUtils.gcj_encrypt({
        latitude: result.latitude,
        longitude: result.longitude,
      }),
    );
  };
  // 刷新当前小车位置
  setCarPosition = (result: {latitude: number; longitude: number}) => {
    const {latitude, longitude} = result;
    let newResult = {
      ...result,
    };
    // 兼容经纬度写反
    if (latitude > 91) {
      newResult = {
        latitude: longitude,
        longitude: latitude,
      };
    }
    this.state.carPosition = newResult;
  };
  // 折线图上面的单位，动态变化
  setSelectedUnit = (value: string) => {
    this.state.selectedUnit = value;
  };
  // 日期组件选中后'2024/01/19'
  setSelectedTime = (value = '') => {
    this.state.selectedTime = value;
  };

  // static 最终数据使用
  get statistics() {
    const res = this.deviceConnectionStore.state.resWithNew;
    // 获取总工作时间
    const totalWorkingTime = this.getTotalWorkingTime(res);
    // 获取总行驶距离
    const totalDrivingDistance = this.getTotalDriveDistance(res, 'statistics');
    // 获取总移动面积
    const totalMovingArea = this.getTotalCuttingArea(res, 'statistics');
    // 获取总割草时间
    const totalmowingTime = this.getTotalMowingTime();
    // 获取总功耗
    const totalPowerConsumption = this.getSinglePowerConsumption(res);

    return {
      totalWorkingTime,
      totalDrivingDistance,
      totalMovingArea,
      totalmowingTime,
      totalPowerConsumption,
    };
  }

  /*
  保留几位小数
  * @param {Number} value
  * @returns {Number} decimal 保留几位小数
  */
  keepDecimal = (value: number) => {
    if (value === 0) {
      return 0;
    }
    if (value > 10) {
      return 0;
    } else if (value < 1) {
      return 2;
    } else {
      return 1;
    }
  };

  // 获取总行驶距离
  getTotalDriveDistance(
    res: {[x: string]: string | number | boolean},
    type?: string,
  ) {
    const {isImperial} = this;
    const {total_driving_distance} = ModelMap;
    const driveDistance = Number(res[total_driving_distance] ?? 0);
    let result = '';
    const driveDistanceMiles = convertMeterToMiles(driveDistance);
    const decimal = this.keepDecimal(driveDistanceMiles);
    if (isImperial) {
      if (type === 'statistics') {
        result = `${mathRoundToFixed(
          driveDistanceMiles,
          decimal,
        )} ${Strings.getLang('rn_61004_trackhistory_milesunit_textview_text')}`;
      } else {
        result = `${Math.round(driveDistanceMiles)} ${Strings.getLang(
          'rn_61004_trackhistory_milesunit_textview_text',
        )}`;
      }
    } else if (type === 'statistics') {
      const decimalNew = this.keepDecimal(driveDistance / 1000);
      result = `${mathRoundToFixed(
        driveDistance / 1000,
        decimalNew,
      )} ${Strings.getLang(
        'rn_61004_usagehistory_unitkilometers_textview_text',
      )}`;
    } else {
      result = `${Math.round(driveDistance / 1000)} ${Strings.getLang(
        'rn_61004_usagehistory_unitkilometers_textview_text',
      )}`;
    }
    return result;
  }

  // 获取总工作时间
  getTotalWorkingTime(res: {[x: string]: string | number | boolean}) {
    const {total_working_time} = ModelMap;
    const workingTime = Number(res[total_working_time] ?? 0);
    const result = `${mathRoundToFixed(
      Number(workingTime / 3600),
      0,
    )} ${Strings.getLang('rn_61004_usagehistory_h_textview_text')}`;
    return result;
  }

  // 获取总割草面积
  getTotalCuttingArea(
    res: {[x: string]: string | number | boolean},
    type?: string,
  ) {
    const {isImperial} = this;
    const {total_mown_area} = ModelMap;
    const cuttingArea = Number(res[total_mown_area] ?? 0);
    let result = '';
    if (isImperial) {
      const acreNew = squareMeterToAcreStr(cuttingArea);
      const decimal = this.keepDecimal(Number(acreNew));
      if (type === 'statistics') {
        result = `${mathRoundToFixed(acreNew, decimal)} ${Strings.getLang(
          'rn_61004_usagehistory_acresunit_textview_text',
        )}`;
      } else {
        result = `${Math.round(Number(acreNew))} ${Strings.getLang(
          'rn_61004_usagehistory_acresunit_textview_text',
        )}`;
      }
    } else {
      const decimalNew = this.keepDecimal(cuttingArea);
      result = `${cuttingArea.toFixed(decimalNew)} ${Strings.getLang(
        'rn_61004_usagehistory_m2unit_textview_text',
      )}`;
    }
    return result;
  }

  // 获取单次割草时间
  getTotalMowingTime() {
    const res = this.deviceConnectionStore.state.resWithNew;
    const {single_moving_time} = ModelMap;
    const workingTime = Number(res[single_moving_time] ?? 0);
    const info = convertSecondsToTimeInfo(workingTime);
    const hour = Number(info.hour || 0);
    const minute = Number(info.minute || 0);
    if (Number(hour) <= 0) {
      this.state.totalMowingTime = `${Math.round(minute)}${Strings.getLang(
        'rn_61004_usagehistory_minute_textview_text',
      )}`;
    } else {
      this.state.totalMowingTime = `${Math.round(hour)}${Strings.getLang(
        'rn_61004_usagehistory_hour_textview_text',
      )} ${Math.round(minute)}${Strings.getLang(
        'rn_61004_usagehistory_minute_textview_text',
      )}`;
    }
    return this.state.totalMowingTime;
  }

  /**
   * 总工作时长,原始值秒，转成 输出「min，hour
   * @example 10000 => 02hr 46min
   * @return {Object} '02hr 46min'
   */
  @computed get totalWorkingTime() {
    const {totalValue} = this.state.chartData;
    const seconds = Number(totalValue ?? 0);
    const info = convertSecondsToTimeInfo(seconds);
    let result = '';
    if (info && 'hour' in info && 'minute' in info) {
      const {hour, minute} = info;
      result = `${hour}${Strings.getLang(
        'rn_61004_usagehistory_hour_textview_text',
      )} ${minute}${Strings.getLang(
        'rn_61004_usagehistory_minute_textview_text',
      )}`;
    }
    return result;
  }

  @computed get totalMownArea() {
    // 原始值平分米
    const {totalValue = 0} = this.state.chartData;
    const areaValue = Number(totalValue ?? 0);

    const squareMeters = areaValue - 0 || 0;
    const acres = squareMeterToAcreStr(squareMeters);

    let result = '';
    const {isImperial} = this;
    if (isImperial) {
      result = `${acres} ${Strings.getLang(
        'rn_61004_usagehistory_acresunit_textview_text',
      )}`;
    } else {
      result = `${squareMeters} ${Strings.getLang(
        'rn_61004_usagehistory_m2unit_textview_text',
      )}`;
    }

    return result;
  }

  @computed get powerConsumption() {
    // 功耗ah
    const {totalValue = 0} = this.state.chartData;
    const powerConsump = Number(totalValue ?? 0) / 1000;
    let result = '';
    result = `${Math.round(powerConsump)} ${Strings.getLang(
      'rn_common_unit_kwh_textview_text',
    )}`;

    return result;
  }

  @computed get drivingdistance() {
    // 行驶距离km
    const {totalValue = 0} = this.state.chartData;
    const drivingdis = Number(totalValue ?? 0);
    const kilometers = mToKm(drivingdis);
    const miles = mToMiles(drivingdis);
    let result = '';
    const {isImperial} = this;
    if (isImperial) {
      result = `${kilometers} ${Strings.getLang(
        'rn_61004_statehome_distancekilometers_textview_text',
      )}`;
    } else {
      result = `${miles} ${Strings.getLang(
        'rn_61004_trackhistory_milesunit_textview_text',
      )}`;
    }
    return result;
  }

  /**
   * 使用历史页面二氧化碳消耗量
   * g 接口返回值
   * 1.工作时长：秒
   * 2.co2减排量：g  接口返回值
   * 3.割草面积： 平方米
   * 6.3日李伟找方工确定的值
   */
  @computed get totalCo2Reduced() {
    const {totalValue = 0} = this.state.chartData;
    const co2Value = Number(totalValue);
    /**
     * 二氧化碳减排量跟实际使用时长相关，为380mg/s，22.8g/min，1.4kg/h
     */
    const kilograms = gramsToKilograms(co2Value);
    const pounds = gramsToPounds(co2Value);
    let result = '';
    const {isImperial} = this;
    if (isImperial) {
      const decimal = this.keepDecimal(pounds);
      result = `${pounds.toFixed(decimal)} ${Strings.getLang(
        'rn_61004_usagehistory_lbsunit_textview_text',
      )}`;
    } else {
      const decimalKil = this.keepDecimal(kilograms);
      result = `${kilograms.toFixed(decimalKil)} ${Strings.getLang(
        'rn_61004_usagehistory_kgunit_textview_text',
      )}`;
    }
    return result;
  }
}
