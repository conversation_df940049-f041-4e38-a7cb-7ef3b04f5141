import {computed} from 'mobx';
import DeviceStatusAndConnectionState from './state';
import Strings from 'IOTRN/src/i18n';
import {cmPerSecondToMph} from 'IOTRN/src/utils/transform';
import {convertSecondsToTimeInfo} from 'IOTRN/src/utils/time';
import {device} from 'cvn-panel-kit';
import {
  editProperty,
  getOnceDataWithWIFI,
  getTopicDpData,
  setLocalProperty,
} from '../utils/help';
import ModelMap from '@utils/model';
import topicMap from 'IOTRN/src/utils/topic';
import DpDataUtils from 'IOTRN/src/utils/dpData';
import {RootStore} from '../rootStore';
import DeviceStore from '../deviceStore';
import {InitialParamsProps} from 'IOTRN/src/types/device';
import {parseJsonSafely} from '../../utils/jsonParser';
import BatteryStore from '../batteryStore';
import {PrimitiveValue} from 'IOTRN/src/types';

// Type aliases to replace repeated union types
type ParsedArrayItem = {dp_id: string; dp_data?: PrimitiveValue};
type DeviceDataMap = {[key: string]: PrimitiveValue};
type StringMap = {[key: string]: string};

export default class DeviceStatusAndConnectionActions {
  state: DeviceStatusAndConnectionState;
  deviceStore: DeviceStore;
  batteryStore: BatteryStore;
  constructor(state: DeviceStatusAndConnectionState, rootStore: RootStore) {
    this.state = state;
    this.deviceStore = rootStore.deviceStore;
    this.batteryStore = rootStore.batteryManagementStore;
  }

  /**
   * 汇聚，用于展示首页navBar subtitle，只要有一个连接就是连接
   * 都不连接时显示不连接
   * 蓝牙连接中and WIFI不连接显示连接中
   */
  @computed get connectStatus() {
    // 蓝牙连接状态0、1、2、3   disconnect/connecting/connected/fail
    // WIFI 连接 true or false
    const {bleConnectedStatus, connected} = this.state;
    let result = 0;
    if (bleConnectedStatus === 2 || connected) {
      result = 2;
    }
    if (bleConnectedStatus === 1 && !connected) {
      result = 1;
    }
    return result;
  }

  // 根据状态获取当前时间对象
  @computed get getCurrentTimeObj() {
    const res = this.state.resWithNew;
    const {full_vehicle_remaining_time, remaining_mowing_time} = ModelMap; // 获取物模型标识
    // 获取剩余充电时间
    const remainChargeTime = res[full_vehicle_remaining_time] ?? 0;
    // 获取剩余割草时间
    const remainMowingTime = res[remaining_mowing_time] ?? 0;
    let remainTime = {hour: '0', minute: '0'};
    switch (this.state.status) {
      case 'mowing':
        // 转换剩余割草时间为时间信息
        remainTime = convertSecondsToTimeInfo(remainMowingTime as number);
        break;
      case 'charging':
        // 转换剩余充电时间为时间信息
        remainTime = convertSecondsToTimeInfo(
          (remainChargeTime as number) * 60,
        );
        break;
      default:
        break;
    }
    return remainTime;
  }
  /**
   * @description: 通过指令获取WiFi信息
   */
  getDeviceWifiInfo() {
    const wificmdHex = '55AA00AD0000A092';
    device.sendCmd(wificmdHex, this.deviceStore.state.initialParams.mac);
  }

  // 设置初始参数
  setInitialParams(params: InitialParamsProps) {
    // 将参数赋值给state中的initialParams
    this.deviceStore.state.initialParams = params;
  }

  // 根据状态获取剩余时间的标题前缀
  getRemainTitlePrefix() {
    const status = this.state.status;
    if (status === 'mowing') {
      this.state.remainTitlePrefix = Strings.getLang(
        'rn_61004_panelhome_bladerotation_textview_text',
      );
    }
    this.state.remainTitlePrefix = Strings.getLang(
      'rn_61004_panelhome_statuscardfinishin_textview_text',
    );
  }

  /**
   * @description: 离线时全部disabled
   */
  @computed get allDisabled() {
    const result = !this.state.bleConnected && !this.state.connected;
    return result;
  }

  /**
   * 获取刀片转速
   */
  @computed get bladeSpeed() {
    const cmPerSeconds = Number(
      this.state.resWithNew[ModelMap.current_driving_speed] ?? 0,
    );
    const value = cmPerSecondToMph(cmPerSeconds);
    const result = value > 0 ? value.toFixed(1) : 0;
    return result;
  }

  /**
   * @description: 设置WIFI连接状态
   * @param {Boolean} connected
   */
  setWIFIConnect = (connected: boolean) => {
    this.state.connected = connected;
    // 首次进入RN面板设备在线/检测到连接成功，单次拉取数据
    getOnceDataWithWIFI({
      deviceId: this.deviceStore.state.initialParams.deviceId,
    });
  };

  /**
   * @description: topic返回处理
   * @param {Object} info
   * @param {Function} callBack
   * 备注：callBack可能会走多次
   */
  handleTopicRes = (info: {type: string; payload: string}) => {
    const {type, payload} = info;
    if (payload === '') {
      console.warn('返回payload为空串');
      return;
    }
    let connected = this.state.connected;
    if (type.indexOf(topicMap.connectWIFI) !== -1) {
      connected = true;
    } else if (type.indexOf(topicMap.disConnectWIFI) !== -1) {
      connected = false;
    }

    // 物模型topic
    else if (
      type.indexOf(topicMap.shadowUpdateAcceptedSuffix) !== -1 ||
      type.indexOf(topicMap.shadowGetAcceptedSuffix) !== -1
    ) {
      // 使用安全的JSON解析，提供默认值和错误处理
      const payloadObj = parseJsonSafely(
        payload,
        {state: {reported: {}}} as {state: {reported: {[key: number]: string}}}, // 默认结构
        'DeviceConnection.handleTopicRes',
      );
      this.handleModelData(payloadObj);
    }
    if (
      type.indexOf(topicMap.connectWIFI) !== -1 ||
      type.indexOf(topicMap.disConnectWIFI) !== -1
    ) {
      this.setWIFIConnect(connected);
    }
  };

  /**
   * @description: 物模型处理,RN端接受只处理reported 数据，设备端处理desired的数据
   * @param {Object} payload
   * @param {Function} callBack
   */
  handleModelData = (payload: {state: {reported: {[key: string]: string}}}) => {
    const reportedMap = payload?.state?.reported || {};
    this.batteryStore.actions.handleWIFIBatteryData(reportedMap);
    const parsedArray = Object.keys(reportedMap).map(key => {
      const dp_data = getTopicDpData(reportedMap, key) ?? '';
      return {
        dp_id: `${key}`,
        // 注意这里对应的值是否需要hexstring转成 int、bool
        // dp_data: reportedMap[key],
        // 单参数改成多参数，增加了一层param_id，目前都为1，
        dp_data,
      };
    });
    const result = this.dealWithModel(parsedArray, this.state.resWithWIFI);
    this.state.resWithWIFI = result;
    // 4.8日新增
    const resultNew = this.dealWithModel(parsedArray, this.state.resWithNew);
    this.state.resWithNew = resultNew;
  };

  /**
   * WIFI和蓝牙解析共用
   */
  dealWithModel = (parsedArray: ParsedArrayItem[], lastData: DeviceDataMap) => {
    // topic返回或者蓝牙返回最新的数据，暂存
    const comingResult: StringMap = {};
    const result: DeviceDataMap = {...lastData};
    parsedArray.forEach((item: ParsedArrayItem) => {
      result[item.dp_id] = item.dp_data ?? '';
      comingResult[item.dp_id] = String(item.dp_data ?? '');
    });
    this.state.comingResult = {...comingResult};
    return result;
  };

  /**
   * @description: 统一处理蓝牙返回，用到的地方直接从 this.pannel取对应值
   * @param {Object} res
   */
  handleBleRes = (res: string) => {
    // 使用安全的JSON解析，提供默认值和错误处理
    const array = parseJsonSafely(
      res,
      [] as Array<{
        dp_id: string;
        dp_type: string;
        dp_data: {[key: string]: string}[];
      }>, // 默认空数组
      'DeviceConnection.handleBleRes',
    );
    const parsedArray = array.map(
      (item: {
        dp_id: string;
        dp_type: string;
        dp_data: {[key: string]: string}[];
      }) => {
        const result: {
          dp_id: string;
          dp_type: string;
          dp_data: PrimitiveValue;
        } = {
          dp_id: '',
          dp_type: '',
          dp_data: '',
        };
        result.dp_id = Number.parseInt(item.dp_id, 16).toString();
        result.dp_type = item.dp_type;
        result.dp_data = String(DpDataUtils.parseDpData(result, item)); // 转换为字符串
        return result;
      },
    );
    // 上次缓存，防止蓝牙多次返回数据显示异常(冲掉)
    const result = this.dealWithModel(parsedArray, this.state.resWithBle);
    this.state.resWithBle = result;
    const resultNew = this.dealWithModel(parsedArray, this.state.resWithNew);
    this.state.resWithNew = resultNew;
  };

  handleNewBleRes = (
    res: string,
    callback: (res: {[key: string]: string}) => void,
  ) => {
    // 使用安全的JSON解析，提供默认值和错误处理
    const array = parseJsonSafely(
      res,
      [] as Array<{
        dp_id: string;
        dp_type: string;
        dp_data: {[key: string]: string}[];
      }>, // 默认空数组
      'DeviceConnection.handleNewBleRes',
    );
    const parsedArray = array.map(
      (item: {
        dp_id: string;
        dp_type: string;
        dp_data: {[key: string]: string}[];
      }) => {
        const result: {
          dp_id: string;
          dp_type: string;
          dp_data: PrimitiveValue;
        } = {
          dp_id: '',
          dp_type: '',
          dp_data: '',
        };
        result.dp_id = Number.parseInt(item.dp_id, 16).toString();
        result.dp_type = item.dp_type;
        result.dp_data = String(DpDataUtils.parseDpData(result, item)); // 转换为字符串
        return result;
      },
    );
    // 上次缓存，防止蓝牙多次返回数据显示异常(冲掉)
    const result = this.dealWithModel(parsedArray, {});
    const stringResult = Object.entries(result).reduce((acc, [key, value]) => {
      acc[key] = String(value);
      return acc;
    }, {} as {[key: string]: string});
    callback(stringResult);
  };

  /**
   * @description: 本地先响应，最终等设备端回馈后再刷新
   * @param {String} key
   * @param {Boolean | Number} value
   */
  setLocalRender = (key: string | number, value: string) => {
    const result = {...this.state.resWithNew};
    result[key] = value;
    if (this.state.bleConnected) {
      this.state.resWithBle = {...result};
    } else if (this.state.connected) {
      this.state.resWithWIFI = {...result};
    } else {
      this.state.resWithBle = {...result};
      this.state.resWithWIFI = {...result};
    }
    // 4.8日新增
    this.state.resWithNew = {...result};
  };

  /**
   * @description: 本地先响应，最终等设备端回馈后再刷新
   * @param {Object} info
   */
  setLocalRenderWithObject = (info = {}) => {
    const result = {...this.state.resWithNew, ...info};
    if (this.state.bleConnected) {
      this.state.resWithBle = {...result};
    } else if (this.state.connected) {
      this.state.resWithWIFI = {...result};
    } else {
      this.state.resWithBle = {...result};
      this.state.resWithWIFI = {...result};
    }
    // 4.8日新增
    this.state.resWithNew = {...result};
  };

  /**
   * @description: 最新连接状态
   * @param {Number} value
   */
  setLatestBleConnectedStatus = (value: number) => {
    this.state.latestBleConnectedStatus = value;
  };
  /**
   * @description: 判断是否在RNContainerVC内
   * @param {Boolean} value
   */
  setIsInRNContainerVC = (value: boolean) => {
    this.state.isInRNContainerVC = !!value;
  };

  /**
   * @description: 从后台切到前台赋值
   * @param {Boolean} value
   */
  setAppDidBecomeActive = (value: boolean) => {
    this.state.appDidBecomeActive = value;
  };

  /**
   * @description: 各种标识，当成是发送指令一次，返回一次
   * @param {Object} value
   */
  setTagInfo = (value = {}) => {
    this.state.tagInfo = {...this.state.tagInfo, ...value};
  };

  /**
   * @description: 设置零部件详情数据
   * @param {Object} value - 零部件详情数据
   */
  setPartsDetail = (value: {number: number; type: number}) => {
    this.state.partsDetail = value;
  };

  // 蓝牙开关状态
  setCurrentBleState = (status: number) => {
    this.state.currentBleState = status;
  };

  /**
   * @description: 统一处理数据结构,有蓝牙连接走蓝牙，没有的话走云端
   * @param {Object} params
   */
  editProperty = (params: {propertyData: {[x: string]: number}}) => {
    const secondParams = {
      bleConnected: this.state.bleConnected,
      connected: this.state.connected,
      deviceId: this.deviceStore.state.initialParams.deviceId,
    };
    editProperty(params, secondParams, tmpKeys => {
      this.setCurrentModelKeys(tmpKeys);
    });
  };

  /**
   * @description: 遥控模式走蓝牙协议,再此统一封装成需要的数据格式
   * @param {Array} data
   */
  setLocalProperty = (data: object) => {
    return setLocalProperty(data);
  };

  /**
   * @description: 设置原生pop 标识
   * @param {Boolean} value
   */
  setIsNativeGoback = (value: boolean) => {
    this.state.isNativeGoback = !!value;
  };

  /**
   * @description: 设置当前发命令的key数组
   * @param {Array} value
   */
  setCurrentModelKeys = (value: string[]) => {
    this.state.currentModelKeys = [...value];
  };

  // 设置是否是刚进入RN
  setIsJustEnterInPanel = (value: boolean) => {
    this.state.isJustEnterInPanel = value;
  };

  /**
   * @description: 设置蓝牙连接状态 number
   * @param {Number} status
   */
  setBleConnectStatus = (status: number) => {
    this.state.bleConnectedStatus = status;
  };
  /**
   * @description: 设置蓝牙连接状态
   * @param {Boolean} connected
   */
  setBleConnect = (connected: boolean) => {
    this.state.bleConnected = connected;
  };
  /**
   * @description: 重置蓝牙连接状态
   */
  resetBleConnect = () => {
    this.state.bleConnected = false;
  };
  /**
   * @description: 重置WIFI连接状态
   */
  resetConnect = () => {
    this.state.connected = false;
  };
  /**
   * @description: 重置物模型数据
   */
  resetData = () => {
    this.state.resWithWIFI = {};
    this.state.resWithBle = {};
    // 4.8日新增
    this.state.resWithNew = {};
    this.state.initialTimeStampFromDevice = 0;
  };

  /**
   * @description: 每次进入设备面板拿一次设备端返回的值，蓝牙或者wifi通道,赋值一次
   * @param {Number} value
   */
  setInitialTimeStampFromDevice = (value = 0) => {
    this.state.initialTimeStampFromDevice = value - 0;
  };
  /**
   * @description: 本地记录local 时间戳,秒级,判断 isTomrrow的基准值
   * @param {Number} value
   */
  setLocalTimeStamp = (value = 0) => {
    this.state.localTimeStamp = value;
  };
  /**
   * @description: 仪表盘 是否超出信号强度限制
   * @param {Boolean} value
   */
  setIsOutOfControl = (value: boolean) => {
    this.state.isOutOfControl = value;
  };

  // 获取剩余充电和割草时间的信息
  getRemainingTimeInfo() {
    const res = this.state.resWithNew;
    const {remaining_mowing_time} = ModelMap; // 获取物模型标识
    // 获取剩余割草时间
    const remainMowingTime = res[remaining_mowing_time] ?? 0;
    // 转换剩余割草时间为时间信息
    this.state.remainMowingTimeTimeInfo = convertSecondsToTimeInfo(
      remainMowingTime as number,
    );
  }

  // 获取刀盘转速
  getBladeRotationSpeed() {
    const res = this.state.resWithNew;
    const {blade_rotation_speed} = ModelMap; // 获取物模型标识
    // 获取刀盘转速
    const bladeRotationSpeed = res[blade_rotation_speed] ?? 0;
    // 返回刀盘转速
    return {
      bladeRotationSpeed,
    };
  }

  /**
   *  * 0 - 空闲、1 - 自走、2 - 割草、3 - 充电、4 - 移动
   */
  @computed get ztrWorkStatus() {
    const {ztr_work_status} = ModelMap; // 获取物模型标识
    // 获取工作状态
    const result = Number(this.state.resWithNew[ztr_work_status] ?? 0);
    return result;
  }

  // 获取当前工作状态和状态标题
  getStatusInfo() {
    let status = '';
    let statusTitle = '';
    let showStatus = false;

    // 判断状态是否应该显示
    if (this.ztrWorkStatus === 2 || this.ztrWorkStatus === 3) {
      showStatus = true;
    }

    // 根据工作状态确定状态和状态标题
    switch (this.ztrWorkStatus) {
      case 2:
        status = 'mowing';
        statusTitle = Strings.getLang(
          'rn_61004_panelhome_statusmowing_textview_text',
        );
        break;
      case 3:
        status = 'charging';
        statusTitle = Strings.getLang(
          'rn_61004_panelhome_statuscharging_textview_text',
        );
        break;
      default:
        break;
    }
    this.state.status = status;
    this.state.statusTitle = statusTitle;
    this.state.showStatus = showStatus;
  }
}
