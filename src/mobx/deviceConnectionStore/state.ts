import {MixedValueMap, DeviceWorkStatus} from '../../types';

export default class DeviceStatusAndConnectionState {
  // 初始化参数

  // res: 0:notconnected  1:connecting  2:connected 3: 链接失败
  bleConnectedStatus: number = 1;
  // true false
  bleConnected: boolean = false;

  // WIFI 连接
  connected: boolean = false;
  // ble 数据返回统一处理
  resWithBle: MixedValueMap = {};
  // WIFI 数据返回统一处理
  resWithWIFI: MixedValueMap = {};
  // 4.8日 新增，统一返回数据源，蓝牙通道或者WIFI通道，哪个最新，就刷新对应的物模型的数据
  resWithNew: MixedValueMap = {};
  // 本地先响应，区分WIFI和蓝牙数据返回，方便看日志
  resWithLocal: MixedValueMap = {};
  // 记录上一次的status，当最新的和上一次不一致时，清理轨迹数据，然后调用云端path接口
  lastDeviceStatus: number = DeviceWorkStatus.MOVING; // 默认值和default保持一致

  // connect/disconnect 内时间戳记录
  connectChangeTimestamp: number = 0;
  // 调用了原生pop操作标识
  isNativeGoback: boolean = false;
  // 上一次的连接状态
  latestBleConnectedStatus: number = 0;
  // 是否在RN容器内
  isInRNContainerVC: boolean = true;
  // app变为active
  appDidBecomeActive: boolean = false;
  // 发命令时，记录key放到下面数组，等reported回来，如果有key被包含在下面数组，去除loading，更精准
  currentModelKeys: string[] = [];
  // 请求标识
  tagInfo: MixedValueMap = {};

  // 里面有hour、date、minute key，值为字符串
  currentDateInfo: MixedValueMap = {};
  // topic 返回或者蓝牙监听返回的最新解析后的数据，区别于resWithWIFI，resWithBle
  comingResult: MixedValueMap = {};
  // 最新的ota version
  newOtaVersion: string = '';

  // 表示刚刚进入面板，15s后置位false
  isJustEnterInPanel: boolean = true;

  // 配件详情数据源
  partsDetail: {number: number; type: number} = {number: 0, type: 1};

  // 预约开关默认设置的时间戳
  defaultTimeStamp: number = 0;
  // 每次进入面板取到的设备端返回的值
  initialTimeStampFromDevice: number = 0;
  // 本地记录local 时间戳,秒级
  localTimeStamp: number = 0;

  // 超出蓝牙可控范围，用信号值判断，如果超出，弹框
  isOutOfControl: boolean = false;
  // 遥控页面 间隔时间 调试用
  remoteControlInterval: number = 230;

  // 0 未开启，1已开启，2 未授权
  currentBleState: number = 0;

  // 原生监听回来后的设备消息体
  messageData: MixedValueMap = {};
  // 转换剩余割草时间为时间信息
  remainMowingTimeTimeInfo: {hour: string; minute: string} = {
    hour: '',
    minute: '',
  };

  status: string = '';
  statusTitle: string = '';
  showStatus: boolean = false;

  remainTitlePrefix: string = '';
}
