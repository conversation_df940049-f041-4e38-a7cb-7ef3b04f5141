import apiMap from '@api/map.js';
import {CVNSdk} from '@cvn/rn-panel-kit';
/**
 * 拉取首页故障信息接口
 * @param {Object} panel
 * @param {Function} callBack
 * @returns {Promise} 供外部调用
 */
export const fetchFaultInfo = (panel = {}, callback = () => {}) => {
  console.log('fetchFaultInfo-panel.deviceId--', panel.deviceId);

  return CVNSdk.apiRequest(
    apiMap.faultInfo,
    {
      deviceId: panel.deviceId,
    },
    {showLoading: false},
  ).then(res => {
    console.log('fetchFaultInfo---', res);
    callback(res.entry || {});
  });
};
