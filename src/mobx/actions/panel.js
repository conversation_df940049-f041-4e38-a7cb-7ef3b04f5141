/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2024-11-19 14:17:24
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-12-02 11:30:47
 * @FilePath: /react-native-panels/src/mobx/actions/panel.js
 * @Description: mobx action
 *
 */
import {action, isObservableArray, isObservableObject, toJS} from 'mobx';
import {device, mobile} from '@cvn/rn-panel-kit';
import topicMap from '@config/topic';
import {OtaUtils, JumpUtils} from '@cvn/rn-panel-kit/src/utils';
import {get, omitBy, isArray, isEqual} from 'lodash-es';
import {isObject} from '@utils/type.js';
import DpDataUtils from '@utils/dpdata.js';
import {dpIds} from '@config/model.js';
import {fetchFaultInfo, getDetail} from '@/api/map';

/**
 * @typedef {import('@config/types').OtaResult} OtaResult
 * @typedef {import('@config/types').TopicData} TopicData
 * @typedef {import('@config/types').DeviceDetailData} DeviceDetailData
 * @typedef {import('@config/types').MessageData} MessageData
 * @typedef {import('@config/types').DeviceDetailResponseData} DeviceDetailResponseData
 * @typedef {import('@config/types').DeviceUnreadMessageResponseData} DeviceUnreadMessageResponseData
 */

export default class PanelActions {
  constructor(panel) {
    this.panel = panel;
    this.showDialog = false;
  }

  /**
   * @description: 设备详情接口
   * @param {Object} options
   * @return {Promise<DeviceDetailData>} 供外部调用
   */
  @action getDeviceDetail = (option = {showLoading: false}) => {
    return getDetail(option, this.panel).then(res => {
      const detail = res.entry;
      this.setDetail(detail);
      return detail;
    });
  };

  /**
   * 设置wifi连接状态
   * @param {Boolean} isConnected
   */
  @action setWifiConnectStatus = isConnected => {
    this.panel.isWifiConnected = isConnected;
  };

  /**
   * 原生传递参数后初始化，同时初始化设备详情数据
   * @param {object} params
   */
  @action initParams = params => {
    this.panel.initialParams = params;

    // 解析传进来的设备详情
    const {deviceDetail: detail} = params;
    if (detail !== undefined) {
      const detailDict = JSON.parse(detail);
      if (isObject(detailDict)) {
        this.setDetail(detailDict);
      }
    }
  };

  /**
   * 订阅aws
   * @param {String} deviceId
   * @param {Boolean} isSubset
   */
  @action subscribe = (deviceId, isSubset = false) => {
    // 获取数据
    if (device.subscribe) {
      const connect4GTopic = `${topicMap.connect4G}${deviceId}`;
      const disConnect4GTopic = `${topicMap.disConnect4G}${deviceId}`;
      const shadowStr = `${topicMap.preFix}${deviceId}${topicMap.shadowUpdateAcceptedSuffix}`;
      // 新增 单次拉取返回
      const shadowGetAcceptedStr = `${topicMap.preFix}${deviceId}${topicMap.shadowGetAcceptedSuffix}`;
      console.log('connect4GTopic---', connect4GTopic);
      console.log('disConnect4GTopic---', disConnect4GTopic);
      console.log('shadowStr---', shadowStr);

      if (isSubset === false) {
        // 4G连接
        device.subscribe(connect4GTopic);
        // 4G断开
        device.subscribe(disConnect4GTopic);
      }
      // 订阅物模型 接收
      device.subscribe(shadowStr);
      // 物模型订阅，单次拉取
      device.subscribe(shadowGetAcceptedStr);
    }
  };

  /**
   * 取消订阅aws
   * @param {String} deviceId
   * @param {Boolean} isSubset
   */
  @action unsubscribe = (deviceId, isSubset = false) => {
    if (device.unsubscribe) {
      const connect4GTopic = `${topicMap.connect4G}${deviceId}`;
      const disConnect4GTopic = `${topicMap.disConnect4G}${deviceId}`;
      const shadowStr = `${topicMap.preFix}${deviceId}${topicMap.shadowUpdateAcceptedSuffix}`;
      const shadowGetAcceptedStr = `${topicMap.preFix}${deviceId}${topicMap.shadowGetAcceptedSuffix}`;
      if (!!isSubset === false) {
        // 4G连接
        device.unsubscribe(connect4GTopic);
        // 4G断开
        device.unsubscribe(disConnect4GTopic);
      }
      // 订阅物模型 接收
      device.unsubscribe(shadowStr);
      // 物模型订阅，单次拉取
      device.unsubscribe(shadowGetAcceptedStr);
    }
  };

  /**
   * 设置详情数据
   * @param {DeviceDetailData} params
   */
  @action setDetail = params => {
    this.panel.deviceDetail = params;
  };

  /**
   * 更新 wifi 报上来的数据，如果不一样才更新
   * @param {array} parsedArray
   * @returns {void}
   */
  _updateStoreData = parsedArray => {
    const res = this.panel.hubModelData;
    parsedArray.forEach(item => {
      const newData = item.dp_data;
      const oldData = res[`${item.dp_id}`];
      if (isObservableArray(oldData)) {
        if (!isEqual(newData, toJS(oldData))) {
          res[`${item.dp_id}`] = newData;
          console.log('update array', item.dp_id, newData);
        }
        return;
      }
      if (isObservableObject(oldData)) {
        if (!isEqual(newData, toJS(oldData))) {
          res[`${item.dp_id}`] = newData;
          console.log('update object', item.dp_id, newData);
        }
        return;
      }
      if (!isEqual(newData, oldData)) {
        res[`${item.dp_id}`] = newData;
        console.log('update plain data', item.dp_id, newData);
      }
    });
  };

  /**
   * 解析物模型，并更新 store
   * @param {TopicData} res
   */
  @action parseThingModel = res => {
    /**
     * @type {Object}
     */
    const reportedMap = get(res, ['state', 'reported']) || {};

    // console.log('reportedMap--parseThingModel-', reportedMap);
    const parsedArray = Object.keys(reportedMap)
      .filter(key => {
        return dpIds.includes(`${key}`);
      })
      .map(key => {
        const dp_id = `${key}`;
        let dp_data;
        if (isArray(reportedMap)) {
          console.warn('4G返回数据结构错误');
        } else {
          dp_data = DpDataUtils.parseDpData({
            dp_id,
            dp_data: reportedMap[key],
          });
        }
        // console.log('dp_data--', dp_id, dp_data);
        return {
          dp_id,
          dp_data,
        };
      });

    // console.log('parsedArray--', parsedArray);
    this._updateStoreData(parsedArray);
  };

  /**
   * 多参转换
   * @param {Object} propertyData
   * @return {Object} 物模型对象中增加一层key: 1
   */
  @action getDataFromPropertyData = propertyData => {
    const result = {};
    Object.keys(propertyData).forEach(key => {
      result[key] = {
        1: propertyData[key],
      };
    });
    return result;
  };

  /**
   * 在通过命令更新设备影子之前，立刻更新属性的值
   * @param {Object} propertyData
   */
  _updateLocalData = propertyData => {
    const res = this.panel.hubModelData;
    const toBeUpdatedDpIds = Object.keys(propertyData);
    for (const dpId of toBeUpdatedDpIds) {
      const newData = propertyData[dpId];
      // 确保 dpId 在 ModelMap 中，排除 issueOnlyMap
      if (dpIds.includes(dpId)) {
        res[dpId] = newData;
      }
    }
  };

  /**
   * 设置命令，通过wifi下发指令
   * @param {Object} params
   * @param {boolean} params.isRemote 是否通过device发送下行数据，默认打开，只在少数情况下关闭
   * @param {string} params.deviceId 设备Id
   * @param {Object} params.propertyData 发送数据
   */
  @action editProperty = params => {
    const {deviceId, isRemote = true, propertyData} = params;
    // console.log('update property, data => ', propertyData);
    this._updateLocalData(propertyData);

    if (device.editProperty && isRemote) {
      const data = omitBy(propertyData, item => item === undefined);
      const resultData = this.getDataFromPropertyData(data);
      // console.log('resultData---', JSON.stringify(resultData));
      const topicStr = `${topicMap.preFix}${deviceId}${topicMap.shadowUpdateSuffix}`;
      console.log('4G命令---topicStr---', topicStr);
      // console.log('4G命令---resultData---', JSON.stringify(resultData));
      device.editProperty(
        topicStr,
        JSON.stringify({
          state: {
            desired: resultData,
          },
        }),
      );
    }
  };

  /**
   * publish to shadow/get topic
   * 等待物模型数据上报
   */
  @action publishTopic = deviceId => {
    // 单次获取物模型所有属性，通过监听方法返回
    const shadowStr = `${topicMap.preFix}${deviceId}${topicMap.shadowGet}`;
    if (device.topicPublish) {
      console.log('topicPublishshadowStr--', shadowStr);
      device.topicPublish(shadowStr, '');
    }
  };

  /**
   * ota升级，监听查询是否有升级
   * @param {Function} callback
   */
  @action checkAndSetOtaResult = callback => {
    const {deviceId} = this.panel;
    OtaUtils.checkAndSubscribeToOTATask({deviceId}, otaInfo => {
      console.log('check ota result', otaInfo);
      this.setOtaResult(otaInfo);
      callback && callback();
    });
  };

  /**
   * 设置ota升级结果
   * @param {OtaResult} value
   */
  @action setOtaResult = value => {
    this.panel.otaResult = value;
  };

  /**
   * 判断是否在RNContainerVC内
   * @param {Boolean} value
   */
  @action setIsInRNContainerVC = value => {
    this.panel.isInRNContainerVC = !!value;
  };

  /**
   * 设置离线弹窗已打开
   * @param {Boolean} value
   */
  @action setIsOfflinePopupPoped = value => {
    this.panel.isOfflinePopupPoped = value;
  };

  /**
   * ota强制升级弹框
   * @param {string} confirmText
   */
  @action dealWithOtaForceAlert = confirmText => {
    const {isInRNContainerVC, isWifiConnected} = this.panel;
    const {showRed, isForceUpdate} = this.panel.otaResult;
    if (
      showRed &&
      isForceUpdate &&
      isInRNContainerVC &&
      isWifiConnected &&
      !this.showDialog
    ) {
      this.showDialog = true;
      // 强制弹框
      mobile.simpleConfirmDialog(
        ' ',
        confirmText,
        () => {
          this.showDialog = false;
          this.jumpToOTA();
        },
        () => {
          mobile.back();
        },
      );
    }
  };

  /**
   * 设置连接状态topic返回的最新时间戳
   * @param {number} value
   */
  @action setConnectChangeTimestamp = value => {
    this.panel.connectChangeTimestamp = value;
  };
  /**
   * 设置故障数据源
   */
  @action setFaultInfo = (info = {}) => {
    console.log('faultInfo--', JSON.stringify(info));
    this.panel.faultInfo = info;
  };
  /**
   * 拉取首页故障信息接口
   * @returns Promise
   */
  @action fetchFaultInfo = () => {
    fetchFaultInfo(this.panel, resObj => {
      this.setFaultInfo(resObj);
    });
  };
  /**
   * @description 跳转到OTA升级
   **/
  @action jumpToOTA = () => {
    const {panel} = this;
    JumpUtils.jumpTo('ChervonIot://Fleet/DeviceManage/OTAInfo', {
      deviceId: panel.deviceId,
      singleMcu: true,
      upgradeModel: 0,
      isSubset: false, // 待定
    });
  };

  /**
   * @description 跳转到产品详情
   **/
  @action jumpToProductIntro = () => {
    const {panel} = this;
    JumpUtils.jumpTo('ChervonIot://Fleet/DeviceManage/productHelp', {
      productId: panel.productId,
    });
  };
  // 获取设备详情封装
  @action fetchDeviceDetail = (option = {showLoading: false}) => {
    return this.getDeviceDetail(option);
  };
}
