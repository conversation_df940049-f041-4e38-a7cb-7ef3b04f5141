import {action, isObservableArray, isObservableObject, toJS} from 'mobx';
import apiMap from '@/api';
import {CVNSdk, device, Utils} from '@cvn/rn-panel-kit';
import ModelMap from '@config/model.js';
import {isObject} from '@utils/type.js';
import {isEqual, omitBy} from 'lodash-es';
import DpDataUtils from '@utils/dpdata.js';
import {Platform} from 'react-native';
import {fetchFaultInfo} from './help';
const {JumpUtils} = Utils;
/**
 * @typedef {import('@config/types').MessageData} MessageData
 * @typedef {import('@config/types').DeviceDetailResponseData} DeviceDetailResponseData
 * @typedef {import('@config/types').DeviceUnreadMessageResponseData} DeviceUnreadMessageResponseData
 */

export default class PanelActions {
  constructor(panel) {
    this.panel = panel;
  }

  /**
   * 请求接口后，更新设备详情数据
   * @param {Object} params
   * @returns {Promise<Object>}
   */
  @action deviceDetail = params => {
    this.getDeviceDetail(params).then(res => {
      this.setDetail(res.entry);
    });
  };

  /**
   * 请求设备详情接口, 子设备详情同样调用这个接口
   * @param {Object} params
   * @returns {Promise<DeviceDetailResponseData>}
   */
  @action getDeviceDetail = params => {
    return CVNSdk.apiRequest(apiMap.deviceDetail, params);
  };

  /**
   * 蓝牙发送指令修改属性封装
   * @param {Object} params 请求参数
   */
  @action editProperty = params => {
    const {propertyData} = params;
    // 过滤数据
    const data = omitBy(propertyData, item => item === undefined);
    DpDataUtils.editPropertyWithBle(data, resultData => {
      this.setLocalProperty(resultData).then(res => {});
    });
  };

  /**
   * 原生传递参数后初始化，同时初始化设备详情数据
   * @param {object} params
   */
  @action initParams = params => {
    this.panel.initialParams = params;

    // 解析传进来的设备详情
    const {deviceDetail: detail} = params;
    if (detail !== undefined) {
      const detailDict = JSON.parse(detail);
      if (isObject(detailDict)) {
        this.setDetail(detailDict);
      }
    }
  };

  /**
   * 设置详情数据
   * @param {DeviceDetailData} params
   */
  @action setDetail = params => {
    this.panel.deviceDetail = params;
  };

  /**
   * ota版本和小红点处理，首页OTA升级
   * @param {{showRed: boolean, otaVersion: string}}
   * @returns {void}
   */
  @action setOtaInfo = ({showRed = false, otaVersion = ''}) => {
    this.panel.showRed = showRed;
    otaVersion && (this.panel.otaVersion = otaVersion);
  };

  /**
   * 关闭DC充电模式的开关
   */
  @action closeDcDcModeSwitch = () => {
    const res = this.panel.resWithBle;
    // 1 reprent true in ble
    if (res[ModelMap.dc_dc_mode_switch] === 1) {
      res[ModelMap.dc_dc_mode_switch] = 0;
    }
  };

  /**
   * 更新蓝牙报上来的数据，如果不一样才更新
   * @param {array} parsedArray
   * @returns {void}
   */
  _updatehBleStoreData = parsedArray => {
    const res = this.panel.resWithBle;
    parsedArray.forEach(item => {
      const newData = item.dp_data;
      const oldData = res[`${item.dp_id}`];
      console.log('-----++++ before update data', item.dp_id, newData);
      if (isObservableArray(oldData)) {
        if (!isEqual(newData, toJS(oldData))) {
          res[`${item.dp_id}`] = newData;
          console.log('update array item', item.dp_id);
        }
        return;
      }
      if (isObservableObject(oldData)) {
        if (!isEqual(newData, toJS(oldData))) {
          res[`${item.dp_id}`] = newData;
          console.log('update object item', item.dp_id);
        }
        return;
      }
      if (!isEqual(newData, oldData)) {
        res[`${item.dp_id}`] = newData;
        console.log('-----++++ update plain data', item.dp_id, newData);
      }
    });
  };

  /**
   * 统一处理蓝牙返回，监听设备蓝牙数据返回，根据物模型定义解析数据
   * 为了避免无效的 parse，导致面板因为数据变化重复渲染，只有定义在物模型中 R N面板需要的数据（剩余电量，总使用时长等），才去 parse，其他的会被忽略
   * @param {object} res
   * @returns {void}
   */
  @action handleBleRes = res => {
    const array = JSON.parse(res);
    const toBeParsedDpIds = Object.values(ModelMap); // 待 parse 的物模型 id 列表
    const parsedArray = array
      .filter(item => {
        // 过滤掉未被 RN 定义和使用的物模型 id
        const id = Number.parseInt(item.dp_id, 16);
        return toBeParsedDpIds.includes(`${id}`);
      })
      .map(item => {
        const dp_id = String(Number.parseInt(item.dp_id, 16));
        const dp_type = Number.parseInt(item.dp_type, 16);
        const dp_data = item.dp_data;
        return {
          dp_id,
          dp_type,
          dp_data: DpDataUtils.parseDpData({
            dp_id,
            dp_type,
            dp_data,
          }),
        };
      });
    // 中断因为被过滤而导致没有数据的 parsedArray 数据
    if (parsedArray.length === 0) {
      return;
    }
    // console.log('handleBleRes--parsedArray-', JSON.stringify(parsedArray));
    // 更新结果
    this._updatehBleStoreData(parsedArray);
  };

  /**
   * 在通过命令更新设备影子之前，立刻更新属性的值
   * @param {Object} propertyData
   */
  _updateLocalData = propertyData => {
    const res = this.panel.resWithBle;
    const toBeUpdatedDpIds = Object.keys(propertyData);
    for (const dpId of toBeUpdatedDpIds) {
      const newData = propertyData[dpId];
      res[dpId] = newData;
    }
  };

  /**
   * 遥控模式走蓝牙协议,再此统一封装成需要的数据格式
   * @param {Array} data
   * @return {Promise} 用于外部promise调用
   */
  @action setLocalProperty = data => {
    return new Promise(resolve => {
      const jsonStr = JSON.stringify(data);
      console.log('jsonStr--setLocalProperty--', jsonStr);
      device.setLocalDeviceData(jsonStr).then(res => {
        resolve(res);
      });
    });
  };

  /**
   * 蓝牙是否连接
   * @param {boolean} value
   * @returns {void}
   */
  @action setBleConnected = value => {
    this.panel.bleConnected = value;
  };

  /**
   * 判断是否是pop到原生操作
   * @param {boolean} value
   */
  @action setIsNativeGoback = value => {
    this.panel.isNativeGoback = value;
  };

  /**
   * 设置强制升级标识
   * @param {boolean} value
   */
  @action setIsForceUpdate = value => {
    this.panel.isForceUpdate = value;
  };

  /**
   * 判断是否在RNContainerVC内
   * @param {boolean} value
   */
  @action setIsInRNContainerVC = value => {
    this.panel.isInRNContainerVC = value;
  };
  /**
   * 跳转ota升级原生页面
   * @see http://wiki.chervon.com.cn/pages/viewpage.action?pageId=22834912
   * @param {Object} options
   * @param {boolean} options.singleMcu - 是否为单MCU升级，目前除R项目均为单MCU升级
   * @param {number} options.upgradeModel - 升级模式，0：AWS升级(网络升级)   1：蓝牙升级
   * @param {boolean} options.isSubset - 是否为子设备，目前只有C的电池是子设备
   * @param {String} options.deviceId - deviceId
   * @param {function} callback
   */
  @action jumpToOtaUpdate = () => {
    const {panel} = this;
    JumpUtils.jumpTo('ChervonIot://Fleet/DeviceManage/OTAInfo', {
      deviceId: panel.deviceId,
      singleMcu: true,
      upgradeModel: 1,
      isSubset: false, // 通用，只有C的电池是子设备 这里设置为true，其他都是主设备,设置为false
    });
  };
  /**
   * @description 跳转到产品详情
   **/
  @action jumpToProductIntro = () => {
    const {panel} = this;
    JumpUtils.jumpTo('ChervonIot://Fleet/DeviceManage/productHelp', {
      productId: panel.productId,
    });
  };
  // 获取设备详情封装
  @action fetchDeviceDetail = () => {
    const {deviceId, appType = Platform.OS, appVersion = '1.1.0'} = this.panel;
    const params = {
      deviceId,
      appType,
      appVersion,
    };
    this.deviceDetail(params);
  };

  /**
   * 设置故障数据源
   */
  @action setFaultInfo = (info = {}) => {
    console.log('faultInfo--', JSON.stringify(info));
    this.panel.faultInfo = info;
  };
  /**
   * 拉取首页故障信息接口
   * @returns Promise
   */
  @action fetchFaultInfo = () => {
    fetchFaultInfo(this.panel, resObj => {
      this.setFaultInfo(resObj);
    });
  };
}
