import React from 'react';
import {View, TouchableOpacity} from 'react-native';

export default ({
  isInteractive = false,
  onPress = () => {},
  style = {},
  children,
  ...props
}) => {
  return (
    <>
      {isInteractive ? (
        <TouchableOpacity style={style} onPress={onPress} {...props}>
          {children}
        </TouchableOpacity>
      ) : (
        <View style={style} {...props}>
          {children}
        </View>
      )}
    </>
  );
};
