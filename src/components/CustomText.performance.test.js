/**
 * CustomText 组件性能测试
 * 验证字体名称缓存和样式过滤的性能优化效果
 */

import React from 'react';
import {render} from '@testing-library/react-native';
import CustomText, {FONT_WEIGHTS, FONT_FAMILY_TYPES} from './CustomText';

// 模拟性能测试工具
const measurePerformance = (testName, testFunction, iterations = 1000) => {
  const startTime = performance.now();

  for (let i = 0; i < iterations; i++) {
    testFunction();
  }

  const endTime = performance.now();
  const totalTime = endTime - startTime;
  const averageTime = totalTime / iterations;

  console.log(`${testName}:`);
  console.log(`  总时间: ${totalTime.toFixed(2)}ms`);
  console.log(`  平均时间: ${averageTime.toFixed(4)}ms`);
  console.log(`  迭代次数: ${iterations}`);
  console.log('---');

  return {totalTime, averageTime, iterations};
};

describe('CustomText Performance Tests', () => {
  beforeEach(() => {
    // 清理缓存以确保测试的准确性
    jest.clearAllMocks();
  });

  test('字体名称缓存性能测试', () => {
    const testCases = [
      {
        fontFamily: FONT_FAMILY_TYPES.WORK_SANS,
        fontWeight: FONT_WEIGHTS.REGULAR,
      },
      {fontFamily: FONT_FAMILY_TYPES.WORK_SANS, fontWeight: FONT_WEIGHTS.BOLD},
      {fontFamily: FONT_FAMILY_TYPES.RUBIK, fontWeight: FONT_WEIGHTS.MEDIUM},
      {fontFamily: FONT_FAMILY_TYPES.RUBIK, fontWeight: FONT_WEIGHTS.SEMI_BOLD},
    ];

    // 第一次渲染 - 缓存未命中
    const firstRenderResults = measurePerformance(
      '首次渲染 (缓存未命中)',
      () => {
        testCases.forEach(({fontFamily, fontWeight}) => {
          render(
            <CustomText fontFamily={fontFamily} fontWeight={fontWeight}>
              Test Text
            </CustomText>,
          );
        });
      },
      100,
    );

    // 第二次渲染 - 缓存命中
    const secondRenderResults = measurePerformance(
      '重复渲染 (缓存命中)',
      () => {
        testCases.forEach(({fontFamily, fontWeight}) => {
          render(
            <CustomText fontFamily={fontFamily} fontWeight={fontWeight}>
              Test Text
            </CustomText>,
          );
        });
      },
      100,
    );

    // 验证缓存优化效果
    expect(secondRenderResults.averageTime).toBeLessThan(
      firstRenderResults.averageTime,
    );

    // 性能改进应该至少有10%
    const improvementRatio =
      (firstRenderResults.averageTime - secondRenderResults.averageTime) /
      firstRenderResults.averageTime;
    expect(improvementRatio).toBeGreaterThan(0.1);
  });

  test('样式过滤性能测试', () => {
    const complexStyle = {
      color: 'red',
      fontSize: 18,
      fontFamily: 'Arial', // 这个会被过滤掉
      fontWeight: 'bold', // 这个会被过滤掉
      marginTop: 10,
      paddingHorizontal: 15,
      textAlign: 'center',
      lineHeight: 24,
    };

    const simpleStyle = {
      color: 'blue',
      fontSize: 16,
    };

    // 测试复杂样式过滤
    const complexStyleResults = measurePerformance(
      '复杂样式过滤',
      () => {
        render(
          <CustomText
            fontFamily={FONT_FAMILY_TYPES.WORK_SANS}
            fontWeight={FONT_WEIGHTS.BOLD}
            style={complexStyle}>
            Complex Style Text
          </CustomText>,
        );
      },
      500,
    );

    // 测试简单样式过滤
    const simpleStyleResults = measurePerformance(
      '简单样式过滤',
      () => {
        render(
          <CustomText
            fontFamily={FONT_FAMILY_TYPES.WORK_SANS}
            fontWeight={FONT_WEIGHTS.BOLD}
            style={simpleStyle}>
            Simple Style Text
          </CustomText>,
        );
      },
      500,
    );

    // 验证样式过滤不会显著影响性能
    const performanceDifference = Math.abs(
      complexStyleResults.averageTime - simpleStyleResults.averageTime,
    );
    expect(performanceDifference).toBeLessThan(0.1); // 差异应该小于0.1ms
  });

  test('大量组件渲染性能测试', () => {
    const componentCount = 100;

    const massRenderResults = measurePerformance(
      `大量组件渲染 (${componentCount}个组件)`,
      () => {
        const components = [];
        for (let i = 0; i < componentCount; i++) {
          const fontFamily =
            i % 2 === 0 ? FONT_FAMILY_TYPES.WORK_SANS : FONT_FAMILY_TYPES.RUBIK;
          const fontWeight = [
            FONT_WEIGHTS.REGULAR,
            FONT_WEIGHTS.MEDIUM,
            FONT_WEIGHTS.SEMI_BOLD,
            FONT_WEIGHTS.BOLD,
          ][i % 4];

          components.push(
            <CustomText
              key={i}
              fontFamily={fontFamily}
              fontWeight={fontWeight}
              style={{color: `hsl(${i * 3.6}, 70%, 50%)`}}>
              Text {i}
            </CustomText>,
          );
        }

        render(<>{components}</>);
      },
      10,
    );

    // 验证大量渲染的性能在可接受范围内
    expect(massRenderResults.averageTime).toBeLessThan(50); // 平均时间应该小于50ms
  });

  test('内存使用测试', () => {
    // 测试缓存是否会导致内存泄漏
    const initialMemory = process.memoryUsage().heapUsed;

    // 创建大量不同的字体组合
    for (let i = 0; i < 1000; i++) {
      const fontFamily =
        i % 2 === 0 ? FONT_FAMILY_TYPES.WORK_SANS : FONT_FAMILY_TYPES.RUBIK;
      const fontWeight = [
        FONT_WEIGHTS.REGULAR,
        FONT_WEIGHTS.MEDIUM,
        FONT_WEIGHTS.SEMI_BOLD,
        FONT_WEIGHTS.BOLD,
      ][i % 4];

      render(
        <CustomText fontFamily={fontFamily} fontWeight={fontWeight}>
          Memory Test {i}
        </CustomText>,
      );
    }

    const finalMemory = process.memoryUsage().heapUsed;
    const memoryIncrease = finalMemory - initialMemory;

    // 内存增长应该在合理范围内（小于10MB）
    expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);

    console.log(`内存使用测试:`);
    console.log(`  初始内存: ${(initialMemory / 1024 / 1024).toFixed(2)}MB`);
    console.log(`  最终内存: ${(finalMemory / 1024 / 1024).toFixed(2)}MB`);
    console.log(`  内存增长: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
  });
});

// 基准测试工具
export const runBenchmark = () => {
  console.log('🚀 CustomText 性能基准测试');
  console.log('================================');

  // 基准测试配置
  const benchmarkConfig = {
    warmupIterations: 50,
    testIterations: 200,
  };

  // 预热
  console.log('预热中...');
  for (let i = 0; i < benchmarkConfig.warmupIterations; i++) {
    render(
      <CustomText
        fontFamily={FONT_FAMILY_TYPES.WORK_SANS}
        fontWeight={FONT_WEIGHTS.REGULAR}>
        Warmup
      </CustomText>,
    );
  }

  // 基准测试
  const benchmarkResults = measurePerformance(
    '基准测试',
    () => {
      render(
        <CustomText
          fontFamily={FONT_FAMILY_TYPES.WORK_SANS}
          fontWeight={FONT_WEIGHTS.BOLD}
          style={{color: 'red', fontSize: 18}}>
          Benchmark Text
        </CustomText>,
      );
    },
    benchmarkConfig.testIterations,
  );

  console.log('✅ 基准测试完成');
  return benchmarkResults;
};

// 如果直接运行此文件，执行基准测试
if (require.main === module) {
  runBenchmark();
}
