import * as React from 'react';
import Svg, {G, Circle} from 'react-native-svg';

const MoreIcon = ({
  width = 21,
  height = 21,
  strokeColor = '#3C3936',
  ...props
}) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 42 42"
    {...props}>
    <G fill={strokeColor} fillRule="evenodd" transform="translate(17 1)">
      <Circle cx={4} cy={36.098} r={4} transform="rotate(-90 4 36.098)" />
      <Circle cx={4} cy={20.049} r={4} transform="rotate(-90 4 20.049)" />
      <Circle cx={4} cy={4} r={4} transform="rotate(-90 4 4)" />
    </G>
  </Svg>
);

export default MoreIcon;
