import React from 'react';
import Svg, {G, Circle, Path} from 'react-native-svg';

const TotalTimeIcon = ({width, height, ...props}) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 40 40"
    {...props}>
    <G fill="none" fillRule="evenodd">
      <G transform="translate(2 2)">
        <Circle cx={18} cy={18} r={16.5} stroke="#000" strokeWidth={3} />
        <Path stroke="#77BC1F" strokeWidth={4} d="M28 18H18V8" />
      </G>
      <Path d="M0 0h40v40H0z" />
    </G>
  </Svg>
);

export default TotalTimeIcon;
