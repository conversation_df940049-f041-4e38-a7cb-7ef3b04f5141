import React from 'react';
import Svg, {Path, Circle} from 'react-native-svg';

const UsageHistoryIcon = ({width, height, ...props}) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 57 54"
    fill="none"
    {...props}>
    <Path stroke="#3C3936" strokeWidth={5} d="M2.5 2.5h41v45h-41z" />
    <Path
      stroke="#3C3936"
      strokeLinecap="square"
      strokeWidth={5}
      d="M11.5 16.5H24M11.5 30h6.25"
    />
    <Circle
      cx={39.762}
      cy={36.262}
      r={14.262}
      fill="#fff"
      stroke="#77BC1F"
      strokeWidth={5}
    />
    <Path stroke="#77BC1F" strokeWidth={5} d="M47.25 36h-7.5v-7.5" />
  </Svg>
);

export default UsageHistoryIcon;
