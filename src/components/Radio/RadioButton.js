import React from 'react';
import {PixelRatio, Pressable, StyleSheet, View} from 'react-native';
import CustomText from '../CustomText';

const RadioButton = ({
  borderColor,
  color = '#77BC1F',
  inacticeColor = '#979797',
  containerStyle,
  disabled = false,
  id,
  label,
  labelStyle,
  layout = 'row',
  onPress,
  selected = false,
  size = 24,
  innerSize = 6,
  activeBorderSize = 7.5,
  inactiveBorderSize = 2,
}) => {
  const halfSize = PixelRatio.roundToNearestPixel(size * 0.5);
  const fullSize = PixelRatio.roundToNearestPixel(size);

  let orientationStyle = {flexDirection: 'row'};
  let margin = {marginLeft: 10};

  if (layout === 'column') {
    orientationStyle = {alignItems: 'center'};
    margin = {marginTop: 10};
  }

  const opacityStyle = disabled ? {opacity: 0.2} : {opacity: 1};

  function handlePress() {
    if (disabled) {
      return null;
    }
    if (onPress) {
      onPress(id);
    }
  }

  return (
    <Pressable
      onPress={handlePress}
      style={[
        styles.container,
        orientationStyle,
        containerStyle,
        opacityStyle,
      ]}>
      {selected ? (
        <View
          style={[
            styles.border,
            {
              borderColor: borderColor || color,
              borderWidth: activeBorderSize,
              width: fullSize,
              height: fullSize,
              borderRadius: halfSize,
            },
          ]}>
          <View
            style={[
              styles.whiteBg,
              {
                width: innerSize,
                height: innerSize,
                borderRadius: innerSize,
              },
            ]}
          />
        </View>
      ) : (
        <View
          style={[
            styles.border,
            {
              borderColor: inacticeColor || color,
              borderWidth: inactiveBorderSize,
              width: fullSize,
              height: fullSize,
              borderRadius: halfSize,
            },
          ]}
        />
      )}
      {Boolean(label) && (
        <CustomText style={[margin, labelStyle]}>{label}</CustomText>
      )}
    </Pressable>
  );
};

export default RadioButton;

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginHorizontal: 10,
    marginVertical: 5,
  },
  border: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  whiteBg: {
    backgroundColor: '#fff',
  },
});
