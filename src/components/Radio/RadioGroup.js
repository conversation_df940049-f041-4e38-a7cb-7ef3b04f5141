import React, {useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {isEqual} from 'lodash-es';

import RadioButton from './RadioButton';

const RadioGroup = ({
  containerStyle,
  layout = 'column',
  onPress,
  radioButtons,
  disabled = false,
}) => {
  const [radioButtonsLocal, setRadioButtonsLocal] = useState(radioButtons);

  if (!isEqual(radioButtons, radioButtonsLocal)) {
    setRadioButtonsLocal(radioButtons);
  }

  function handlePress(id) {
    for (const button of radioButtonsLocal) {
      if (button.selected && button.id === id) {
        return;
      }
      button.selected = button.id === id;
    }
    setRadioButtonsLocal([...radioButtonsLocal]);
    if (onPress) {
      onPress(radioButtonsLocal);
    }
  }

  return (
    <View style={[styles.container, {flexDirection: layout}, containerStyle]}>
      {radioButtonsLocal.map(button => (
        <RadioButton
          {...button}
          key={button.id}
          onPress={id => {
            if (disabled) {
              return;
            }
            handlePress(id);
            if (button.onPress && typeof button.onPress === 'function') {
              button.onPress(id);
            }
          }}
        />
      ))}
    </View>
  );
};

export default RadioGroup;

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
});
