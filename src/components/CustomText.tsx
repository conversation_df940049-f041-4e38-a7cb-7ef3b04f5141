import React, {memo} from 'react';
import {Text, StyleSheet, TextStyle, TextProps} from 'react-native';

// 字体权重类型定义
export type FontWeight = 400 | 500 | 600 | 700;

// 字体家族类型定义
export type FontFamily = 'WorkSans' | 'Rubik';

// 字体权重常量
export const FONT_WEIGHTS = {
  REGULAR: 400,
  MEDIUM: 500,
  SEMI_BOLD: 600,
  BOLD: 700,
} as const;

// 支持的字体家族
export const FONT_FAMILY_TYPES = {
  WORK_SANS: 'WorkSans',
  RUBIK: 'Rubik',
} as const;

// 字体名称映射表
const FONT_FAMILIES: Record<FontFamily, Record<FontWeight, string>> = {
  [FONT_FAMILY_TYPES.WORK_SANS]: {
    [FONT_WEIGHTS.REGULAR]: 'WorkSans-Regular',
    [FONT_WEIGHTS.MEDIUM]: 'WorkSans-Medium',
    [FONT_WEIGHTS.SEMI_BOLD]: 'WorkSans-SemiBold',
    [FONT_WEIGHTS.BOLD]: 'WorkSans-Bold',
  },
  [FONT_FAMILY_TYPES.RUBIK]: {
    [FONT_WEIGHTS.REGULAR]: 'Rubik-Regular',
    [FONT_WEIGHTS.MEDIUM]: 'Rubik-Medium',
    [FONT_WEIGHTS.SEMI_BOLD]: 'Rubik-SemiBold',
    // Rubik Bold 字体文件不存在，回退到 SemiBold
    [FONT_WEIGHTS.BOLD]: 'Rubik-SemiBold',
  },
} as const;

// 默认配置
const DEFAULT_CONFIG = {
  FONT_FAMILY: FONT_FAMILY_TYPES.WORK_SANS as FontFamily,
  FONT_WEIGHT: FONT_WEIGHTS.REGULAR as FontWeight,
  FONT_SIZE: 16,
} as const;

/**
 * 获取字体名称的工具函数
 * @param fontFamily - 字体家族
 * @param fontWeight - 字体权重
 * @returns 实际的字体文件名
 */
const getFontName = (fontFamily: FontFamily, fontWeight: FontWeight): string => {
  const fontMap = FONT_FAMILIES[fontFamily] || FONT_FAMILIES[DEFAULT_CONFIG.FONT_FAMILY];
  return fontMap[fontWeight] || fontMap[DEFAULT_CONFIG.FONT_WEIGHT];
};

// 组件属性接口
export interface CustomTextProps extends Omit<TextProps, 'style'> {
  /** 文本内容 */
  children: React.ReactNode;
  /** 字体家族，默认为 WorkSans */
  fontFamily?: FontFamily;
  /** 字体权重，默认为 400 */
  fontWeight?: FontWeight;
  /** 自定义样式 */
  style?: TextStyle | TextStyle[];
  /** 是否启用可访问性优化，默认为 true */
  accessibilityEnabled?: boolean;
}

/**
 * 自定义Text组件，支持根据字体和权重自动选择正确的字体文件
 * 
 * 特性：
 * - 🎨 支持多种字体家族和权重
 * - 📱 React Native 优化（移除额外padding，垂直居中）
 * - ♿ 可访问性支持
 * - 🔧 TypeScript 类型安全
 * - ⚡ 性能优化（memo包装）
 * 
 * @example
 * ```tsx
 * // 基础使用
 * <CustomText>Hello World</CustomText>
 * 
 * // 指定字体和权重
 * <CustomText fontFamily="Rubik" fontWeight={600}>Bold Text</CustomText>
 * 
 * // 自定义样式
 * <CustomText style={{color: 'red', fontSize: 18}}>Styled Text</CustomText>
 * 
 * // 可访问性支持
 * <CustomText accessibilityRole="header">Page Title</CustomText>
 * ```
 */
const CustomText: React.FC<CustomTextProps> = ({
  children,
  fontFamily = DEFAULT_CONFIG.FONT_FAMILY,
  fontWeight = DEFAULT_CONFIG.FONT_WEIGHT,
  style,
  accessibilityEnabled = true,
  ...rest
}) => {
  const fontName = getFontName(fontFamily, fontWeight);
  
  // 构建最终样式
  const finalStyle = [
    styles.text,
    {fontFamily: fontName},
    style,
  ];

  // 可访问性属性
  const accessibilityProps = accessibilityEnabled
    ? {
        accessible: true,
        accessibilityRole: rest.accessibilityRole || 'text',
      }
    : {};
  
  return (
    <Text 
      style={finalStyle}
      {...accessibilityProps}
      {...rest}
    >
      {children}
    </Text>
  );
};

// 默认样式
const styles = StyleSheet.create({
  text: {
    fontSize: DEFAULT_CONFIG.FONT_SIZE,
    color: '#000000', // 默认文本颜色
    includeFontPadding: false, // Android优化：移除额外的字体padding
    textAlignVertical: 'center', // Android优化：垂直居中对齐
  },
});

// 导出常量供外部使用
export {FONT_WEIGHTS, FONT_FAMILY_TYPES};

export default memo(CustomText);
