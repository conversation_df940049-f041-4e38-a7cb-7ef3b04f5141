# CustomText 组件

一个高度优化的React Native文本组件，支持多种字体家族和权重，具备完整的TypeScript类型支持和可访问性优化。

## ✨ 特性

- 🎨 **多字体支持**: 支持WorkSans和Rubik字体家族
- ⚖️ **多权重支持**: 支持400、500、600、700字体权重
- 📱 **React Native优化**: 移除额外padding，优化Android显示效果
- ♿ **可访问性支持**: 内置可访问性属性和角色支持
- 🔧 **TypeScript**: 完整的类型定义和类型安全
- ⚡ **性能优化**: 使用React.memo包装，避免不必要的重渲染
- 📚 **完整文档**: 详细的JSDoc注释和使用示例

## 🚀 快速开始

### 基础使用

```tsx
import CustomText from './components/CustomText';

// 默认样式
<CustomText>Hello World</CustomText>

// 指定字体和权重
<CustomText fontFamily="Rubik" fontWeight={600}>
  Bold Text
</CustomText>

// 自定义样式
<CustomText style={{color: 'red', fontSize: 18}}>
  Styled Text
</CustomText>
```

### 使用常量

```tsx
import CustomText, {FONT_WEIGHTS, FONT_FAMILY_TYPES} from './components/CustomText';

<CustomText 
  fontFamily={FONT_FAMILY_TYPES.WORK_SANS}
  fontWeight={FONT_WEIGHTS.BOLD}
>
  使用常量定义的文本
</CustomText>
```

## 📋 API 参考

### Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `children` | `React.ReactNode` | - | 文本内容 (必需) |
| `fontFamily` | `'WorkSans' \| 'Rubik'` | `'WorkSans'` | 字体家族 |
| `fontWeight` | `400 \| 500 \| 600 \| 700` | `400` | 字体权重 |
| `style` | `TextStyle \| TextStyle[]` | - | 自定义样式 |
| `accessibilityEnabled` | `boolean` | `true` | 是否启用可访问性优化 |
| `...rest` | `TextProps` | - | 其他Text组件支持的属性 |

### 常量

```tsx
// 字体权重常量
export const FONT_WEIGHTS = {
  REGULAR: 400,
  MEDIUM: 500,
  SEMI_BOLD: 600,
  BOLD: 700,
} as const;

// 字体家族常量
export const FONT_FAMILY_TYPES = {
  WORK_SANS: 'WorkSans',
  RUBIK: 'Rubik',
} as const;
```

## 🎯 使用场景

### 1. 标题文本
```tsx
<CustomText 
  fontWeight={FONT_WEIGHTS.BOLD}
  style={{fontSize: 24, color: '#333'}}
  accessibilityRole="header"
>
  页面标题
</CustomText>
```

### 2. 正文内容
```tsx
<CustomText 
  fontFamily={FONT_FAMILY_TYPES.WORK_SANS}
  fontWeight={FONT_WEIGHTS.REGULAR}
  style={{fontSize: 16, lineHeight: 24}}
>
  这是正文内容，使用常规字体权重...
</CustomText>
```

### 3. 强调文本
```tsx
<CustomText 
  fontWeight={FONT_WEIGHTS.SEMI_BOLD}
  style={{color: '#007AFF'}}
>
  重要信息
</CustomText>
```

### 4. 错误提示
```tsx
<CustomText 
  fontWeight={FONT_WEIGHTS.MEDIUM}
  style={{color: '#FF3B30', fontSize: 14}}
  accessibilityRole="alert"
>
  错误：请检查输入内容
</CustomText>
```

## 🔧 技术实现

### 字体映射机制

组件内部维护了一个字体映射表，根据传入的`fontFamily`和`fontWeight`自动选择正确的字体文件：

```tsx
const FONT_FAMILIES = {
  WorkSans: {
    400: 'WorkSans-Regular',
    500: 'WorkSans-Medium',
    600: 'WorkSans-SemiBold',
    700: 'WorkSans-Bold',
  },
  Rubik: {
    400: 'Rubik-Regular',
    500: 'Rubik-Medium',
    600: 'Rubik-SemiBold',
    700: 'Rubik-SemiBold', // 回退到SemiBold
  },
};
```

### 性能优化

1. **React.memo**: 组件使用memo包装，只在props变化时重新渲染
2. **样式优化**: 预定义样式对象，避免运行时创建
3. **字体缓存**: 字体名称通过纯函数计算，支持缓存优化

### Android优化

```tsx
const styles = StyleSheet.create({
  text: {
    includeFontPadding: false, // 移除额外的字体padding
    textAlignVertical: 'center', // 垂直居中对齐
  },
});
```

## ♿ 可访问性

组件内置了可访问性支持：

- 默认启用`accessible`属性
- 支持`accessibilityRole`设置
- 支持所有标准的可访问性属性
- 可通过`accessibilityEnabled`属性控制

## 🧪 测试

建议的测试用例：

```tsx
// 基础渲染测试
test('renders with default props', () => {
  render(<CustomText>Test</CustomText>);
});

// 字体属性测试
test('applies correct font family and weight', () => {
  const {getByText} = render(
    <CustomText fontFamily="Rubik" fontWeight={600}>
      Test
    </CustomText>
  );
  // 验证样式应用
});

// 可访问性测试
test('supports accessibility props', () => {
  render(
    <CustomText accessibilityRole="header">
      Header Text
    </CustomText>
  );
});
```

## 📝 更新日志

### v2.0.0 (优化版本)
- ✅ 添加完整的TypeScript支持
- ✅ 优化性能，移除不必要的useCallback
- ✅ 改进可访问性支持
- ✅ 添加常量导出
- ✅ 完善文档和示例
- ✅ Android显示优化

### v1.0.0 (原始版本)
- ✅ 基础字体支持
- ✅ PropTypes验证
- ✅ 基础样式支持

## 🤝 贡献指南

1. 确保所有新功能都有TypeScript类型定义
2. 添加相应的测试用例
3. 更新文档和示例
4. 遵循现有的代码风格和命名约定
