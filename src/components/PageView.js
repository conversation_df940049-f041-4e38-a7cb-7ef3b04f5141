import React, {Component} from 'react';
import {StyleSheet} from 'react-native';
import {SafeAreaProvider} from 'react-native-safe-area-context';

// Set basic view style
export default class PageView extends Component {
  render() {
    const {children, style = {}, ...restProps} = this.props;
    return (
      <SafeAreaProvider style={[styles.container, style]} {...restProps}>
        {children}
      </SafeAreaProvider>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F7F7',
  },
});
