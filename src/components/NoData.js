import React, {memo} from 'react';
import {View, Text, Image} from 'react-native';
import CustomStyleSheet from '@utils/style/index.js';
import {getImageUrl} from '@utils/image.js';
import {useTranslation} from 'react-i18next';
import {geti18nText} from '@i18n/config';

const NoData = ({
  content = '',
  showImage = true,
  style = {},
  contentStyle = {},
}) => {
  const {t} = useTranslation('all');
  return (
    <View style={[styles.container, style]}>
      {showImage && (
        <Image
          source={{
            uri: getImageUrl('common_nodata'),
          }}
          style={styles.blank}
        />
      )}
      <Text style={[styles.text, contentStyle]}>
        {content || t(geti18nText('nodata_title_textview_text', true))}
      </Text>
    </View>
  );
};

const styles = CustomStyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  blank: {
    width: 94,
    height: 121,
    marginBottom: 5,
  },
  text: {
    fontSize: 15,
    lineHeight: 18,
    color: '#999999',
  },
});

export default memo(NoData);
