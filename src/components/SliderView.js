import React from 'react';
import {Text, View} from 'react-native';
import {Slider} from './Slider';
import CustomStyleSheet from '@utils/style/index.js';

const SliderView = ({
  value,
  onChange = () => {},
  onSlidingComplete = () => {},
  style = {},
  unit = 'm',
  minimumValue,
  maximumValue,
  ...rest
}) => {
  const isShowMarker = value > minimumValue && value < maximumValue;

  return (
    <View style={[styles.wrapper, style]}>
      <Text style={styles.min}>
        {minimumValue}
        {unit}
      </Text>
      <Text style={styles.max}>
        {maximumValue}
        {unit}
      </Text>

      <Slider
        onSlidingComplete={onSlidingComplete}
        style={{flex: 1, height: 33}}
        value={value}
        onValueChange={onChange}
        minimumValue={minimumValue}
        maximumValue={maximumValue}
        thumbTouchSize={{width: 20, height: 20}}
        minimumTrackTintColor="#77BC1F"
        maximumTrackTintColor="#D8D8D8"
        trackStyle={styles.track}
        thumbStyle={{height: 25, width: 25, backgroundColor: 'transparent'}}
        thumbProps={{
          children: (
            <View style={[styles.outerThumb, styles.shadow]}>
              <View style={styles.innerThumb} />
              {isShowMarker ? (
                <View style={styles.marker}>
                  <Text
                    style={{
                      color: '#fff',
                      fontSize: 18,
                      lineHeight: 21.5,
                      fontWeight: 'bold',
                    }}>
                    {value}
                    {unit}
                  </Text>
                </View>
              ) : null}
            </View>
          ),
        }}
        {...rest}
      />
    </View>
  );
};
export default SliderView;

export const styles = CustomStyleSheet.create({
  wrapper: {
    position: 'relative',
    flexDirection: 'row',
    height: 60,
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
  },
  min: {
    position: 'absolute',
    left: 0,
    top: 17.5,
    fontSize: 12,
    lineHeight: 14.5,
    color: '#000',
  },
  max: {
    position: 'absolute',
    right: 0,
    top: 17.5,
    fontSize: 12,
    color: '#000',
  },
  track: {
    height: 6,
    backgroundColor: '#0000ff',
    borderRadius: 3,
  },
  shadow: {
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowRadius: 5.5,
    shadowOpacity: 0.1,
    shadowColor: '#000000',
    elevation: 1,
  },
  extra: {
    marginLeft: 20,
    width: 50,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  text: {
    color: '#77BC1F',
    fontSize: 17,
    marginRight: 5,
  },
  outerThumb: {
    width: 25,
    height: 25,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12.5,
    position: 'relative',
  },
  innerThumb: {
    width: 18,
    height: 18,
    backgroundColor: '#77BC1F',
    borderRadius: 9,
  },
  marker: {
    position: 'absolute',
    backgroundColor: '#77BC1F',
    height: 25,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12.5,
    top: -32,
    width: 58,
  },
});
