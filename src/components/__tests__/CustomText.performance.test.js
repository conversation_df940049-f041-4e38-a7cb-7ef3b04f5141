/**
 * CustomText 组件性能测试
 * 专门测试字体缓存机制和渲染性能
 */

import React from 'react';
import {render} from '@testing-library/react-native';
import CustomText, {FONT_WEIGHTS, FONT_FAMILY_TYPES} from '../CustomText';

// 性能测试工具函数
const measureRenderTime = (renderFunction, iterations = 100) => {
  const startTime = performance.now();

  for (let i = 0; i < iterations; i++) {
    renderFunction();
  }

  const endTime = performance.now();
  return {
    totalTime: endTime - startTime,
    averageTime: (endTime - startTime) / iterations,
    iterations,
  };
};

// 清理函数 - 重置缓存（暂时未使用，但保留供将来扩展）
// const clearFontCache = () => {
//   // 通过重新导入模块来清理缓存
//   jest.resetModules();
// };

describe('CustomText Performance Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('字体名称缓存测试', () => {
    test('缓存应该提升重复渲染的性能', () => {
      const testProps = {
        fontFamily: FONT_FAMILY_TYPES.WORK_SANS,
        fontWeight: FONT_WEIGHTS.BOLD,
      };

      // 第一次渲染 - 缓存未命中
      const firstRender = measureRenderTime(() => {
        render(<CustomText {...testProps}>Cache Test Text</CustomText>);
      }, 50);

      // 第二次渲染 - 缓存命中
      const secondRender = measureRenderTime(() => {
        render(<CustomText {...testProps}>Cache Test Text</CustomText>);
      }, 50);

      // 缓存命中应该更快（允许一定的误差范围）
      expect(secondRender.averageTime).toBeLessThanOrEqual(
        firstRender.averageTime * 1.1, // 允许10%的误差
      );
    });

    test('不同字体组合应该被正确缓存', () => {
      const fontCombinations = [
        {
          fontFamily: FONT_FAMILY_TYPES.WORK_SANS,
          fontWeight: FONT_WEIGHTS.REGULAR,
        },
        {
          fontFamily: FONT_FAMILY_TYPES.WORK_SANS,
          fontWeight: FONT_WEIGHTS.BOLD,
        },
        {fontFamily: FONT_FAMILY_TYPES.RUBIK, fontWeight: FONT_WEIGHTS.MEDIUM},
        {
          fontFamily: FONT_FAMILY_TYPES.RUBIK,
          fontWeight: FONT_WEIGHTS.SEMI_BOLD,
        },
      ];

      // 第一次渲染所有组合
      const renderFontCombinations = combinations => {
        combinations.forEach(props => {
          const key = `${props.fontFamily}-${props.fontWeight}`;
          render(
            <CustomText key={key} {...props}>
              Font Combination {key}
            </CustomText>,
          );
        });
      };

      const firstRender = measureRenderTime(() => {
        renderFontCombinations(fontCombinations);
      }, 20);

      // 第二次渲染相同组合
      const secondRender = measureRenderTime(() => {
        renderFontCombinations(fontCombinations);
      }, 20);

      // 第二次应该更快
      expect(secondRender.averageTime).toBeLessThan(firstRender.averageTime);
    });

    test('缓存应该处理大量不同的字体组合', () => {
      const generateRandomProps = () => {
        const families = Object.values(FONT_FAMILY_TYPES);
        const weights = Object.values(FONT_WEIGHTS);

        return {
          fontFamily: families[Math.floor(Math.random() * families.length)],
          fontWeight: weights[Math.floor(Math.random() * weights.length)],
        };
      };

      // 生成100个随机字体组合
      const randomCombinations = Array.from({length: 100}, generateRandomProps);

      const renderRandomCombinations = combinations => {
        combinations.forEach((props, index) => {
          const key = `random-${props.fontFamily}-${props.fontWeight}-${index}`;
          render(
            <CustomText key={key} {...props}>
              Random Font {index}
            </CustomText>,
          );
        });
      };

      const renderTime = measureRenderTime(() => {
        renderRandomCombinations(randomCombinations);
      }, 5);

      // 确保大量渲染在合理时间内完成
      expect(renderTime.averageTime).toBeLessThan(100); // 平均每次渲染小于100ms
    });
  });

  describe('样式处理性能测试', () => {
    test('样式过滤不应该显著影响性能', () => {
      const simpleStyle = {
        color: 'red',
        fontSize: 16,
      };

      const complexStyle = {
        color: 'blue',
        fontSize: 18,
        fontFamily: 'Arial', // 会被过滤
        fontWeight: 'bold', // 会被过滤
        marginTop: 10,
        paddingHorizontal: 15,
        textAlign: 'center',
        lineHeight: 24,
        textDecorationLine: 'underline',
        textShadowColor: 'gray',
        textShadowOffset: {width: 1, height: 1},
        textShadowRadius: 2,
      };

      // 测试简单样式
      const simpleStyleRender = measureRenderTime(() => {
        render(<CustomText style={simpleStyle}>Simple Style</CustomText>);
      }, 100);

      // 测试复杂样式
      const complexStyleRender = measureRenderTime(() => {
        render(<CustomText style={complexStyle}>Complex Style</CustomText>);
      }, 100);

      // 复杂样式处理时间不应该显著增加
      const timeDifference = Math.abs(
        complexStyleRender.averageTime - simpleStyleRender.averageTime,
      );
      expect(timeDifference).toBeLessThan(0.5); // 差异小于0.5ms
    });

    test('样式数组处理性能', () => {
      const styleArray = [
        {color: 'red'},
        {fontSize: 16},
        {marginTop: 10},
        {fontFamily: 'Arial'}, // 会被过滤
        {textAlign: 'center'},
      ];

      const singleStyle = {
        color: 'red',
        fontSize: 16,
        marginTop: 10,
        textAlign: 'center',
      };

      // 测试样式数组
      const arrayStyleRender = measureRenderTime(() => {
        render(<CustomText style={styleArray}>Array Style</CustomText>);
      }, 100);

      // 测试单一样式对象
      const singleStyleRender = measureRenderTime(() => {
        render(<CustomText style={singleStyle}>Single Style</CustomText>);
      }, 100);

      // 样式数组处理不应该显著慢于单一样式
      expect(arrayStyleRender.averageTime).toBeLessThan(
        singleStyleRender.averageTime * 2,
      );
    });
  });

  describe('大规模渲染性能测试', () => {
    test('大量组件同时渲染性能', () => {
      const componentCount = 50;

      const massRender = measureRenderTime(() => {
        const components = [];
        for (let i = 0; i < componentCount; i++) {
          const fontFamily =
            i % 2 === 0 ? FONT_FAMILY_TYPES.WORK_SANS : FONT_FAMILY_TYPES.RUBIK;
          const fontWeight = [
            FONT_WEIGHTS.REGULAR,
            FONT_WEIGHTS.MEDIUM,
            FONT_WEIGHTS.SEMI_BOLD,
            FONT_WEIGHTS.BOLD,
          ][i % 4];

          components.push(
            <CustomText
              key={i}
              fontFamily={fontFamily}
              fontWeight={fontWeight}
              style={{color: `hsl(${i * 7}, 70%, 50%)`}}>
              Component {i}
            </CustomText>,
          );
        }

        render(<>{components}</>);
      }, 10);

      // 大量渲染应该在合理时间内完成
      expect(massRender.averageTime).toBeLessThan(50); // 平均小于50ms
    });

    test('嵌套组件渲染性能', () => {
      const nestedDepth = 10;

      const createNestedComponents = depth => {
        if (depth === 0) {
          return <CustomText>Nested Text {depth}</CustomText>;
        }

        return (
          <CustomText
            fontFamily={
              depth % 2 === 0
                ? FONT_FAMILY_TYPES.WORK_SANS
                : FONT_FAMILY_TYPES.RUBIK
            }
            fontWeight={[FONT_WEIGHTS.REGULAR, FONT_WEIGHTS.BOLD][depth % 2]}>
            Level {depth} {createNestedComponents(depth - 1)}
          </CustomText>
        );
      };

      const nestedRender = measureRenderTime(() => {
        render(createNestedComponents(nestedDepth));
      }, 20);

      // 嵌套渲染应该在合理时间内完成
      expect(nestedRender.averageTime).toBeLessThan(20); // 平均小于20ms
    });
  });

  describe('内存使用测试', () => {
    test('缓存不应该导致内存泄漏', () => {
      const initialMemory = process.memoryUsage().heapUsed;

      // 创建大量不同的字体组合来填充缓存
      for (let i = 0; i < 1000; i++) {
        const fontFamily =
          i % 2 === 0 ? FONT_FAMILY_TYPES.WORK_SANS : FONT_FAMILY_TYPES.RUBIK;
        const fontWeight = [
          FONT_WEIGHTS.REGULAR,
          FONT_WEIGHTS.MEDIUM,
          FONT_WEIGHTS.SEMI_BOLD,
          FONT_WEIGHTS.BOLD,
        ][i % 4];

        render(
          <CustomText fontFamily={fontFamily} fontWeight={fontWeight}>
            Memory Test {i}
          </CustomText>,
        );
      }

      // 强制垃圾回收（如果可用）
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;

      // 内存增长应该在合理范围内（小于5MB）
      expect(memoryIncrease).toBeLessThan(5 * 1024 * 1024);
    });

    test('缓存大小应该有限制', () => {
      // 由于只有2个字体家族和4个权重，最多应该有8个缓存条目
      const maxExpectedCacheSize = 8;

      // 验证缓存大小不会超过预期
      expect(maxExpectedCacheSize).toBe(8);

      // 渲染所有可能的字体组合
      const renderAllFontCombinations = testText => {
        Object.values(FONT_FAMILY_TYPES).forEach(fontFamily => {
          Object.values(FONT_WEIGHTS).forEach(fontWeight => {
            render(
              <CustomText fontFamily={fontFamily} fontWeight={fontWeight}>
                {testText}
              </CustomText>,
            );
          });
        });
      };

      renderAllFontCombinations('Cache Size Test');

      // 这里我们无法直接访问缓存大小，但可以通过渲染性能来推断
      // 如果缓存工作正常，重复渲染应该很快
      const repeatRender = measureRenderTime(() => {
        renderAllFontCombinations('Cache Size Test Repeat');
      }, 50);

      // 重复渲染应该很快（缓存命中）
      expect(repeatRender.averageTime).toBeLessThan(5); // 平均小于5ms
    });
  });
});
