#!/usr/bin/env node

/**
 * CustomText 组件测试运行脚本
 * 提供便捷的测试运行和报告功能
 */

const {execSync} = require('child_process');
const path = require('path');
const fs = require('fs');

// 颜色输出工具
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

const log = {
  info: msg => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: msg => console.log(`${colors.green}✓${colors.reset} ${msg}`),
  warning: msg => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
  error: msg => console.log(`${colors.red}✗${colors.reset} ${msg}`),
  header: msg =>
    console.log(`\n${colors.bright}${colors.cyan}${msg}${colors.reset}\n`),
};

// 测试配置
const testConfig = {
  testDir: __dirname,
  coverageDir: path.join(__dirname, '../../../coverage'),
  testFiles: {
    unit: 'CustomText.test.js',
    performance: 'CustomText.performance.test.js',
    integration: 'CustomText.integration.test.js',
  },
};

// 检查依赖
function checkDependencies() {
  log.header('检查测试依赖');

  const requiredPackages = [
    '@testing-library/react-native',
    '@testing-library/jest-native',
    'jest',
  ];

  const packageJsonPath = path.join(__dirname, '../../../package.json');

  if (!fs.existsSync(packageJsonPath)) {
    log.error('找不到 package.json 文件');
    return false;
  }

  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  const allDeps = {
    ...packageJson.dependencies,
    ...packageJson.devDependencies,
  };

  const missingPackages = requiredPackages.filter(pkg => !allDeps[pkg]);

  if (missingPackages.length > 0) {
    log.error(`缺少以下依赖包: ${missingPackages.join(', ')}`);
    log.info('请运行: npm install --save-dev ' + missingPackages.join(' '));
    return false;
  }

  log.success('所有依赖检查通过');
  return true;
}

// 运行特定测试
function runTest(testType, options = {}) {
  const {verbose = false, coverage = false, watch = false} = options;

  log.header(`运行 ${testType} 测试`);

  const testFile = testConfig.testFiles[testType];
  if (!testFile) {
    log.error(`未知的测试类型: ${testType}`);
    return false;
  }

  const testPath = path.join(testConfig.testDir, testFile);
  if (!fs.existsSync(testPath)) {
    log.error(`测试文件不存在: ${testPath}`);
    return false;
  }

  let jestCommand = `npx jest ${testPath}`;

  if (verbose) {
    jestCommand += ' --verbose';
  }

  if (coverage) {
    jestCommand += ` --coverage --coverageDirectory=${testConfig.coverageDir}`;
  }

  if (watch) {
    jestCommand += ' --watch';
  }

  try {
    log.info(`执行命令: ${jestCommand}`);
    execSync(jestCommand, {
      stdio: 'inherit',
      cwd: path.join(__dirname, '../../..'),
    });
    log.success(`${testType} 测试完成`);
    return true;
  } catch (error) {
    log.error(`${testType} 测试失败`);
    return false;
  }
}

// 运行所有测试
function runAllTests(options = {}) {
  log.header('运行所有 CustomText 测试');

  const testTypes = Object.keys(testConfig.testFiles);
  const results = {};

  for (const testType of testTypes) {
    log.info(`开始 ${testType} 测试...`);
    results[testType] = runTest(testType, options);
  }

  // 输出总结
  log.header('测试结果总结');

  let allPassed = true;
  for (const [testType, passed] of Object.entries(results)) {
    if (passed) {
      log.success(`${testType}: 通过`);
    } else {
      log.error(`${testType}: 失败`);
      allPassed = false;
    }
  }

  if (allPassed) {
    log.success('所有测试通过！');
  } else {
    log.error('部分测试失败');
  }

  return allPassed;
}

// 生成测试报告
function generateReport() {
  log.header('生成测试报告');

  const reportCommand = `npx jest ${testConfig.testDir} --coverage --coverageDirectory=${testConfig.coverageDir} --coverageReporters=html,text,lcov`;

  try {
    execSync(reportCommand, {
      stdio: 'inherit',
      cwd: path.join(__dirname, '../../..'),
    });
    log.success('测试报告生成完成');
    log.info(`报告位置: ${testConfig.coverageDir}/lcov-report/index.html`);
    return true;
  } catch (error) {
    log.error('测试报告生成失败');
    return false;
  }
}

// 性能基准测试
function runBenchmark() {
  log.header('运行性能基准测试');

  const benchmarkCommand = `npx jest ${path.join(
    testConfig.testDir,
    testConfig.testFiles.performance,
  )} --verbose`;

  try {
    execSync(benchmarkCommand, {
      stdio: 'inherit',
      cwd: path.join(__dirname, '../../..'),
    });
    log.success('性能基准测试完成');
    return true;
  } catch (error) {
    log.error('性能基准测试失败');
    return false;
  }
}

// 清理测试缓存
function cleanCache() {
  log.header('清理测试缓存');

  try {
    execSync('npx jest --clearCache', {
      stdio: 'inherit',
      cwd: path.join(__dirname, '../../..'),
    });
    log.success('测试缓存清理完成');
    return true;
  } catch (error) {
    log.error('测试缓存清理失败');
    return false;
  }
}

// 命令行接口
function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  // 解析选项
  const options = {
    verbose: args.includes('--verbose') || args.includes('-v'),
    coverage: args.includes('--coverage') || args.includes('-c'),
    watch: args.includes('--watch') || args.includes('-w'),
  };

  // 首先检查依赖
  if (!checkDependencies()) {
    process.exit(1);
  }

  switch (command) {
    case 'unit':
    case 'performance':
    case 'integration':
      runTest(command, options);
      break;

    case 'all':
      runAllTests(options);
      break;

    case 'report':
      generateReport();
      break;

    case 'benchmark':
      runBenchmark();
      break;

    case 'clean':
      cleanCache();
      break;

    case 'help':
    case '--help':
    case '-h':
    default:
      console.log(`
${colors.bright}CustomText 测试运行器${colors.reset}

用法: node runTests.js <command> [options]

命令:
  unit          运行单元测试
  performance   运行性能测试
  integration   运行集成测试
  all           运行所有测试
  report        生成测试覆盖率报告
  benchmark     运行性能基准测试
  clean         清理测试缓存
  help          显示此帮助信息

选项:
  --verbose, -v    详细输出
  --coverage, -c   生成覆盖率报告
  --watch, -w      监视模式

示例:
  node runTests.js all --coverage
  node runTests.js unit --verbose
  node runTests.js performance
  node runTests.js report
      `);
      break;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  runTest,
  runAllTests,
  generateReport,
  runBenchmark,
  cleanCache,
};
