/**
 * Jest 测试环境配置
 * 为 CustomText 组件测试设置必要的模拟和配置
 */

import 'react-native-testing-library/extend-expect';

// 模拟 React Native 的 StyleSheet
jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native');

  return {
    ...RN,
    StyleSheet: {
      ...RN.StyleSheet,
      create: jest.fn(styles => styles),
      flatten: jest.fn(style => {
        if (!style) {
          return {};
        }
        if (Array.isArray(style)) {
          return Object.assign({}, ...style.filter(Boolean));
        }
        return style;
      }),
    },
    // 模拟 Text 组件
    Text: jest.fn().mockImplementation(({children, ...props}) => ({
      type: 'Text',
      props: {children, ...props},
      children,
    })),
  };
});

// 模拟 performance API（如果不存在）
if (!global.performance) {
  global.performance = {
    now: jest.fn(() => Date.now()),
  };
}

// 模拟 console 方法以避免测试输出污染
const originalConsole = global.console;

beforeEach(() => {
  // 重置所有模拟
  jest.clearAllMocks();

  // 模拟 console.error 以捕获 PropTypes 警告
  global.console = {
    ...originalConsole,
    error: jest.fn(),
    warn: jest.fn(),
    log: jest.fn(),
  };
});

afterEach(() => {
  // 恢复原始 console
  global.console = originalConsole;
});

// 全局测试超时设置
jest.setTimeout(10000);

// 模拟字体加载（如果需要）
global.FontFace = jest.fn().mockImplementation(() => ({
  load: jest.fn().mockResolvedValue(),
}));

// 添加自定义匹配器
expect.extend({
  toHaveStyleProperty(received, property, value) {
    const pass = received.props.style?.some(
      style => style?.[property] === value,
    );

    if (pass) {
      return {
        message: () =>
          `expected element not to have style property ${property} with value ${value}`,
        pass: true,
      };
    } else {
      return {
        message: () =>
          `expected element to have style property ${property} with value ${value}`,
        pass: false,
      };
    }
  },

  toHaveFontFamily(received, fontFamily) {
    const styles = received.props.style;
    const flattenedStyle = Array.isArray(styles)
      ? Object.assign({}, ...styles.filter(Boolean))
      : styles || {};

    const pass = flattenedStyle.fontFamily === fontFamily;

    if (pass) {
      return {
        message: () => `expected element not to have fontFamily ${fontFamily}`,
        pass: true,
      };
    } else {
      return {
        message: () =>
          `expected element to have fontFamily ${fontFamily}, but got ${flattenedStyle.fontFamily}`,
        pass: false,
      };
    }
  },

  toBeWithinPerformanceRange(received, baseline, tolerance = 0.1) {
    const difference = Math.abs(received - baseline);
    const allowedDifference = baseline * tolerance;
    const pass = difference <= allowedDifference;

    if (pass) {
      return {
        message: () =>
          `expected ${received} not to be within ${
            tolerance * 100
          }% of ${baseline}`,
        pass: true,
      };
    } else {
      return {
        message: () =>
          `expected ${received} to be within ${
            tolerance * 100
          }% of ${baseline}`,
        pass: false,
      };
    }
  },
});

// 全局测试工具函数
global.testUtils = {
  // 创建测试渲染器
  createTestRenderer: component => {
    return {
      getInstance: () => component,
      toJSON: () => component,
    };
  },

  // 等待异步操作
  waitFor: async (callback, timeout = 1000) => {
    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      try {
        const result = await callback();
        if (result) {
          return result;
        }
      } catch {
        // 继续等待，忽略错误
      }

      await new Promise(resolve => {
        setTimeout(resolve, 10);
      });
    }

    throw new Error(`Timeout waiting for condition after ${timeout}ms`);
  },

  // 模拟延迟
  delay: ms =>
    new Promise(resolve => {
      setTimeout(resolve, ms);
    }),
};

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// 测试环境信息
console.log('Jest setup completed for CustomText component tests');
console.log(
  'Available custom matchers: toHaveStyleProperty, toHaveFontFamily, toBeWithinPerformanceRange',
);
console.log('Global testUtils available with helper functions');
