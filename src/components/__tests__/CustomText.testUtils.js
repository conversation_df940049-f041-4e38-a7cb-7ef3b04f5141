/**
 * CustomText 测试工具函数
 * 提供测试中常用的辅助函数和模拟数据
 */

import React from 'react';
import {render} from '@testing-library/react-native';
import {FONT_WEIGHTS, FONT_FAMILY_TYPES} from '../CustomText';

/**
 * 性能测试工具
 */
export const performanceUtils = {
  /**
   * 测量渲染时间
   * @param {Function} renderFunction - 渲染函数
   * @param {number} iterations - 迭代次数
   * @returns {Object} 性能测试结果
   */
  measureRenderTime: (renderFunction, iterations = 100) => {
    const startTime = performance.now();

    for (let i = 0; i < iterations; i++) {
      renderFunction();
    }

    const endTime = performance.now();
    return {
      totalTime: endTime - startTime,
      averageTime: (endTime - startTime) / iterations,
      iterations,
    };
  },

  /**
   * 比较两个性能测试结果
   * @param {Object} baseline - 基准测试结果
   * @param {Object} comparison - 对比测试结果
   * @returns {Object} 比较结果
   */
  comparePerformance: (baseline, comparison) => {
    const improvement = baseline.averageTime - comparison.averageTime;
    const improvementPercentage = (improvement / baseline.averageTime) * 100;

    return {
      improvement,
      improvementPercentage,
      isFaster: improvement > 0,
      isSlower: improvement < 0,
      isSignificant: Math.abs(improvementPercentage) > 5, // 5%以上认为是显著差异
    };
  },

  /**
   * 内存使用测试
   * @param {Function} testFunction - 测试函数
   * @returns {Object} 内存使用情况
   */
  measureMemoryUsage: testFunction => {
    const initialMemory = process.memoryUsage();

    testFunction();

    // 强制垃圾回收（如果可用）
    if (global.gc) {
      global.gc();
    }

    const finalMemory = process.memoryUsage();

    return {
      heapUsedDelta: finalMemory.heapUsed - initialMemory.heapUsed,
      heapTotalDelta: finalMemory.heapTotal - initialMemory.heapTotal,
      externalDelta: finalMemory.external - initialMemory.external,
      initialMemory,
      finalMemory,
    };
  },
};

/**
 * 测试数据生成器
 */
export const testDataGenerators = {
  /**
   * 生成随机字体属性
   * @returns {Object} 随机字体属性
   */
  randomFontProps: () => {
    const families = Object.values(FONT_FAMILY_TYPES);
    const weights = Object.values(FONT_WEIGHTS);

    return {
      fontFamily: families[Math.floor(Math.random() * families.length)],
      fontWeight: weights[Math.floor(Math.random() * weights.length)],
    };
  },

  /**
   * 生成所有可能的字体组合
   * @returns {Array} 字体组合数组
   */
  allFontCombinations: () => {
    const combinations = [];
    Object.values(FONT_FAMILY_TYPES).forEach(fontFamily => {
      Object.values(FONT_WEIGHTS).forEach(fontWeight => {
        combinations.push({fontFamily, fontWeight});
      });
    });
    return combinations;
  },

  /**
   * 生成测试样式
   * @param {Object} options - 选项
   * @returns {Object} 测试样式
   */
  generateTestStyles: (options = {}) => {
    const {
      includeConflicting = false,
      complexity = 'simple',
      includeColors = true,
    } = options;

    const baseStyle = {};

    if (includeColors) {
      baseStyle.color = '#333333';
    }

    if (complexity === 'simple') {
      baseStyle.fontSize = 16;
    } else if (complexity === 'medium') {
      Object.assign(baseStyle, {
        fontSize: 18,
        lineHeight: 24,
        textAlign: 'center',
        marginVertical: 8,
      });
    } else if (complexity === 'complex') {
      Object.assign(baseStyle, {
        fontSize: 20,
        lineHeight: 28,
        textAlign: 'justify',
        marginVertical: 12,
        paddingHorizontal: 16,
        textDecorationLine: 'underline',
        textShadowColor: 'rgba(0,0,0,0.1)',
        textShadowOffset: {width: 1, height: 1},
        textShadowRadius: 2,
      });
    }

    if (includeConflicting) {
      baseStyle.fontFamily = 'Arial';
      baseStyle.fontWeight = 'bold';
    }

    return baseStyle;
  },

  /**
   * 生成长文本
   * @param {number} length - 文本长度
   * @returns {string} 生成的文本
   */
  generateLongText: (length = 1000) => {
    const words = [
      'Lorem',
      'ipsum',
      'dolor',
      'sit',
      'amet',
      'consectetur',
      'adipiscing',
      'elit',
      'sed',
      'do',
      'eiusmod',
      'tempor',
      'incididunt',
      'ut',
      'labore',
      'et',
      'dolore',
      'magna',
      'aliqua',
      'Ut',
      'enim',
      'ad',
      'minim',
      'veniam',
    ];

    let text = '';
    while (text.length < length) {
      const word = words[Math.floor(Math.random() * words.length)];
      text += (text ? ' ' : '') + word;
    }

    return text.substring(0, length);
  },
};

/**
 * 样式测试工具
 */
export const styleTestUtils = {
  /**
   * 检查元素是否包含指定样式
   * @param {Object} element - React Native 元素
   * @param {Object} expectedStyle - 期望的样式
   * @returns {boolean} 是否包含样式
   */
  hasStyle: (element, expectedStyle) => {
    const elementStyles = element.props.style;

    if (!elementStyles) {return false;}

    // 处理样式数组
    const flattenedStyle = Array.isArray(elementStyles)
      ? Object.assign({}, ...elementStyles.filter(Boolean))
      : elementStyles;

    // 检查每个期望的样式属性
    return Object.keys(expectedStyle).every(
      key => flattenedStyle[key] === expectedStyle[key],
    );
  },

  /**
   * 提取元素的字体相关样式
   * @param {Object} element - React Native 元素
   * @returns {Object} 字体样式
   */
  extractFontStyles: element => {
    const elementStyles = element.props.style;

    if (!elementStyles) {return {};}

    const flattenedStyle = Array.isArray(elementStyles)
      ? Object.assign({}, ...elementStyles.filter(Boolean))
      : elementStyles;

    const fontKeys = ['fontFamily', 'fontWeight', 'fontSize', 'fontStyle'];
    const fontStyles = {};

    fontKeys.forEach(key => {
      if (flattenedStyle[key] !== undefined) {
        fontStyles[key] = flattenedStyle[key];
      }
    });

    return fontStyles;
  },

  /**
   * 验证样式是否被正确过滤
   * @param {Object} element - React Native 元素
   * @param {Array} filteredKeys - 应该被过滤的样式键
   * @returns {boolean} 是否正确过滤
   */
  isStyleFiltered: (element, filteredKeys = ['fontFamily', 'fontWeight']) => {
    const elementStyles = element.props.style;

    if (!elementStyles) {return true;}

    const flattenedStyle = Array.isArray(elementStyles)
      ? Object.assign({}, ...elementStyles.filter(Boolean))
      : elementStyles;

    // 检查过滤的键是否不存在于最终样式中（除了组件自己设置的）
    return filteredKeys.every(key => {
      // 如果是fontFamily，应该存在但是是组件设置的值
      if (key === 'fontFamily') {
        return flattenedStyle[key] && flattenedStyle[key].includes('-');
      }
      // 其他被过滤的属性不应该存在
      return !flattenedStyle.hasOwnProperty(key);
    });
  },
};

/**
 * 可访问性测试工具
 */
export const accessibilityTestUtils = {
  /**
   * 检查元素的可访问性属性
   * @param {Object} element - React Native 元素
   * @returns {Object} 可访问性属性
   */
  getAccessibilityProps: element => {
    const props = element.props;
    const accessibilityKeys = [
      'accessible',
      'accessibilityRole',
      'accessibilityLabel',
      'accessibilityHint',
      'accessibilityState',
      'accessibilityValue',
      'accessibilityLiveRegion',
    ];

    const accessibilityProps = {};
    accessibilityKeys.forEach(key => {
      if (props[key] !== undefined) {
        accessibilityProps[key] = props[key];
      }
    });

    return accessibilityProps;
  },

  /**
   * 验证可访问性最佳实践
   * @param {Object} element - React Native 元素
   * @returns {Object} 验证结果
   */
  validateAccessibility: element => {
    const props = element.props;
    const issues = [];
    const suggestions = [];

    // 检查是否有文本但没有可访问性标签
    if (
      props.children &&
      typeof props.children === 'string' &&
      props.children.length > 50
    ) {
      if (!props.accessibilityLabel && !props.accessibilityHint) {
        suggestions.push(
          'Consider adding accessibilityLabel or accessibilityHint for long text',
        );
      }

    // 检查是否有交互但没有适当的角色
    if (props.onPress && !props.accessibilityRole) {
      issues.push('Interactive element should have accessibilityRole');
    }

    // 检查是否有状态变化但没有实时区域
    if (props.accessibilityState && !props.accessibilityLiveRegion) {
      suggestions.push(
        'Consider adding accessibilityLiveRegion for dynamic content',
      );

    return {
      isValid: issues.length === 0,
      issues,
      suggestions,
      score: Math.max(0, 100 - issues.length * 20 - suggestions.length * 5),
    };
  },
};

/**
 * 模拟数据
 */
export const mockData = {
  // 常用的测试文本
  texts: {
    short: 'Hello',
    medium: 'Hello World, this is a medium length text.',
    long: 'This is a very long text that might be used to test how the component handles lengthy content and whether it performs well with large amounts of text data.',
    multiline: 'Line 1\nLine 2\nLine 3',
    special: '🚀 Hello 世界 🌍 @#$%^&*()',
    empty: '',
  },

  // 常用的样式组合
  styles: {
    title: {fontSize: 24, fontWeight: 'bold', color: '#333'},
    subtitle: {fontSize: 18, color: '#666'},
    body: {fontSize: 16, lineHeight: 24, color: '#333'},
    caption: {fontSize: 12, color: '#999'},
    error: {fontSize: 14, color: '#ff0000'},
    success: {fontSize: 14, color: '#00aa00'},
  },

  // 测试主题
  themes: {
    light: {
      text: '#000000',
      textSecondary: '#666666',
      background: '#ffffff',
    },
    dark: {
      text: '#ffffff',
      textSecondary: '#cccccc',
      background: '#000000',
    },
  },
};

/**
 * 测试断言辅助函数
 */
export const assertions = {
  /**
   * 断言性能改进
   * @param {Object} baseline - 基准性能
   * @param {Object} optimized - 优化后性能
   * @param {number} expectedImprovement - 期望的改进百分比
   */
  expectPerformanceImprovement: (
    baseline,
    optimized,
    expectedImprovement = 10,
  ) => {
    const comparison = performanceUtils.comparePerformance(baseline, optimized);
    expect(comparison.isFaster).toBe(true);
    expect(comparison.improvementPercentage).toBeGreaterThan(
      expectedImprovement,
    );
  },

  /**
   * 断言内存使用合理
   * @param {Object} memoryUsage - 内存使用情况
   * @param {number} maxIncrease - 最大允许增长（字节）
   */
  expectReasonableMemoryUsage: (memoryUsage, maxIncrease = 1024 * 1024) => {
    expect(memoryUsage.heapUsedDelta).toBeLessThan(maxIncrease);
  },

  /**
   * 断言样式正确应用
   * @param {Object} element - 元素
   * @param {Object} expectedStyle - 期望样式
   */
  expectStyleApplied: (element, expectedStyle) => {
    expect(styleTestUtils.hasStyle(element, expectedStyle)).toBe(true);
  },
};
