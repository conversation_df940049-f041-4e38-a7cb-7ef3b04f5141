/**
 * CustomText 组件集成测试
 * 测试组件在实际应用场景中的表现和与其他组件的交互
 */

import React from 'react';
import {render, fireEvent} from '@testing-library/react-native';
import {View, ScrollView, TouchableOpacity} from 'react-native';
import CustomText, {FONT_WEIGHTS, FONT_FAMILY_TYPES} from '../CustomText';

describe('CustomText Integration Tests', () => {
  describe('与其他组件的交互', () => {
    test('应该在ScrollView中正常工作', () => {
      const {getByText} = render(
        <ScrollView>
          <CustomText fontWeight={FONT_WEIGHTS.BOLD}>Header Text</CustomText>
          <CustomText>
            Body text that might be very long and need to scroll...
          </CustomText>
          <CustomText fontFamily={FONT_FAMILY_TYPES.RUBIK}>
            Footer Text
          </CustomText>
        </ScrollView>,
      );

      expect(getByText('Header Text')).toBeTruthy();
      expect(
        getByText('Body text that might be very long and need to scroll...'),
      ).toBeTruthy();
      expect(getByText('Footer Text')).toBeTruthy();
    });

    test('应该在TouchableOpacity中正常工作', () => {
      const onPress = jest.fn();

      const {getByText} = render(
        <TouchableOpacity onPress={onPress}>
          <CustomText fontWeight={FONT_WEIGHTS.MEDIUM}>
            Clickable Text
          </CustomText>
        </TouchableOpacity>,
      );

      const touchable = getByText('Clickable Text').parent;
      fireEvent.press(touchable);

      expect(onPress).toHaveBeenCalledTimes(1);
    });

    test('应该支持嵌套的CustomText组件', () => {
      const {getByText} = render(
        <CustomText>
          This is normal text with{' '}
          <CustomText fontWeight={FONT_WEIGHTS.BOLD}>bold text</CustomText> and{' '}
          <CustomText fontFamily={FONT_FAMILY_TYPES.RUBIK}>
            different font
          </CustomText>
          .
        </CustomText>,
      );

      expect(getByText('This is normal text with')).toBeTruthy();
      expect(getByText('bold text')).toBeTruthy();
      expect(getByText('different font')).toBeTruthy();
    });
  });

  describe('实际使用场景测试', () => {
    test('应该正确渲染文章页面布局', () => {
      const ArticlePage = () => (
        <View>
          <CustomText
            fontWeight={FONT_WEIGHTS.BOLD}
            style={{fontSize: 24, marginBottom: 16}}>
            Article Title
          </CustomText>

          <CustomText
            fontFamily={FONT_FAMILY_TYPES.RUBIK}
            style={{fontSize: 14, color: '#666', marginBottom: 8}}>
            By Author Name • 5 min read
          </CustomText>

          <CustomText style={{fontSize: 16, lineHeight: 24, marginBottom: 16}}>
            This is the article content. It should be readable and
            well-formatted with proper line height and spacing.
          </CustomText>

          <CustomText
            fontWeight={FONT_WEIGHTS.SEMI_BOLD}
            style={{fontSize: 18, marginBottom: 12}}>
            Section Header
          </CustomText>

          <CustomText style={{fontSize: 16, lineHeight: 24}}>
            More article content continues here with consistent styling
            throughout the entire article.
          </CustomText>
        </View>
      );

      const {getByText} = render(<ArticlePage />);

      expect(getByText('Article Title')).toBeTruthy();
      expect(getByText('By Author Name • 5 min read')).toBeTruthy();
      expect(getByText(/This is the article content/)).toBeTruthy();
      expect(getByText('Section Header')).toBeTruthy();
      expect(getByText(/More article content/)).toBeTruthy();
    });

    test('应该正确渲染卡片列表', () => {
      const cardData = [1, 2, 3];

      const CardList = () => (
        <View>
          {cardData.map(id => (
            <View key={id} style={{marginBottom: 16, padding: 16}}>
              <CustomText
                fontWeight={FONT_WEIGHTS.BOLD}
                style={{fontSize: 18, marginBottom: 8}}>
                Card Title {id}
              </CustomText>

              <CustomText
                style={{fontSize: 14, color: '#666', marginBottom: 8}}>
                Card subtitle or description
              </CustomText>

              <CustomText style={{fontSize: 16}}>
                Card content goes here with proper formatting.
              </CustomText>
            </View>
          ))}
        </View>
      );

      const {getByText} = render(<CardList />);

      expect(getByText('Card Title 1')).toBeTruthy();
      expect(getByText('Card Title 2')).toBeTruthy();
      expect(getByText('Card Title 3')).toBeTruthy();
    });

    test('应该正确处理表单标签', () => {
      const FormExample = () => (
        <View>
          <CustomText
            fontWeight={FONT_WEIGHTS.MEDIUM}
            style={{fontSize: 16, marginBottom: 8}}>
            Email Address *
          </CustomText>

          <CustomText style={{fontSize: 14, color: '#666', marginBottom: 16}}>
            Please enter your email address
          </CustomText>

          <CustomText style={{fontSize: 14, color: '#ff0000'}}>
            This field is required
          </CustomText>
        </View>
      );

      const {getByText} = render(<FormExample />);

      expect(getByText('Email Address *')).toBeTruthy();
      expect(getByText('Please enter your email address')).toBeTruthy();
      expect(getByText('This field is required')).toBeTruthy();
    });
  });

  describe('可访问性集成测试', () => {
    test('应该正确处理屏幕阅读器', () => {
      const {getByText} = render(
        <View>
          <CustomText accessibilityRole="header" accessibilityLevel={1}>
            Main Heading
          </CustomText>

          <CustomText
            accessibilityRole="text"
            accessibilityHint="This is the main content">
            Main content text
          </CustomText>

          <CustomText
            accessibilityRole="button"
            accessibilityLabel="Click to continue">
            Continue
          </CustomText>
        </View>,
      );

      const heading = getByText('Main Heading');
      const content = getByText('Main content text');
      const button = getByText('Continue');

      expect(heading.props.accessibilityRole).toBe('header');
      expect(content.props.accessibilityHint).toBe('This is the main content');
      expect(button.props.accessibilityLabel).toBe('Click to continue');
    });

    test('应该支持动态内容更新', () => {
      const createDynamicContent = count => (
        <CustomText
          accessibilityLiveRegion="polite"
          accessibilityLabel={`Count is now ${count}`}>
          Count: {count}
        </CustomText>
      );

      const {getByText, rerender} = render(createDynamicContent(0));

      expect(getByText('Count: 0')).toBeTruthy();

      rerender(createDynamicContent(5));

      expect(getByText('Count: 5')).toBeTruthy();
      expect(getByText('Count: 5').props.accessibilityLabel).toBe(
        'Count is now 5',
      );
    });
  });

  describe('主题和样式集成测试', () => {
    test('应该支持主题色彩系统', () => {
      const theme = {
        colors: {
          primary: '#007AFF',
          secondary: '#5856D6',
          text: '#000000',
          textSecondary: '#666666',
        },
      };

      const ThemedComponent = () => (
        <View>
          <CustomText
            fontWeight={FONT_WEIGHTS.BOLD}
            style={{color: theme.colors.primary, fontSize: 20}}>
            Primary Text
          </CustomText>

          <CustomText style={{color: theme.colors.textSecondary, fontSize: 14}}>
            Secondary Text
          </CustomText>
        </View>
      );

      const {getByText} = render(<ThemedComponent />);

      const primaryText = getByText('Primary Text');
      const secondaryText = getByText('Secondary Text');

      expect(primaryText.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            color: theme.colors.primary,
            fontSize: 20,
          }),
        ]),
      );

      expect(secondaryText.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            color: theme.colors.textSecondary,
            fontSize: 14,
          }),
        ]),
      );
    });

    test('应该支持响应式字体大小', () => {
      const createResponsiveText = isLarge => (
        <CustomText
          style={{
            fontSize: isLarge ? 24 : 16,
            lineHeight: isLarge ? 32 : 24,
          }}>
          Responsive Text
        </CustomText>
      );

      const {getByText, rerender} = render(createResponsiveText(false));

      let textElement = getByText('Responsive Text');
      expect(textElement.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            fontSize: 16,
            lineHeight: 24,
          }),
        ]),
      );

      rerender(createResponsiveText(true));

      textElement = getByText('Responsive Text');
      expect(textElement.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            fontSize: 24,
            lineHeight: 32,
          }),
        ]),
      );
    });
  });

  describe('错误处理和边界情况', () => {
    test('应该优雅处理渲染错误', () => {
      // 模拟一个可能导致错误的场景
      const createProblematicComponent = shouldError => {
        if (shouldError) {
          throw new Error('Simulated error');
        }

        return <CustomText>Normal text</CustomText>;
      };

      // 正常情况应该工作
      const {getByText} = render(createProblematicComponent(false));
      expect(getByText('Normal text')).toBeTruthy();

      // 错误情况应该被适当处理（这里我们不测试错误，只确保组件本身是健壮的）
    });

    test('应该处理极长的文本内容', () => {
      const longText = 'A'.repeat(10000);

      const {getByText} = render(
        // cSpell:ignore ellipsize
        <CustomText numberOfLines={3} ellipsizeMode="tail">
          {longText}
        </CustomText>,
      );

      expect(getByText(longText)).toBeTruthy();
    });

    test('应该处理特殊字符和表情符号', () => {
      const specialText = '🚀 Hello 世界 🌍 Special chars: @#$%^&*()';

      const {getByText} = render(<CustomText>{specialText}</CustomText>);

      expect(getByText(specialText)).toBeTruthy();
    });
  });
});
