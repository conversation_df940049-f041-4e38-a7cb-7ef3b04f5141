/**
 * CustomText 组件单元测试
 * 测试组件的渲染、属性处理、样式应用和性能优化
 */

import React from 'react';
import {render, screen} from '@testing-library/react-native';
import CustomText, {FONT_WEIGHTS, FONT_FAMILY_TYPES} from '../CustomText';

// 模拟 StyleSheet.flatten 方法
jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native');
  return {
    ...RN,
    StyleSheet: {
      ...RN.StyleSheet,
      flatten: jest.fn(style => {
        if (Array.isArray(style)) {
          return Object.assign({}, ...style.filter(Boolean));
        }
        return style || {};
      }),
    },
  };
});

describe('CustomText Component', () => {
  // 每个测试前清理缓存
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('基础渲染测试', () => {
    test('应该正确渲染文本内容', () => {
      render(<CustomText>Hello World</CustomText>);
      expect(screen.getByText('Hello World')).toBeTruthy();
    });

    test('应该使用默认字体和权重', () => {
      const {getByText} = render(<CustomText>Default Text</CustomText>);
      const textElement = getByText('Default Text');

      expect(textElement.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            fontFamily: 'WorkSans-Regular',
          }),
        ]),
      );
    });

    test('应该正确应用自定义字体家族', () => {
      const {getByText} = render(
        <CustomText fontFamily={FONT_FAMILY_TYPES.RUBIK}>
          Rubik Text
        </CustomText>,
      );
      const textElement = getByText('Rubik Text');

      expect(textElement.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            fontFamily: 'Rubik-Regular',
          }),
        ]),
      );
    });

    test('应该正确应用自定义字体权重', () => {
      const {getByText} = render(
        <CustomText fontWeight={FONT_WEIGHTS.BOLD}>Bold Text</CustomText>,
      );
      const textElement = getByText('Bold Text');

      expect(textElement.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            fontFamily: 'WorkSans-Bold',
          }),
        ]),
      );
    });

    test('应该正确组合字体家族和权重', () => {
      const {getByText} = render(
        <CustomText
          fontFamily={FONT_FAMILY_TYPES.RUBIK}
          fontWeight={FONT_WEIGHTS.SEMI_BOLD}>
          Rubik SemiBold
        </CustomText>,
      );
      const textElement = getByText('Rubik SemiBold');

      expect(textElement.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            fontFamily: 'Rubik-SemiBold',
          }),
        ]),
      );
    });
  });

  describe('样式处理测试', () => {
    test('应该正确应用自定义样式', () => {
      const customStyle = {
        color: 'red',
        fontSize: 18,
        textAlign: 'center',
      };

      const {getByText} = render(
        <CustomText style={customStyle}>Styled Text</CustomText>,
      );
      const textElement = getByText('Styled Text');

      expect(textElement.props.style).toEqual(
        expect.arrayContaining([expect.objectContaining(customStyle)]),
      );
    });

    test('应该过滤掉冲突的字体样式', () => {
      const conflictingStyle = {
        color: 'blue',
        fontSize: 20,
        fontFamily: 'Arial', // 这个应该被过滤掉
        fontWeight: 'bold', // 这个应该被过滤掉
      };

      const {getByText} = render(
        <CustomText
          fontFamily={FONT_FAMILY_TYPES.WORK_SANS}
          fontWeight={FONT_WEIGHTS.MEDIUM}
          style={conflictingStyle}>
          Filtered Style Text
        </CustomText>,
      );
      const textElement = getByText('Filtered Style Text');

      // 应该包含组件设置的字体
      expect(textElement.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            fontFamily: 'WorkSans-Medium',
          }),
        ]),
      );

      // 应该包含非冲突的样式
      expect(textElement.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            color: 'blue',
            fontSize: 20,
          }),
        ]),
      );
    });

    test('应该正确处理样式数组', () => {
      const style1 = {color: 'red'};
      const style2 = {fontSize: 18};
      const styleArray = [style1, style2];

      const {getByText} = render(
        <CustomText style={styleArray}>Array Style Text</CustomText>,
      );
      const textElement = getByText('Array Style Text');

      expect(textElement.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            color: 'red',
            fontSize: 18,
          }),
        ]),
      );
    });

    test('应该正确处理空样式', () => {
      const {getByText} = render(
        <CustomText style={null}>Null Style Text</CustomText>,
      );
      const textElement = getByText('Null Style Text');

      expect(textElement.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            fontFamily: 'WorkSans-Regular',
          }),
        ]),
      );
    });
  });

  describe('属性传递测试', () => {
    test('应该正确传递其他Text属性', () => {
      const {getByText} = render(
        <CustomText numberOfLines={2} ellipsizeMode="tail" testID="custom-text">
          Props Test
        </CustomText>,
      );
      const textElement = getByText('Props Test');

      expect(textElement.props.numberOfLines).toBe(2);
      expect(textElement.props.ellipsizeMode).toBe('tail');
      expect(textElement.props.testID).toBe('custom-text');
    });

    test('应该正确处理可访问性属性', () => {
      const {getByText} = render(
        <CustomText
          accessibilityRole="header"
          accessibilityHint="This is a header">
          Accessibility Test
        </CustomText>,
      );
      const textElement = getByText('Accessibility Test');

      expect(textElement.props.accessibilityRole).toBe('header');
      expect(textElement.props.accessibilityHint).toBe('This is a header');
    });

    test('应该正确处理事件处理器', () => {
      const onPress = jest.fn();
      const {getByText} = render(
        <CustomText onPress={onPress}>Pressable Text</CustomText>,
      );
      const textElement = getByText('Pressable Text');

      expect(textElement.props.onPress).toBe(onPress);
    });
  });

  describe('边界情况测试', () => {
    test('应该处理不存在的字体家族', () => {
      const {getByText} = render(
        <CustomText fontFamily="NonExistentFont">
          Fallback Font Test
        </CustomText>,
      );
      const textElement = getByText('Fallback Font Test');

      // 应该回退到默认字体
      expect(textElement.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            fontFamily: 'WorkSans-Regular',
          }),
        ]),
      );
    });

    test('应该处理不存在的字体权重', () => {
      const {getByText} = render(
        <CustomText fontWeight={999}>Fallback Weight Test</CustomText>,
      );
      const textElement = getByText('Fallback Weight Test');

      // 应该回退到默认权重
      expect(textElement.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            fontFamily: 'WorkSans-Regular',
          }),
        ]),
      );
    });

    test('应该处理Rubik字体的Bold权重回退', () => {
      const {getByText} = render(
        <CustomText
          fontFamily={FONT_FAMILY_TYPES.RUBIK}
          fontWeight={FONT_WEIGHTS.BOLD}>
          Rubik Bold Fallback
        </CustomText>,
      );
      const textElement = getByText('Rubik Bold Fallback');

      // Rubik Bold 应该回退到 SemiBold
      expect(textElement.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            fontFamily: 'Rubik-SemiBold',
          }),
        ]),
      );
    });

    test('应该处理空的children', () => {
      const {container} = render(<CustomText />);
      expect(container).toBeTruthy();
    });

    test('应该处理复杂的children结构', () => {
      const {getByText} = render(
        <CustomText>
          Hello <CustomText fontWeight={FONT_WEIGHTS.BOLD}>World</CustomText>
        </CustomText>,
      );

      expect(getByText('Hello')).toBeTruthy();
      expect(getByText('World')).toBeTruthy();
    });
  });

  describe('PropTypes 验证测试', () => {
    // 保存原始的 console.error
    const originalError = console.error;

    beforeEach(() => {
      console.error = jest.fn();
    });

    afterEach(() => {
      console.error = originalError;
    });

    test('应该验证 children 属性为必需', () => {
      render(<CustomText />);
      expect(console.error).toHaveBeenCalledWith(
        expect.stringContaining('Warning: Failed prop type'),
      );
    });

    test('应该验证 fontFamily 属性值', () => {
      render(<CustomText fontFamily="InvalidFont">Invalid Font</CustomText>);
      expect(console.error).toHaveBeenCalledWith(
        expect.stringContaining('Warning: Failed prop type'),
      );
    });

    test('应该验证 fontWeight 属性值', () => {
      render(<CustomText fontWeight={123}>Invalid Weight</CustomText>);
      expect(console.error).toHaveBeenCalledWith(
        expect.stringContaining('Warning: Failed prop type'),
      );
    });
  });
});
