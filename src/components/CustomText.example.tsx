/**
 * CustomText 组件使用示例
 * 展示了各种使用场景和最佳实践
 */

import React from 'react';
import {View, StyleSheet, ScrollView} from 'react-native';
import CustomText, {FONT_WEIGHTS, FONT_FAMILY_TYPES} from './CustomText';

const CustomTextExample: React.FC = () => {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.section}>
        <CustomText style={styles.sectionTitle} fontWeight={FONT_WEIGHTS.BOLD}>
          字体家族示例
        </CustomText>
        
        <CustomText fontFamily={FONT_FAMILY_TYPES.WORK_SANS}>
          WorkSans 字体 (默认)
        </CustomText>
        
        <CustomText fontFamily={FONT_FAMILY_TYPES.RUBIK}>
          Rubik 字体
        </CustomText>
      </View>

      <View style={styles.section}>
        <CustomText style={styles.sectionTitle} fontWeight={FONT_WEIGHTS.BOLD}>
          字体权重示例
        </CustomText>
        
        <CustomText fontWeight={FONT_WEIGHTS.REGULAR}>
          Regular (400) - 常规字体
        </CustomText>
        
        <CustomText fontWeight={FONT_WEIGHTS.MEDIUM}>
          Medium (500) - 中等字体
        </CustomText>
        
        <CustomText fontWeight={FONT_WEIGHTS.SEMI_BOLD}>
          SemiBold (600) - 半粗体
        </CustomText>
        
        <CustomText fontWeight={FONT_WEIGHTS.BOLD}>
          Bold (700) - 粗体
        </CustomText>
      </View>

      <View style={styles.section}>
        <CustomText style={styles.sectionTitle} fontWeight={FONT_WEIGHTS.BOLD}>
          样式组合示例
        </CustomText>
        
        <CustomText 
          fontFamily={FONT_FAMILY_TYPES.RUBIK}
          fontWeight={FONT_WEIGHTS.BOLD}
          style={styles.primaryText}
        >
          主要文本 - Rubik Bold
        </CustomText>
        
        <CustomText 
          fontFamily={FONT_FAMILY_TYPES.WORK_SANS}
          fontWeight={FONT_WEIGHTS.MEDIUM}
          style={styles.secondaryText}
        >
          次要文本 - WorkSans Medium
        </CustomText>
        
        <CustomText 
          style={styles.errorText}
          fontWeight={FONT_WEIGHTS.SEMI_BOLD}
        >
          错误文本 - 红色半粗体
        </CustomText>
      </View>

      <View style={styles.section}>
        <CustomText style={styles.sectionTitle} fontWeight={FONT_WEIGHTS.BOLD}>
          可访问性示例
        </CustomText>
        
        <CustomText 
          accessibilityRole="header"
          fontWeight={FONT_WEIGHTS.BOLD}
          style={styles.headerText}
        >
          页面标题 (Header Role)
        </CustomText>
        
        <CustomText 
          accessibilityHint="这是一个重要的提示信息"
          style={styles.hintText}
        >
          带有可访问性提示的文本
        </CustomText>
      </View>

      <View style={styles.section}>
        <CustomText style={styles.sectionTitle} fontWeight={FONT_WEIGHTS.BOLD}>
          响应式文本示例
        </CustomText>
        
        <CustomText style={styles.largeText}>
          大号文本 (24px)
        </CustomText>
        
        <CustomText style={styles.mediumText}>
          中号文本 (18px)
        </CustomText>
        
        <CustomText style={styles.smallText}>
          小号文本 (12px)
        </CustomText>
      </View>

      <View style={styles.section}>
        <CustomText style={styles.sectionTitle} fontWeight={FONT_WEIGHTS.BOLD}>
          多行文本示例
        </CustomText>
        
        <CustomText style={styles.paragraphText}>
          这是一段较长的文本，用来展示CustomText组件在处理多行文本时的表现。
          组件会自动应用正确的字体文件，并保持良好的可读性和一致的样式。
          同时支持各种React Native Text组件的原生属性。
        </CustomText>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 16,
  },
  section: {
    marginBottom: 24,
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  sectionTitle: {
    fontSize: 20,
    color: '#333333',
    marginBottom: 12,
  },
  primaryText: {
    fontSize: 18,
    color: '#007AFF',
    marginBottom: 8,
  },
  secondaryText: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 8,
  },
  errorText: {
    fontSize: 16,
    color: '#FF3B30',
    marginBottom: 8,
  },
  headerText: {
    fontSize: 22,
    color: '#000000',
    marginBottom: 8,
  },
  hintText: {
    fontSize: 14,
    color: '#8E8E93',
    fontStyle: 'italic',
    marginBottom: 8,
  },
  largeText: {
    fontSize: 24,
    color: '#333333',
    marginBottom: 8,
  },
  mediumText: {
    fontSize: 18,
    color: '#333333',
    marginBottom: 8,
  },
  smallText: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 8,
  },
  paragraphText: {
    fontSize: 16,
    color: '#333333',
    lineHeight: 24,
    textAlign: 'justify',
  },
});

export default CustomTextExample;
