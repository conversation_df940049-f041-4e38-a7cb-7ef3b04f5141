import React, {isValidElement} from 'react';
import {CVNIcon} from '@cvn/rn-panel-kit';
import {View, Image} from 'react-native';
import CustomStyleSheet from '@utils/style/index.js';
import InteractiveBox from './InteractiveBox';
import CustomText from './CustomText';

const arrowRightImgBase64 =
  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAiCAYAAABfqvm9AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAADUSURBVHgB7dW9DYMwEAXgO1eUjMAIWSGjpGQiJBpGYYSswAj0CDs+KYUBY+6HkichZD/xSQYZAzyxBtNB3/ffEELtvX+3bTuBIm43nuPVOOfGrusaUGQDruv6ibfJguJ+ghDCCCVcunzMTVpQPCu0KJZKDVoENeglKEVZoARlg1xUBHJQB8LQw4RAsqPSXgxSqqqaEXHOdWJwGIZ6WZYx/pVe8F9y2oveYQ5TfxQOxga5GAuUYJegFCuCGuwU1GJZ0IIdQCtG2ewUK3YA6ZC3YE/uyQ+Ki<PERSON>+geaRzgAAAABJRU5ErkJggg==';

export default ({
  leftIconElement,
  rightElement,
  rightExtra,
  onPress = () => {},
  showRightUnderline = false,
  showWholeUnderline = false,
  underlineStyle = {},
  title = '',
  titleStyle = {},
  subTitleStyle = {},
  style = {},
  containerStyle = {},
  itemStyle = {},
  leftViewStyle = {},
  leftIconSource = undefined,
  subTitle = undefined,
  iconShow = true,
  isShowShadow = false,
  disabled = false,
  isClickable = true,
  isContentVertical = false,
  hideRightArrow = false,
}) => {
  const underline = {
    ...styles.underline,
    ...underlineStyle,
  };
  return (
    <View style={[isShowShadow ? shadow.container : {}, style]}>
      <InteractiveBox isInteractive={isClickable} onPress={onPress}>
        <View
          style={[
            styles.container,
            containerStyle,
            isContentVertical ? {} : styles.horizontalContainer,
            showWholeUnderline ? underline : {},
          ]}>
          <View style={isContentVertical ? styles.verticalIconContainer : {}}>
            {isValidElement(leftIconElement) ? (
              leftIconElement
            ) : iconShow && leftIconSource ? (
              <CVNIcon
                style={styles.leftIcon}
                source={{
                  uri: leftIconSource,
                }}
              />
            ) : null}
          </View>
          <View
            style={[
              styles.itemContainer,
              itemStyle,
              showRightUnderline ? underline : {},
            ]}>
            <View
              style={[
                styles.contentContainer,
                isContentVertical ? {} : styles.horizontalItem,
              ]}>
              <View
                style={[
                  styles.leftView,
                  leftViewStyle,
                  isContentVertical ? styles.verticalLeftView : {},
                ]}>
                {subTitle ? (
                  <View style={styles.textContainer}>
                    <CustomText style={[styles.itemText, titleStyle]}>
                      {title}
                    </CustomText>
                    <CustomText style={[styles.itemSubText, subTitleStyle]}>
                      {subTitle}
                    </CustomText>
                  </View>
                ) : (
                  <CustomText style={[styles.itemText, titleStyle]}>
                    {title}
                  </CustomText>
                )}
              </View>
              <View>
                {isValidElement(rightElement) ? (
                  rightElement
                ) : (
                  <View style={styles.rightBox}>
                    {isValidElement(rightExtra) ? rightExtra : null}
                  </View>
                )}
              </View>
            </View>
            {hideRightArrow ? null : (
              <Image
                style={styles.rightIcon}
                source={{
                  uri: arrowRightImgBase64,
                }}
              />
            )}
          </View>
        </View>
        <View style={disabled ? styles.disabled : {}} />
      </InteractiveBox>
    </View>
  );
};

const styles = CustomStyleSheet.create({
  container: {
    backgroundColor: '#fff',
    flexDirection: 'row',
  },
  horizontalContainer: {
    alignItems: 'center',
  },
  verticalContainer: {
    paddingTop: 15,
  },
  itemContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 15,
  },
  horizontalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  contentContainer: {
    flex: 1,
  },
  underline: {
    marginTop: 0.5,
    borderBottomWidth: 0.5,
    borderBottomColor: '#E5E5E5',
  },
  leftView: {
    flexDirection: 'row',
    flexShrink: 1,
  },
  verticalLeftView: {
    marginBottom: 6,
  },
  verticalIconContainer: {
    marginTop: 15,
  },
  leftIcon: {
    width: 29,
    height: 29,
    marginRight: 10,
  },
  textContainer: {
    flexDirection: 'column',
  },
  itemText: {
    fontSize: 16,
    color: '#000000',
    fontWeight: '400',
  },
  itemSubText: {
    marginTop: 5,
    fontSize: 12,
    color: '#666666',
    fontWeight: '400',
  },
  rightBox: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rightIcon: {
    width: 10,
    height: 17,
    marginLeft: 10,
  },
  disabled: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 1, // should not be 0, otherwise the underline will decrease opacity
    backgroundColor: '#fff',
    opacity: 0.4,
  },
});

const shadow = CustomStyleSheet.create({
  container: {
    shadowColor: '#000000',
    shadowOffset: {width: 0, height: 2},
    shadowRadius: 7.5,
    shadowOpacity: 0.05,
    // elevation: 2,
  },
});
