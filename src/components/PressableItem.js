import React from 'react';
import {View, Image} from 'react-native';
import CustomStyleSheet from '@utils/style/index.js';
import {CVNIcon} from '@cvn/rn-panel-kit';
import PropTypes from 'prop-types';
import InteractiveBox from './InteractiveBox';
import CustomText from './CustomText';

const PressableItem = ({
  onPress = () => {},
  icon,
  content = '',
  extra = '',
  isShowRedDot = false,
  isInteractive = false,
  style = {},
  contentTextStyle,
  extraTextStyle,
}) => {
  const disableStyle = isInteractive ? {} : styles.disabled;
  return (
    <InteractiveBox
      isInteractive={isInteractive}
      style={[styles.container, style]}
      onPress={onPress}>
      <View style={styles.leftViewStyle}>
        <CVNIcon source={icon} style={[styles.icon, disableStyle]} />
        <CustomText
          numberOfLines={1}
          ellipsizeMode="tail"
          fontWeight={500}
          style={
            contentTextStyle
              ? [styles.content, contentTextStyle]
              : [styles.content, disableStyle]
          }>
          {content}
        </CustomText>
        <CustomText
          numberOfLines={1}
          ellipsizeMode="tail"
          style={
            extraTextStyle
              ? [styles.extra, extraTextStyle]
              : [styles.extra, disableStyle]
          }>
          {extra}
        </CustomText>
      </View>
      <Image
        style={styles.rightIcon}
        source={require('@assets/common_icon_right_arrow.png')}
      />
      {isShowRedDot && <View style={styles.redDot} />}
    </InteractiveBox>
  );
};

PressableItem.propTypes = {
  onPress: PropTypes.func,
  icon: PropTypes.object,
  content: PropTypes.string,
  extra: PropTypes.string,
  isShowRedDot: PropTypes.bool,
  isInteractive: PropTypes.bool,
  style: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
  contentTextStyle: PropTypes.object,
  extraTextStyle: PropTypes.object,
};

const styles = CustomStyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    // boxShadow: '0px 0px 15px 0px rgba(0,0,0,0.05)',
    shadowColor: '#000000',
    shadowRadius: 7.5,
    shadowOpacity: 0.05,
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 14,
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  leftViewStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    width: 34,
    height: 34,
    marginBottom: 3.5,
  },
  content: {
    fontWeight: '500',
    fontSize: 16,
    color: '#3C3936',
    marginLeft: 14,
  },
  extra: {
    fontSize: 14,
    color: '#77BC1F',
    marginLeft: 14,
  },
  redDot: {
    position: 'absolute',
    zIndex: 1,
    right: 7,
    top: 7,
    borderRadius: 3.5,
    width: 7,
    height: 7,
    backgroundColor: '#FF4646',
  },
  disabled: {
    opacity: 0.4,
  },
  rightIcon: {
    width: 24,
    height: 24,
  },
});

export default PressableItem;
