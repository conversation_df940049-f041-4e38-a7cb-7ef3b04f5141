import React, {useState} from 'react';
import {StyleSheet, View, Text, TouchableWithoutFeedback} from 'react-native';
import {CVNIcon} from '@cvn/rn-panel-kit';
import {LineView} from '@components';

export const SingleSelect = ({data = [], onSelected = () => {}}) => {
  const [selectIndex, setSelectedIndex] = useState(0);

  return (
    <View>
      {data.map((item, index) => {
        const {title, key} = item;
        return (
          <View key={key}>
            <TouchableWithoutFeedback
              onPress={() => {
                console.log('title--click--', title);
                setSelectedIndex(index);
                onSelected(item, index);
              }}>
              <View style={styles.itemContainer}>
                <View style={styles.itemView}>
                  <Text style={styles.itemText}>{title}</Text>
                  {selectIndex === index && (
                    <CVNIcon
                      icon="https://global.chervongroup.com/sites/default/files/styles/homepage_story_thumbnail/public/home_image/blower_0.jpg?itok=Zr1eMB8v"
                      size={12}
                    />
                  )}
                </View>
                {index < data.length - 1 && (
                  <LineView style={{marginHorizontal: 17}} />
                )}
              </View>
            </TouchableWithoutFeedback>
          </View>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  itemContainer: {
    justifyContent: 'space-between',
  },
  itemView: {
    height: 49.5,
    flex: 1,
    backgroundColor: '#ffffff',
    paddingHorizontal: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  itemText: {
    marginLeft: 2,
  },
});
