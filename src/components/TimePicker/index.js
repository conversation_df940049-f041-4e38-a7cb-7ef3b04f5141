import React, {useEffect, useState} from 'react';
import Modal from 'react-native-modal';
import {View, StyleSheet, TouchableOpacity, Image} from 'react-native';
import {Colors} from './Themes';
import TimePicker from './TimePicker';
import {WhiteSpace} from '../WhiteSpace';
import {isIphoneX} from '@utils/device/index.js';
import dayjs from 'dayjs';
import CustomText from '../CustomText';

const Index = ({
  title = 'Choose time',
  confirmText = 'Done',
  onClose,
  onSelect,
  visible,
  value,
  is12HourFormat = false,
  closeSource,
}) => {
  const [innerTime, setInnerTime] = useState(value || dayjs().format('HH:mm'));

  useEffect(() => {
    setInnerTime(value);
  }, [value]);

  const onChange = val => {
    setInnerTime(val);
  };

  const onConfirm = () => {
    onSelect(innerTime);
  };

  return (
    <View>
      <Modal
        isVisible={visible}
        onBackdropPress={onClose}
        animationInTiming={200}
        animationOutTiming={200}
        style={styles.modal}>
        <View style={styles.container}>
          <View style={styles.header}>
            <TouchableOpacity
              onPress={onClose}
              hitSlop={{top: 10, right: 10, left: 10, bottom: 10}}>
              <Image source={closeSource} style={{width: 21, height: 21}} />
            </TouchableOpacity>
            <CustomText fontWeight={500} style={styles.title}>
              {title}
            </CustomText>
            <TouchableOpacity
              hitSlop={{top: 10, right: 10, left: 10, bottom: 10}}
              onPress={onConfirm}>
              <CustomText fontWeight={500} style={styles.confirm}>
                {confirmText}
              </CustomText>
            </TouchableOpacity>
          </View>

          <View style={styles.pickerContainer}>
            <TimePicker
              value={innerTime}
              is12HourFormat={is12HourFormat}
              onChangeValue={onChange}
            />
          </View>
          <WhiteSpace size={isIphoneX ? 34 : 0} />
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  modal: {
    flex: 1,
    justifyContent: 'flex-end',
    margin: 0,
  },
  container: {
    backgroundColor: Colors.white,
    borderTopStartRadius: 10,
    borderTopEndRadius: 10,
    paddingBottom: 41,
  },
  label: {
    flexDirection: 'row',
    justifyContent: 'center',
    padding: 4,
  },
  pickerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  boldCenterText: {
    fontSize: 14,
    color: '#000000',
    fontWeight: '400',
    textAlign: 'center',
  },
  warningText: {
    fontSize: 12,
    textAlign: 'center',
    color: Colors.red,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 67,
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 17,
    color: '#000000',
    fontWeight: '500',
  },
  confirm: {
    color: '#77BC1F',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default Index;
