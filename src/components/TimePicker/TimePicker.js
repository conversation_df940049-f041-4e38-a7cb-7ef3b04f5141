import React, {useState, useMemo} from 'react';
import {View, StyleSheet, Dimensions} from 'react-native';
import ScrollPicker from 'react-native-picker-scrollview';
import {Colors} from './Themes';
import {findIndex, flatten} from 'lodash-es';
import {convertTo12HourFormat} from '@utils/time';
import CustomText from '../CustomText';

const createHourList = is12HourFormat => {
  const hoursList = flatten(
    Array.from({length: 24}, (_, index) => {
      const prefix = index <= 9 ? `0${index}` : `${index}`;
      return [`${prefix}:00`, `${prefix}:30`];
    }),
  );
  return is12HourFormat ? hoursList.map(convertTo12HourFormat) : hoursList;
};

const TimePicker = ({onChangeValue, value, is12HourFormat = false}) => {
  const [time, setTime] = useState(value);
  const hourList = useMemo(
    () => createHourList(is12HourFormat),
    [is12HourFormat],
  );
  const _currentIndex = findIndex(hourList, item => item === time);
  const [currentIndex, setCurrentIndex] = useState(_currentIndex);

  const onChange = (data, index) => {
    setTime(data);
    setCurrentIndex(index);
    onChangeValue(data);
  };
  return (
    <View style={styles.container}>
      <ScrollPicker
        dataSource={hourList}
        itemHeight={42.5}
        wrapperColor={Colors.white}
        renderItem={(data, index, isSelected) => {
          const dataList = data.split(':');
          const [hour, minutePeriod] = dataList;
          let minute = minutePeriod;
          let period = '';
          if (minutePeriod.includes(' ') && is12HourFormat) {
            [minute, period] = minutePeriod.split(' ');
          }
          const isAfternoon = period === 'PM';
          const textStyle = isSelected ? styles.active : styles.inactive;
          return (
            <View style={styles.textContainer}>
              <CustomText style={textStyle}>{hour}</CustomText>
              <CustomText style={textStyle}>{minute}</CustomText>
              {period ? (
                <CustomText style={textStyle}>
                  {isAfternoon ? 'PM' : 'AM'}
                </CustomText>
              ) : null}
            </View>
          );
        }}
        selectedIndex={currentIndex}
        highlightColor={Colors.line}
        onValueChange={onChange}
      />
    </View>
  );
};

const DEVICE_WIDTH = Dimensions.get('window').width;

const styles = StyleSheet.create({
  container: {
    width: '80%',
    flexDirection: 'row',
    backgroundColor: Colors.white,
  },
  textContainer: {
    alignItems: 'center',
    justifyContent: 'space-around',
    flexDirection: 'row',
    width: DEVICE_WIDTH > 375 ? 180 : 140,
  },
  active: {
    color: Colors.black,
    fontSize: 23,
  },
  inactive: {
    color: Colors.grey,
    fontSize: 18,
  },
});

export default TimePicker;
