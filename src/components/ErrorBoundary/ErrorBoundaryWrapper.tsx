/**
 * @description 错误边界包装器 - 由于React限制，错误边界必须是类组件
 */

import React, {Component, ErrorInfo as ReactErrorInfo} from 'react';
import {
  ErrorBoundaryProps,
  ErrorBoundaryState,
  ErrorDetails,
  ErrorInfo,
} from './types';
import {
  GlobalErrorFallback,
  PageErrorFallback,
  ComponentErrorFallback,
} from './ErrorFallback';
import errorReportService from './ErrorReportService';
import logger from '../../utils/logger';

class ErrorBoundaryWrapper extends Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    // 更新state以显示错误UI
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random()
        .toString(36)
        .substring(2, 11)}`,
    };
  }

  constructor(props: ErrorBoundaryProps) {
    super(props);

    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      retryCount: 0,
    };
  }

  componentDidCatch(error: Error, errorInfo: ReactErrorInfo): void {
    // 转换React错误信息格式
    const customErrorInfo: ErrorInfo = {
      componentStack: errorInfo.componentStack,
      errorBoundary: errorInfo.errorBoundary,
      errorBoundaryStack: errorInfo.errorBoundaryStack,
    };

    // 更新错误信息
    this.setState({
      errorInfo: customErrorInfo,
    });

    // 记录错误
    this.logError(error, customErrorInfo);

    // 报告错误
    this.reportError(error, customErrorInfo).catch((reportError: Error) => {
      logger.error('Failed to report error in componentDidCatch:', reportError);
    });

    // 调用外部错误处理函数
    if (this.props.onError) {
      this.props.onError(error, customErrorInfo);
    }
  }

  componentWillUnmount(): void {
    // 清理资源
  }

  /**
   * 记录错误日志
   */
  private readonly logError = (error: Error, errorInfo: ErrorInfo): void => {
    const {level = 'component', name} = this.props;
    const namePrefix = name ? `:${name}` : '';
    const logPrefix = `[ErrorBoundary:${level}${namePrefix}] Error caught:`;

    logger.error(logPrefix, {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
      errorInfo,
      retryCount: this.state.retryCount,
    });
  };

  /**
   * 报告错误到远程服务
   */
  private readonly reportError = async (
    error: Error,
    errorInfo: ErrorInfo,
  ): Promise<void> => {
    try {
      const {level = 'component'} = this.props;

      const errorDetails: ErrorDetails = {
        error,
        errorInfo,
        timestamp: Date.now(),
        route: this.getCurrentRoute(),
      };

      // 根据错误级别决定报告方式
      if (level === 'global') {
        await errorReportService.reportCrash(errorDetails);
      } else {
        await errorReportService.reportError(errorDetails);
      }
    } catch (reportingError) {
      logger.error('Failed to report error:', reportingError);
    }
  };

  /**
   * 获取当前路由信息
   */
  private readonly getCurrentRoute = (): string => {
    // 这里可以集成导航状态获取当前路由
    // 例如使用 @react-navigation/native 的 useNavigationState
    return 'unknown';
  };

  /**
   * 重置错误状态
   */
  private readonly resetError = (): void => {
    const {maxRetries = 3} = this.props;

    this.setState(prevState => {
      const newRetryCount = prevState.retryCount + 1;

      if (newRetryCount > maxRetries) {
        logger.warn(`Max retries (${maxRetries}) exceeded for error boundary`);
        return null; // 不更新状态
      }

      logger.warn(`Error boundary reset, retry count: ${newRetryCount}`);

      return {
        hasError: false,
        error: null,
        errorInfo: null,
        errorId: null,
        retryCount: newRetryCount,
      };
    });
  };

  /**
   * 检查是否可以重试
   */
  private readonly canRetry = (): boolean => {
    const {enableRetry = true, maxRetries = 3} = this.props;
    return enableRetry && this.state.retryCount < maxRetries;
  };

  /**
   * 获取错误回退组件
   */
  private readonly getFallbackComponent = (): React.ComponentType<
    import('./types').ErrorFallbackProps
  > => {
    const {fallback, level = 'component'} = this.props;

    if (fallback) {
      return fallback;
    }

    // 根据级别返回默认回退组件
    switch (level) {
      case 'global':
        return GlobalErrorFallback;
      case 'page':
        return PageErrorFallback;
      case 'component':
      default:
        return ComponentErrorFallback;
    }
  };

  render(): React.ReactNode {
    if (this.state.hasError) {
      const FallbackComponent = this.getFallbackComponent();
      const {level = 'component', name} = this.props;

      return (
        <FallbackComponent
          error={this.state.error}
          errorInfo={this.state.errorInfo}
          resetError={this.resetError}
          retryCount={this.state.retryCount}
          canRetry={this.canRetry()}
          level={level}
          name={name}
        />
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundaryWrapper;
