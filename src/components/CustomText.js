import React, {memo} from 'react';
import {Text, StyleSheet} from 'react-native';
import PropTypes from 'prop-types';

// 字体权重常量
const FONT_WEIGHTS = {
  REGULAR: 400,
  MEDIUM: 500,
  SEMI_BOLD: 600,
  BOLD: 700,
};

// 支持的字体家族
const FONT_FAMILY_TYPES = {
  WORK_SANS: 'WorkSans',
  RUBIK: 'Rubik',
};

// 字体名称映射表
const FONT_FAMILIES = {
  [FONT_FAMILY_TYPES.WORK_SANS]: {
    [FONT_WEIGHTS.REGULAR]: 'WorkSans-Regular',
    [FONT_WEIGHTS.MEDIUM]: 'WorkSans-Medium',
    [FONT_WEIGHTS.SEMI_BOLD]: 'WorkSans-SemiBold',
    [FONT_WEIGHTS.BOLD]: 'WorkSans-Bold',
  },
  [FONT_FAMILY_TYPES.RUBIK]: {
    [FONT_WEIGHTS.REGULAR]: 'Rubik-Regular',
    [FONT_WEIGHTS.MEDIUM]: 'Rubik-Medium',
    [FONT_WEIGHTS.SEMI_BOLD]: 'Rubik-SemiBold',
    // Rubik Bold 字体文件不存在，回退到 SemiBold
    [FONT_WEIGHTS.BOLD]: 'Rubik-SemiBold',
  },
};

// 默认配置
const DEFAULT_CONFIG = {
  FONT_FAMILY: FONT_FAMILY_TYPES.WORK_SANS,
  FONT_WEIGHT: FONT_WEIGHTS.REGULAR,
  FONT_SIZE: 16,
};

// 字体名称缓存 - 性能优化
const FONT_NAME_CACHE = new Map();

/**
 * 获取字体名称的工具函数 - 带缓存优化
 * @param {string} fontFamily - 字体家族
 * @param {number} fontWeight - 字体权重
 * @returns {string} 实际的字体文件名
 */
const getFontName = (fontFamily, fontWeight) => {
  const key = `${fontFamily}-${fontWeight}`;

  // 检查缓存
  if (FONT_NAME_CACHE.has(key)) {
    return FONT_NAME_CACHE.get(key);
  }

  // 计算字体名称
  const fontMap =
    FONT_FAMILIES[fontFamily] || FONT_FAMILIES[DEFAULT_CONFIG.FONT_FAMILY];
  const fontName = fontMap[fontWeight] || fontMap[DEFAULT_CONFIG.FONT_WEIGHT];

  // 缓存结果
  FONT_NAME_CACHE.set(key, fontName);
  return fontName;
};

/**
 * 过滤样式中的字体相关属性，避免冲突
 * @param {Object} style - 原始样式对象
 * @returns {Object} 过滤后的样式对象
 */
const filterFontStyles = style => {
  if (!style) {
    return {};
  }

  const flattenedStyle = StyleSheet.flatten(style) || {};
  // 移除可能冲突的字体属性
  const cleanStyle = {...flattenedStyle};
  delete cleanStyle.fontFamily;
  delete cleanStyle.fontWeight;

  return cleanStyle;
};

/**
 * 自定义Text组件，支持根据字体和权重自动选择正确的字体文件
 *
 * 特性：
 * - 🚀 字体名称缓存优化
 * - 🎨 支持多种字体家族和权重
 * - 🔧 自动处理样式冲突
 * - 📱 React Native 优化
 *
 * @example
 * // 基础使用
 * <CustomText>Hello World</CustomText>
 *
 * // 指定字体和权重
 * <CustomText fontFamily="Rubik" fontWeight={600}>Bold Text</CustomText>
 *
 * // 自定义样式（会自动过滤fontFamily和fontWeight冲突）
 * <CustomText style={{color: 'red', fontSize: 18, fontFamily: 'Arial'}}>
 *   Styled Text
 * </CustomText>
 *
 * @param {Object} props - 组件属性
 * @param {React.ReactNode} props.children - 文本内容
 * @param {string} [props.fontFamily='WorkSans'] - 字体家族
 * @param {number} [props.fontWeight=400] - 字体权重
 * @param {Object} [props.style] - 额外的样式
 * @param {Object} [props.rest] - 其他Text组件支持的属性
 * @returns {React.ReactElement} CustomText组件
 */
const CustomText = ({
  children,
  fontFamily = DEFAULT_CONFIG.FONT_FAMILY,
  fontWeight = DEFAULT_CONFIG.FONT_WEIGHT,
  style,
  ...rest
}) => {
  const fontName = getFontName(fontFamily, fontWeight);

  // 过滤掉可能冲突的样式属性
  const cleanStyle = filterFontStyles(style);

  return (
    <Text style={[styles.text, {fontFamily: fontName}, cleanStyle]} {...rest}>
      {children}
    </Text>
  );
};

// PropTypes 验证
CustomText.propTypes = {
  children: PropTypes.node.isRequired,
  fontFamily: PropTypes.oneOf([
    FONT_FAMILY_TYPES.WORK_SANS,
    FONT_FAMILY_TYPES.RUBIK,
  ]),
  fontWeight: PropTypes.oneOf([
    FONT_WEIGHTS.REGULAR,
    FONT_WEIGHTS.MEDIUM,
    FONT_WEIGHTS.SEMI_BOLD,
    FONT_WEIGHTS.BOLD,
  ]),
  style: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
};

// 默认样式
const styles = StyleSheet.create({
  text: {
    fontSize: DEFAULT_CONFIG.FONT_SIZE,
    color: '#3C3936', // 默认文本颜色
    includeFontPadding: false, // Android优化：移除额外的字体padding
    textAlignVertical: 'center', // Android优化：垂直居中对齐
  },
});

// 导出常量供外部使用
export {FONT_WEIGHTS, FONT_FAMILY_TYPES};

export default memo(CustomText);
