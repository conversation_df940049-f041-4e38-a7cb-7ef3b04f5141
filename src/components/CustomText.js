import React, {memo, useCallback} from 'react';
import {Text, StyleSheet, TextStyle} from 'react-native';
import PropTypes from 'prop-types';
// 字体名称映射表
const FONT_FAMILIES = {
  WorkSans: {
    400: 'WorkSans-Regular',
    500: 'WorkSans-Medium',
    600: 'WorkSans-SemiBold',
    700: 'WorkSans-Bold',
  },
  Rubik: {
    400: 'Rubik-Regular',
    500: 'Rubik-Medium',
    600: 'Rubik-SemiBold',
  },
};

// 默认字体和权重
const DEFAULT_FONT = 'WorkSans';
const DEFAULT_WEIGHT = 400;

/**
 * 自定义Text组件，支持根据字体和权重自动选择正确的字体文件
 * @param {Object} props - 组件属性
 * @param {string} [props.fontFamily='WorkSans'] - 字体家族，可选 'WorkSans' 或 'Rubik'
 * @param {number} [props.fontWeight=400] - 字体权重，支持 400、500、600、700
 * @param {Object} [props.style] - 额外的样式
 */
const CustomText = ({
  children,
  fontFamily = DEFAULT_FONT,
  fontWeight = DEFAULT_WEIGHT,
  style,
  ...rest
}) => {
  // 根据字体和权重获取正确的字体名称
  const getFontName = useCallback(() => {
    const fontMap = FONT_FAMILIES[fontFamily] ?? FONT_FAMILIES[DEFAULT_FONT];
    return fontMap[fontWeight] ?? fontMap[DEFAULT_WEIGHT];
  }, [fontFamily, fontWeight]);
  const fontName = getFontName();
  return (
    <Text style={[styles.text, {fontFamily: fontName}, style]} {...rest}>
      {children}
    </Text>
  );
};

// 在组件定义之后添加propTypes
CustomText.propTypes = {
  children: PropTypes.node.isRequired,
  fontFamily: PropTypes.oneOf(['WorkSans', 'Rubik']),
  fontWeight: PropTypes.oneOf([400, 500, 600, 700]),
  style: TextStyle, // 添加style的propTypes声明
};

const styles = StyleSheet.create({
  text: {
    fontSize: 16,
  },
});

export default memo(CustomText);
