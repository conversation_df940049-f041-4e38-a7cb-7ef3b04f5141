import React, {memo} from 'react';
import {Text, StyleSheet} from 'react-native';
import PropTypes from 'prop-types';

// 字体权重常量
const FONT_WEIGHTS = {
  REGULAR: 400,
  MEDIUM: 500,
  SEMI_BOLD: 600,
  BOLD: 700,
};

// 支持的字体家族
const FONT_FAMILY_TYPES = {
  WORK_SANS: 'WorkSans',
  RUBIK: 'Rubik',
};

// 字体名称映射表
const FONT_FAMILIES = {
  [FONT_FAMILY_TYPES.WORK_SANS]: {
    [FONT_WEIGHTS.REGULAR]: 'WorkSans-Regular',
    [FONT_WEIGHTS.MEDIUM]: 'WorkSans-Medium',
    [FONT_WEIGHTS.SEMI_BOLD]: 'WorkSans-SemiBold',
    [FONT_WEIGHTS.BOLD]: 'WorkSans-Bold',
  },
  [FONT_FAMILY_TYPES.RUBIK]: {
    [FONT_WEIGHTS.REGULAR]: 'Rubik-Regular',
    [FONT_WEIGHTS.MEDIUM]: 'Rubik-Medium',
    [FONT_WEIGHTS.SEMI_BOLD]: 'Rubik-SemiBold',
  },
};

// 默认配置
const DEFAULT_CONFIG = {
  FONT_FAMILY: FONT_FAMILY_TYPES.WORK_SANS,
  FONT_WEIGHT: FONT_WEIGHTS.REGULAR,
  FONT_SIZE: 16,
};

/**
 * 获取字体名称的工具函数
 * @param {string} fontFamily - 字体家族
 * @param {number} fontWeight - 字体权重
 * @returns {string} 实际的字体文件名
 */
const getFontName = (fontFamily, fontWeight) => {
  const fontMap =
    FONT_FAMILIES[fontFamily] || FONT_FAMILIES[DEFAULT_CONFIG.FONT_FAMILY];
  return fontMap[fontWeight] || fontMap[DEFAULT_CONFIG.FONT_WEIGHT];
};

/**
 * 自定义Text组件，支持根据字体和权重自动选择正确的字体文件
 *
 * @example
 * // 基础使用
 * <CustomText>Hello World</CustomText>
 *
 * // 指定字体和权重
 * <CustomText fontFamily="Rubik" fontWeight={600}>Bold Text</CustomText>
 *
 * // 自定义样式
 * <CustomText style={{color: 'red', fontSize: 18}}>Styled Text</CustomText>
 *
 * @param {Object} props - 组件属性
 * @param {React.ReactNode} props.children - 文本内容
 * @param {string} [props.fontFamily='WorkSans'] - 字体家族，可选 'WorkSans' 或 'Rubik'
 * @param {number} [props.fontWeight=400] - 字体权重，支持 400、500、600、700
 * @param {Object} [props.style] - 额外的样式
 * @param {Object} [props.rest] - 其他Text组件支持的属性
 * @returns {React.ReactElement} CustomText组件
 */
const CustomText = ({
  children,
  fontFamily = DEFAULT_CONFIG.FONT_FAMILY,
  fontWeight = DEFAULT_CONFIG.FONT_WEIGHT,
  style,
  ...rest
}) => {
  const fontName = getFontName(fontFamily, fontWeight);

  return (
    <Text style={[styles.text, {fontFamily: fontName}, style]} {...rest}>
      {children}
    </Text>
  );
};

// PropTypes 验证
CustomText.propTypes = {
  children: PropTypes.node.isRequired,
  fontFamily: PropTypes.oneOf([
    FONT_FAMILY_TYPES.WORK_SANS,
    FONT_FAMILY_TYPES.RUBIK,
  ]),
  fontWeight: PropTypes.oneOf([
    FONT_WEIGHTS.REGULAR,
    FONT_WEIGHTS.MEDIUM,
    FONT_WEIGHTS.SEMI_BOLD,
    FONT_WEIGHTS.BOLD,
  ]),
  style: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
};

// 默认样式
const styles = StyleSheet.create({
  text: {
    fontSize: DEFAULT_CONFIG.FONT_SIZE,
    color: '#000000', // 默认文本颜色
    includeFontPadding: false, // Android优化：移除额外的字体padding
    textAlignVertical: 'center', // Android优化：垂直居中对齐
  },
});

export default memo(CustomText);
