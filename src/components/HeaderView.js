import React from 'react';
import {View, TouchableOpacity, Platform} from 'react-native';
import {Header as HeaderRNE} from 'react-native-elements';
import CustomStyleSheet from '@utils/style/index.js';
import {LeftArrowIcon, MoreIcon} from '@components/Icon';
import CustomText from './CustomText';
import {DEVICE_WIDTH} from '@utils/device';

export const HeaderView = ({
  style,
  title = '',
  rightTitle,
  onLeftPress = () => {},
  onRightPress = () => {},
  rightElement = null,
  showMore = false,
  mode = 'normal',
}) => {
  const androidProps =
    Platform.OS === 'android'
      ? {
          statusBarProps: {backgroundColor: 'transparent', translucent: true},
          barStyle: 'dark-content',
        }
      : {};
  const newTitle =
    typeof title === 'string' ? (
      <CustomText fontWeight={500} fontFamily="Rubik" style={styles.heading}>
        {title?.toUpperCase()}
      </CustomText>
    ) : (
      title
    );
  return (
    <HeaderRNE
      containerStyle={[styles.container, style]}
      leftComponent={
        <View style={styles.headerLeft}>
          <TouchableOpacity
            onPress={onLeftPress}
            hitSlop={{top: 10, right: 10, left: 10, bottom: 10}}>
            <LeftArrowIcon
              strokeColor={mode === 'light' ? '#FFFFFF' : '#3C3936'}
              style={styles.leftIcon}
            />
          </TouchableOpacity>
        </View>
      }
      centerComponent={{text: newTitle, style: styles.heading}}
      rightComponent={
        <View style={styles.headerRight}>
          <TouchableOpacity
            onPress={onRightPress}
            hitSlop={{top: 10, right: 10, left: 10, bottom: 10}}>
            {rightElement ? (
              rightElement
            ) : showMore ? (
              <MoreIcon
                strokeColor={mode === 'light' ? '#FFFFFF' : '#3C3936'}
              />
            ) : (
              rightTitle && (
                <CustomText fontFamily="Rubik" style={styles.rightTitle}>
                  {rightTitle}
                </CustomText>
              )
            )}
          </TouchableOpacity>
        </View>
      }
      {...androidProps}
    />
  );
};

const styles = CustomStyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
  },
  leftIcon: {
    marginLeft: 5,
  },
  heading: {
    color: '#3C3936',
    fontSize: 22,
    width: DEVICE_WIDTH - 100,
    textAlign: 'center',
  },
  headerLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerRight: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    color: '#000000',
    paddingRight: 5,
  },
  moreView: {
    width: 11,
    height: 22,
  },
  rightTitle: {
    color: '#000000',
    fontSize: 15,
  },
});
