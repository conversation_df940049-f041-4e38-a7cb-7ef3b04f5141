import React, {useState, useRef, useEffect, useMemo} from 'react';
import {View, TouchableOpacity, StyleSheet, Animated} from 'react-native';
import CustomText from '../CustomText';

const LargerSwitch = ({
  choices = ['', ''],
  value = 0,
  style = {},
  activeBackgroundColor = '#77BC1F',
  containerHeight = 50,
  boxStyle = {},
  boxWidth = 143,
  boxHeight = 42,
  paddingWidth = 4,
  disabled = false,
  onChange = () => {},
}) => {
  const [currentIndex, setCurrentIndex] = useState(value);
  const [currentWidth, setCurrentWidth] = useState(300);

  const translateAnim = useRef(new Animated.Value(0)).current;

  const ref = useRef(null);

  const transformDistance = useMemo(() => {
    return currentWidth - boxWidth - paddingWidth * 2;
  }, [currentWidth]);

  useEffect(() => {
    Animated.timing(translateAnim, {
      toValue: currentIndex === 0 ? 0 : transformDistance,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [currentIndex, transformDistance]);

  const handleSwitch = index => {
    // console.log('handleSwitch index:', index);
    if (disabled || index === currentIndex) {
      return;
    }
    setCurrentIndex(index);
    Animated.timing(translateAnim, {
      toValue: index === 0 ? 0 : transformDistance,
      duration: 300,
      useNativeDriver: true,
    }).start();
    onChange(index);
  };

  return (
    <View
      style={[
        styles.container,
        style,
        {
          height: containerHeight,
          paddingHorizontal: paddingWidth,
        },
      ]}
      ref={ref}
      onLayout={() => {
        if (ref) {
          ref.current?.measure((x, y, width, height) => {
            setCurrentWidth(width);
          });
        }
      }}
      // {...panResponder.panHandlers}
    >
      <Animated.View
        style={[
          styles.activeBox,
          boxStyle,
          {
            backgroundColor: activeBackgroundColor,
            width: boxWidth,
            height: boxHeight,
            transform: [
              {
                translateX: translateAnim,
              },
            ],
          },
        ]}
      />
      {choices.slice(0, 2).map((item, index) => (
        <TouchableOpacity
          key={item + index}
          disabled={disabled}
          style={[styles.contentBox, {width: boxWidth}]}
          onPress={() => handleSwitch(index)}>
          <CustomText
            fontWeight={500}
            style={[
              styles.contentText,
              {
                color:
                  activeBackgroundColor === '#FFFFFF'
                    ? '#000000'
                    : index === currentIndex
                    ? '#FFFFFF'
                    : '#000000',
              },
            ]}>
            {item}
          </CustomText>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#EDEEEF',
    borderRadius: 25,
    height: 50,
    flexDirection: 'row',
    paddingHorizontal: 4,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  contentBox: {
    width: 143,
    height: 42,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentText: {
    fontSize: 14,
    lineHeight: 16.5,
    fontWeight: '500',
    color: '#000000',
  },
  activeBox: {
    backgroundColor: '#77BC1F',
    borderRadius: 21,
    position: 'absolute',
    width: 143,
    height: 42,
    left: 4,
  },
});

export default LargerSwitch;
