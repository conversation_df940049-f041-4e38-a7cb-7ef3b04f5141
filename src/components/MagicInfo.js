import React, {isValidElement} from 'react';
import {View, Text, Platform} from 'react-native';
import CustomStyleSheet from '@utils/style/index.js';
import MagicBox from '@components/MagicBox';
import RemainingBatteryIcon from '@components/Icon/RemainingBatteryIcon';
import WattageIcon from '@components/Icon/WattageIcon';
import TotalTimeIcon from '@components/Icon/TotalTimeIcon';
import PropTypes from 'prop-types';

const BasicInfo = ({
  value = 0,
  connected = false,
  icon = null,
  unit = '',
  text = '',
  children = null,
}) => {
  const dataFormat = item => {
    return isValidValue(item) ? `${item}` : '--';
  };

  const isValidValue = item => {
    return connected && item >= 0;
  };

  const defaultChildern = isValidValue(value) ? (
    <Text>
      <Text style={styles.value}>{dataFormat(value)}</Text>
      {isValidValue(value) ? <Text style={styles.unit}>{unit}</Text> : null}
    </Text>
  ) : (
    <Text style={styles.value}>--</Text>
  );

  return (
    <View style={styles.basicInfoContainer}>
      <MagicBox style={styles.magicBoxContainer}>
        {icon}
        {isAndroid && ' '}
        {children && isValidElement(children) ? children : defaultChildern}
      </MagicBox>
      <Text style={styles.description}>{text}</Text>
    </View>
  );
};

const BatteryInfo = ({
  value = 0,
  connected = false,
  text = 'Remaining Battery',
  ...props
}) => {
  const iconStyle = connected ? {} : styles.opacityStyle;

  return (
    <BasicInfo
      value={value}
      connected={connected}
      icon={
        <RemainingBatteryIcon
          width={20}
          height={20}
          style={[styles.icon, iconStyle]}
        />
      }
      unit="%"
      text={text}
      {...props}
    />
  );
};

const EnergyInfo = ({
  value = 0,
  connected = false,
  text = 'Remaining Energy',
  ...props
}) => {
  const iconStyle = connected ? {} : styles.opacityStyle;

  return (
    <BasicInfo
      value={value}
      connected={connected}
      icon={
        <WattageIcon width={20} height={20} style={[styles.icon, iconStyle]} />
      }
      unit="Ah"
      text={text}
      {...props}
    />
  );
};

const TimeInfo = ({
  value = 0,
  connected = false,
  text = 'Remaining Time',
  ...props
}) => {
  const iconStyle = connected ? {} : styles.opacityStyle;

  return (
    <BasicInfo
      value={value}
      connected={connected}
      icon={
        <TotalTimeIcon
          width={20}
          height={20}
          style={[styles.icon, iconStyle]}
        />
      }
      unit="h"
      text={text}
      {...props}
    />
  );
};

const MagicInfo = ({children}) => {
  return <View style={styles.container}>{children}</View>;
};

MagicInfo.propTypes = {
  children: PropTypes.node,
};

BasicInfo.propTypes = {
  value: PropTypes.number,
  connected: PropTypes.bool,
  icon: PropTypes.node,
  unit: PropTypes.string,
  text: PropTypes.string,
  children: PropTypes.node,
};

BatteryInfo.propTypes = {
  value: PropTypes.number,
  connected: PropTypes.bool,
  text: PropTypes.string,
};

EnergyInfo.propTypes = {
  value: PropTypes.number,
  connected: PropTypes.bool,
  text: PropTypes.string,
};

TimeInfo.propTypes = {
  value: PropTypes.number,
  connected: PropTypes.bool,
  text: PropTypes.string,
};

const isAndroid = Platform.OS === 'android';

const styles = CustomStyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 21,
    paddingBottom: 26,
  },
  magicBoxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2.5,
  },
  basicInfoContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flexBasis: '50%',
  },
  icon: {
    marginRight: 4,
  },
  value: {
    fontSize: 30,
    fontWeight: '500',
    lineHeight: 36,
    color: '#000',
  },
  unit: {
    fontSize: 15,
    fontWeight: '500',
    color: '#000',
  },
  description: {
    fontSize: 13,
    fontWeight: '400',
    color: '#666666',
  },
  opacityStyle: {
    opacity: 0.5,
  },
});

MagicInfo.BasicInfo = BasicInfo;
MagicInfo.BatteryInfo = BatteryInfo;
MagicInfo.EnergyInfo = EnergyInfo;
MagicInfo.TimeInfo = TimeInfo;

export default MagicInfo;
