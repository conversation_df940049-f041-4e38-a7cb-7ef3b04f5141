import React, {memo, useEffect, useRef, useState, useMemo} from 'react';
import {View, TouchableWithoutFeedback, StyleSheet} from 'react-native';
import NoData from '../NoData';
import {DEVICE_WIDTH} from '@utils/device/index.js';
import PropTypes from 'prop-types';
import {map} from 'lodash-es';
import {ECharts} from '@cvn/react-native-echarts-wrapper';

const initialWidth = DEVICE_WIDTH - 30;

const LineChart = ({
  list = [],
  labels = [],
  formatter,
  onPress = () => {},
  style = {},
}) => {
  const isBlank = list.length === 0;

  let datasetList = map(list, item => item || 0);

  const echartsRef = useRef(null);
  const viewRef = useRef(null);

  const [currentWidth, setCurrentWidth] = useState(initialWidth);

  const option = useMemo(
    () => ({
      grid: {
        top: 10,
        left: 10,
        height: 160,
        right: 10,
        containLabel: true,
      },
      xAxis: {
        boundaryGap: false,
        type: 'category',
        data: labels,
        axisTick: {
          show: false, // 刻度不显示
        },
        axisLabel: {
          color: '#000000',
          fontSize: 13,
          padding: [0, 0, 0, 5],
          // interval: 1
        },
        axisLine: {
          show: false, // 不显示轴线，会跟分割线 splitLine 保持一致
        },
        splitLine: {
          show: true, // 是否显示分隔线。默认数值轴显示，类目轴不显示
          lineStyle: {
            color: '#919191',
            opacity: 0.3,
            width: 0.5,
            type: 'dashed',
          },
          type: 'solid', // 坐标轴线线的类型（solid实线类型；dashed虚线类型；dotted点状类型）
        },
      },
      yAxis: {
        type: 'value',
        name: '使用时长',
        position: 'right',
        axisTick: {
          show: false, // 刻度不显示
        },
        offset: 10, // 偏移y轴线向右10
        splitNumber: 5, // 分段数
        axisLine: {
          show: false, // 不显示轴线，跟分割线 splitLine 保持一致
        },
        axisLabel: {
          color: '#000000B3',
          fontSize: 12,
          formatter:
            formatter ||
            function (value) {
              if (value) {
                return `${value}hr`;
              } else {
                return value;
              }
            },
        },
        splitLine: {
          show: true, // 是否显示分隔线。默认数值轴显示，类目轴不显示
          // interval: '1',    // 坐标轴刻度标签的显示间隔，在类目轴中有效.0显示所有
          lineStyle: {
            color: '#9191914D',
            width: 0.5,
          },
        },
      },
      series: [
        {
          data: datasetList,
          type: 'line',
          symbol: 'circle',
          symbolSize: 3,
          itemStyle: {
            color: '#77BC1F',
          },
          lineStyle: {
            width: 1,
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(119, 188, 31, 0.7)',
                },
                {
                  offset: 1,
                  color: 'rgba(119, 188, 31, 0)',
                },
              ],
            },
          },
        },
      ],
    }),
    [datasetList, formatter, labels],
  );

  useEffect(() => {
    echartsRef.current?.setOption(option);
  }, [option]);

  return isBlank ? (
    <NoData />
  ) : (
    <TouchableWithoutFeedback onPress={onPress}>
      <View
        style={style}
        ref={viewRef}
        onLayout={() => {
          viewRef?.current?.measure((x, y, width) => {
            setCurrentWidth(width);
          });
        }}>
        <View style={[styles.container, {width: currentWidth}]}>
          <ECharts ref={echartsRef} option={option} />
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
};

LineChart.propTypes = {
  list: PropTypes.array,
  labels: PropTypes.array,
  formatter: PropTypes.func,
  onPress: PropTypes.func,
  style: PropTypes.object,
};

const styles = StyleSheet.create({
  container: {
    width: initialWidth,
    height: 170,
  },
});

export default memo(LineChart);
