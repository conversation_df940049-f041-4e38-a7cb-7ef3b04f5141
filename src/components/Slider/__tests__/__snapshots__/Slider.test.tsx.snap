// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Slider component should handle all onLayout actions 1`] = `
<View
  testID="wrapper"
>
  <View
    accessibilityRole="adjustable"
    accessibilityValue={
      {
        "max": 1,
        "min": 0,
        "now": 0,
      }
    }
    onLayout={[Function]}
    style={
      {
        "height": 40,
        "justifyContent": "center",
      }
    }
    testID="RNE__Slider_Container"
  >
    <View
      onLayout={[Function]}
      style={
        {
          "backgroundColor": "#b3b3b3",
          "borderRadius": 2,
          "height": 4,
        }
      }
      testID="RNE__Slider_Track_maximum"
    />
    <View
      collapsable={false}
      style={
        {
          "backgroundColor": "#3f3f3f",
          "borderRadius": 2,
          "height": 4,
          "marginTop": -20,
          "position": "absolute",
          "width": 150,
        }
      }
      testID="RNE__Slider_Track_minimum"
    />
    <View
      collapsable={false}
      onLayout={[Function]}
      style={
        {
          "backgroundColor": "red",
          "borderRadius": 20,
          "height": 40,
          "position": "absolute",
          "transform": [
            {
              "translateX": 0,
            },
          ],
          "width": 40,
        }
      }
      testID="RNE__Slider_Thumb"
    />
    <View
      onMoveShouldSetResponder={[Function]}
      onMoveShouldSetResponderCapture={[Function]}
      onResponderEnd={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderReject={[Function]}
      onResponderRelease={[Function]}
      onResponderStart={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      onStartShouldSetResponderCapture={[Function]}
      style={
        {
          "backgroundColor": "transparent",
          "bottom": 0,
          "left": 0,
          "marginBottom": -0,
          "marginLeft": -0,
          "marginRight": -0,
          "marginTop": -0,
          "position": "absolute",
          "right": 0,
          "top": 0,
        }
      }
      testID="RNE__Slider_TouchArea"
    />
  </View>
</View>
`;

exports[`Slider component should handle all onLayout actions with vertical orientation 1`] = `
<View
  testID="wrapper"
>
  <View
    accessibilityRole="adjustable"
    accessibilityValue={
      {
        "max": 1,
        "min": 0,
        "now": 0,
      }
    }
    onLayout={[Function]}
    style={
      {
        "alignItems": "center",
        "flexDirection": "column",
        "width": 40,
      }
    }
    testID="RNE__Slider_Container"
  >
    <View
      onLayout={[Function]}
      style={
        {
          "backgroundColor": "#b3b3b3",
          "borderRadius": 2,
          "flex": 1,
          "width": 4,
        }
      }
      testID="RNE__Slider_Track_maximum"
    />
    <View
      collapsable={false}
      style={
        {
          "backgroundColor": "#3f3f3f",
          "borderRadius": 2,
          "flex": 1,
          "height": 150,
          "marginLeft": -20,
          "position": "absolute",
          "width": 4,
        }
      }
      testID="RNE__Slider_Track_minimum"
    />
    <View
      collapsable={false}
      onLayout={[Function]}
      style={
        {
          "backgroundColor": "red",
          "borderRadius": 20,
          "height": 40,
          "position": "absolute",
          "transform": [
            {
              "translateY": 0,
            },
          ],
          "width": 40,
        }
      }
      testID="RNE__Slider_Thumb"
    />
    <View
      onMoveShouldSetResponder={[Function]}
      onMoveShouldSetResponderCapture={[Function]}
      onResponderEnd={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderReject={[Function]}
      onResponderRelease={[Function]}
      onResponderStart={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      onStartShouldSetResponderCapture={[Function]}
      style={
        {
          "backgroundColor": "transparent",
          "bottom": 0,
          "left": 0,
          "marginBottom": -0,
          "marginLeft": -0,
          "marginRight": -0,
          "marginTop": -0,
          "position": "absolute",
          "right": 0,
          "top": 0,
        }
      }
      testID="RNE__Slider_TouchArea"
    />
  </View>
</View>
`;

exports[`Slider component should match snapshot 1`] = `
<View
  testID="wrapper"
>
  <View
    accessibilityRole="adjustable"
    accessibilityValue={
      {
        "max": 1,
        "min": 0,
        "now": 0,
      }
    }
    onLayout={[Function]}
    style={
      {
        "height": 40,
        "justifyContent": "center",
      }
    }
    testID="RNE__Slider_Container"
  >
    <View
      onLayout={[Function]}
      style={
        {
          "backgroundColor": "#b3b3b3",
          "borderRadius": 2,
          "height": 4,
        }
      }
      testID="RNE__Slider_Track_maximum"
    />
    <View
      collapsable={false}
      style={
        {
          "backgroundColor": "#3f3f3f",
          "borderRadius": 2,
          "height": 0,
          "position": "absolute",
          "width": 0,
        }
      }
      testID="RNE__Slider_Track_minimum"
    />
    <View
      collapsable={false}
      onLayout={[Function]}
      style={
        {
          "backgroundColor": "red",
          "borderRadius": 20,
          "height": 40,
          "position": "absolute",
          "transform": [
            {
              "translateX": 0,
            },
          ],
          "width": 40,
        }
      }
      testID="RNE__Slider_Thumb"
    />
    <View
      onMoveShouldSetResponder={[Function]}
      onMoveShouldSetResponderCapture={[Function]}
      onResponderEnd={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderReject={[Function]}
      onResponderRelease={[Function]}
      onResponderStart={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      onStartShouldSetResponderCapture={[Function]}
      style={
        {
          "backgroundColor": "transparent",
          "bottom": 0,
          "left": 0,
          "marginBottom": -0,
          "marginLeft": -0,
          "marginRight": -0,
          "marginTop": -0,
          "position": "absolute",
          "right": 0,
          "top": 0,
        }
      }
      testID="RNE__Slider_TouchArea"
    />
  </View>
</View>
`;

exports[`Slider component should render vertically 1`] = `
<View
  testID="wrapper"
>
  <View
    accessibilityRole="adjustable"
    accessibilityValue={
      {
        "max": 1,
        "min": 0,
        "now": 0,
      }
    }
    onLayout={[Function]}
    style={
      {
        "alignItems": "center",
        "flexDirection": "column",
        "width": 40,
      }
    }
    testID="RNE__Slider_Container"
  >
    <View
      onLayout={[Function]}
      style={
        {
          "backgroundColor": "#b3b3b3",
          "borderRadius": 2,
          "flex": 1,
          "width": 4,
        }
      }
      testID="RNE__Slider_Track_maximum"
    />
    <View
      collapsable={false}
      style={
        {
          "backgroundColor": "#3f3f3f",
          "borderRadius": 2,
          "flex": 1,
          "height": 0,
          "position": "absolute",
          "width": 0,
        }
      }
      testID="RNE__Slider_Track_minimum"
    />
    <View
      collapsable={false}
      onLayout={[Function]}
      style={
        {
          "backgroundColor": "red",
          "borderRadius": 20,
          "height": 40,
          "position": "absolute",
          "transform": [
            {
              "translateY": 0,
            },
          ],
          "width": 40,
        }
      }
      testID="RNE__Slider_Thumb"
    />
    <View
      onMoveShouldSetResponder={[Function]}
      onMoveShouldSetResponderCapture={[Function]}
      onResponderEnd={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderReject={[Function]}
      onResponderRelease={[Function]}
      onResponderStart={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      onStartShouldSetResponderCapture={[Function]}
      style={
        {
          "backgroundColor": "transparent",
          "bottom": 0,
          "left": 0,
          "marginBottom": -0,
          "marginLeft": -0,
          "marginRight": -0,
          "marginTop": -0,
          "position": "absolute",
          "right": 0,
          "top": 0,
        }
      }
      testID="RNE__Slider_TouchArea"
    />
  </View>
</View>
`;

exports[`Slider component should render with ThumbTouchRect 1`] = `
<View
  testID="wrapper"
>
  <View
    accessibilityRole="adjustable"
    accessibilityValue={
      {
        "max": 100,
        "min": 0,
        "now": 0,
      }
    }
    onLayout={[Function]}
    style={
      {
        "height": 40,
        "justifyContent": "center",
      }
    }
    testID="RNE__Slider_Container"
  >
    <View
      onLayout={[Function]}
      style={
        {
          "backgroundColor": "#b3b3b3",
          "borderRadius": 2,
          "height": 4,
        }
      }
      testID="RNE__Slider_Track_maximum"
    />
    <View
      collapsable={false}
      style={
        {
          "backgroundColor": "#3f3f3f",
          "borderRadius": 2,
          "height": 0,
          "position": "absolute",
          "width": 0,
        }
      }
      testID="RNE__Slider_Track_minimum"
    />
    <View
      collapsable={false}
      onLayout={[Function]}
      style={
        {
          "backgroundColor": "red",
          "borderRadius": 20,
          "height": 40,
          "position": "absolute",
          "transform": [
            {
              "translateX": 0,
            },
          ],
          "width": 40,
        }
      }
      testID="RNE__Slider_Thumb"
    />
    <View
      onMoveShouldSetResponder={[Function]}
      onMoveShouldSetResponderCapture={[Function]}
      onResponderEnd={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderReject={[Function]}
      onResponderRelease={[Function]}
      onResponderStart={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      onStartShouldSetResponderCapture={[Function]}
      style={
        {
          "backgroundColor": "orange",
          "bottom": 0,
          "left": 0,
          "marginBottom": -0,
          "marginLeft": -0,
          "marginRight": -0,
          "marginTop": -0,
          "opacity": 0.5,
          "position": "absolute",
          "right": 0,
          "top": 0,
        }
      }
      testID="RNE__Slider_TouchArea"
    >
      <View
        collapsable={false}
        pointerEvents="none"
        style={
          {
            "height": 40,
            "left": 0,
            "top": -20,
            "width": 40,
          }
        }
      />
    </View>
  </View>
</View>
`;

exports[`Slider component should support value changes 1`] = `
<View
  testID="wrapper"
>
  <View
    accessibilityRole="adjustable"
    accessibilityValue={
      {
        "max": 10,
        "min": 1,
        "now": 1,
      }
    }
    onLayout={[Function]}
    style={
      {
        "height": 40,
        "justifyContent": "center",
      }
    }
    testID="RNE__Slider_Container"
  >
    <View
      onLayout={[Function]}
      style={
        {
          "backgroundColor": "#b3b3b3",
          "borderRadius": 2,
          "height": 4,
        }
      }
      testID="RNE__Slider_Track_maximum"
    />
    <View
      collapsable={false}
      style={
        {
          "backgroundColor": "#3f3f3f",
          "borderRadius": 2,
          "height": 0,
          "position": "absolute",
          "width": 0,
        }
      }
      testID="RNE__Slider_Track_minimum"
    />
    <View
      collapsable={false}
      onLayout={[Function]}
      style={
        {
          "backgroundColor": "red",
          "borderRadius": 20,
          "height": 40,
          "position": "absolute",
          "transform": [
            {
              "translateX": 0,
            },
          ],
          "width": 40,
        }
      }
      testID="RNE__Slider_Thumb"
    />
    <View
      onMoveShouldSetResponder={[Function]}
      onMoveShouldSetResponderCapture={[Function]}
      onResponderEnd={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderReject={[Function]}
      onResponderRelease={[Function]}
      onResponderStart={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      onStartShouldSetResponderCapture={[Function]}
      style={
        {
          "backgroundColor": "transparent",
          "bottom": 0,
          "left": 0,
          "marginBottom": -0,
          "marginLeft": -0,
          "marginRight": -0,
          "marginTop": -0,
          "position": "absolute",
          "right": 0,
          "top": 0,
        }
      }
      testID="RNE__Slider_TouchArea"
    />
  </View>
</View>
`;
