import React, {useState, useEffect, isValidElement} from 'react';
import {View, Text, Modal, StyleSheet} from 'react-native';
import {Divider} from 'react-native-elements';
import PressableButton from './PressableButton';

/**
 * 基于 Modal 封装的提示组件，可以确认关闭
 */
export default ({
  visible = false,
  confirmText = 'OK',
  tipText = '',
  children,
  onPress = () => {},
}) => {
  const [isVisible, setIsVisible] = useState(visible);

  useEffect(() => setIsVisible(visible), [visible]);

  return (
    <Modal transparent={true} visible={isVisible} animationType="slide">
      <View style={styles.container}>
        <View style={styles.modal}>
          <View style={styles.content}>
            {isValidElement(children) ? (
              children
            ) : (
              <Text style={styles.tipText}>{tipText}</Text>
            )}
          </View>
          <Divider />
          <PressableButton style={styles.button} onPress={onPress}>
            <Text style={styles.buttonText}>{confirmText}</Text>
          </PressableButton>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  modal: {
    backgroundColor: '#fff',
    marginTop: 5,
    marginBottom: 8.5,
    borderRadius: 10,
  },
  content: {
    width: '70%',
    paddingVertical: 25,
    paddingHorizontal: 20,
  },
  tipText: {
    fontSize: 14,
    fontWeight: '400',
    color: '#000',
  },
  button: {
    padding: 15,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#77BC1F',
    textAlign: 'center',
  },
});
