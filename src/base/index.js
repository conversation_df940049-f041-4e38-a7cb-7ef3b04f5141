import {Component} from 'react';
import {StatusBar, Platform} from 'react-native';
import {mobile} from '@cvn/rn-panel-kit';
import routeProps from '@utils/global/routeProps.js';

// 标记是否已点击，防止重复点击多次
global.isClicked = false;

export default class PageBase extends Component {
  constructor(props) {
    super(props);
    if (Platform.OS === 'android') {
      StatusBar.setBackgroundColor('transparent');
      StatusBar.setBarStyle('dark-content');
      StatusBar.setTranslucent(true);
    }
  }

  componentDidCatch(error, info) {
    console.log('page base catch', error, info);
  }

  /**
   * @param {string} params
   */
  getParams = params => {
    const navigationParams = this.props.route?.params;
    return navigationParams?.[params] || '';
  };

  goBack = () => {
    if (global.isClicked) {
      return;
    } else {
      global.isClicked = true;
      setTimeout(() => {
        global.isClicked = false;
      }, 500);
    }
    let canBack = this.props.navigation.canGoBack();
    if (!canBack) {
      mobile.back();
    } else {
      this.props.navigation.goBack();
    }
  };

  androidGoBack = () => {
    if (routeProps.routes?.length > 1) {
      this.props.navigation.goBack('');
    } else {
      mobile.back();
    }
  };

  /**
   * @param {string} pageName
   * @param {Record<string, any>} params
   * @returns {void}
   */
  goPage = (pageName, params = {}) => {
    if (!pageName) {
      return;
    }
    if (global.isClicked) {
      return;
    } else {
      global.isClicked = true;
      setTimeout(() => {
        global.isClicked = false;
      }, 500);
    }
    const navigation = this.props && this.props.navigation;
    navigation.push(pageName, params);
  };

  /**
   * @param {string} pageName
   * @param {Record<string, any>} params
   * @returns {void}
   */
  replacePage = (pageName, params = {}) => {
    if (!pageName) {
      return;
    }
    if (global.isClicked) {
      return;
    } else {
      global.isClicked = true;
      setTimeout(() => {
        global.isClicked = false;
      }, 500);
    }
    const navigation = this.props && this.props.navigation;
    navigation.replace(pageName, params);
  };
}
