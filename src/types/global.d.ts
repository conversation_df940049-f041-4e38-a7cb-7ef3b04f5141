declare global {
  // eslint-disable-next-line no-var
  var loadingTimer: NodeJS.Timeout | undefined;
  // eslint-disable-next-line no-var
  var hasLoading: boolean | undefined;

  // React Native ErrorUtils
  // eslint-disable-next-line no-var
  var ErrorUtils: {
    setGlobalHandler: (
      handler: (error: Error, isFatal?: boolean) => void,
    ) => void;
    getGlobalHandler: () =>
      | ((error: Error, isFatal?: boolean) => void)
      | undefined;
  };

  // Development flag
  // eslint-disable-next-line no-var
  var __DEV__: boolean;
}

export {};
