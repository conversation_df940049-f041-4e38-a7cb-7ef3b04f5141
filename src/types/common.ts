/**
 * 通用类型定义
 */
import {RootStore} from '../mobx/rootStore';

/**
 * 基础实体接口
 */
export interface BaseEntity {
  /** 唯一标识符 */
  id: string;
  /** 创建时间 */
  createdAt: number;
  /** 更新时间 */
  updatedAt: number;
  /** 创建者 */
  createdBy?: string;
  /** 更新者 */
  updatedBy?: string;
}

/**
 * 可选字段类型
 */
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * 必需字段类型
 */
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

/**
 * 深度可选类型
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

/**
 * 键值对类型
 */
export type KeyValuePair<T = unknown> = Record<string, T>;

/**
 * 字符串映射类型
 */
export type StringMap = Record<string, string>;

/**
 * 数字映射类型
 */
export type NumberMap = Record<string, number>;

/**
 * 布尔映射类型
 */
export type BooleanMap = Record<string, boolean>;

/**
 * 基础数据类型联合
 */
export type PrimitiveValue = string | number | boolean;

/**
 * 混合值映射类型
 */
export type MixedValueMap = Record<string, PrimitiveValue>;

/**
 * 坐标点接口
 */
export interface Point {
  /** X坐标 */
  x: number;
  /** Y坐标 */
  y: number;
}

/**
 * 尺寸接口
 */
export interface Size {
  /** 宽度 */
  width: number;
  /** 高度 */
  height: number;
}

/**
 * 矩形区域接口
 */
export interface Rectangle extends Point, Size {}

/**
 * 时间范围接口
 */
export interface TimeRange {
  /** 开始时间 */
  startTime: number;
  /** 结束时间 */
  endTime: number;
}

/**
 * 日期范围接口
 */
export interface DateRange {
  /** 开始日期 */
  startDate: string;
  /** 结束日期 */
  endDate: string;
}

/**
 * 排序配置接口
 */
export interface SortConfig {
  /** 排序字段 */
  field: string;
  /** 排序方向 */
  direction: 'asc' | 'desc';
}

/**
 * 过滤条件接口
 */
export interface FilterCondition {
  /** 字段名 */
  field: string;
  /** 操作符 */
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'like';
  /** 值 */
  value: unknown;
}

/**
 * 查询配置接口
 */
export interface QueryConfig {
  /** 过滤条件 */
  filters?: FilterCondition[];
  /** 排序配置 */
  sort?: SortConfig[];
  /** 分页配置 */
  pagination?: {
    page: number;
    pageSize: number;
  };
}

/**
 * 操作结果接口
 */
export interface OperationResult<T = unknown> {
  /** 是否成功 */
  success: boolean;
  /** 结果数据 */
  data?: T;
  /** 错误信息 */
  error?: string;
  /** 错误代码 */
  errorCode?: string;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 加载状态枚举
 */
export enum LoadingStatus {
  /** 空闲 */
  IDLE = 'idle',
  /** 加载中 */
  LOADING = 'loading',
  /** 成功 */
  SUCCESS = 'success',
  /** 失败 */
  ERROR = 'error',
}

/**
 * 异步状态接口
 */
export interface AsyncState<T = unknown> {
  /** 数据 */
  data?: T;
  /** 加载状态 */
  status: LoadingStatus;
  /** 错误信息 */
  error?: string;
  /** 最后更新时间 */
  lastUpdated?: number;
}

/**
 * 事件接口
 */
export interface Event<T = unknown> {
  /** 事件类型 */
  type: string;
  /** 事件数据 */
  data: T;
  /** 时间戳 */
  timestamp: number;
  /** 事件源 */
  source?: string;
}

/**
 * 回调函数类型
 */
export type Callback<T = void> = (data: T) => void;

/**
 * 异步回调函数类型
 */
export type AsyncCallback<T = void> = (data: T) => Promise<void>;

/**
 * 错误处理函数类型
 */
export type ErrorHandler = (error: Error) => void;

/**
 * 验证函数类型
 */
export type Validator<T> = (value: T) => boolean | string;

/**
 * 转换函数类型
 */
export type Transformer<T, U> = (input: T) => U;

/**
 *
 * @description:store注入
 * @param {RootStore} rootStore
 * @param {*} navigation
 * @return {*}
 */
export interface InjectStore {
  rootStore: RootStore;
  navigation: {[key: string]: Function};
}

