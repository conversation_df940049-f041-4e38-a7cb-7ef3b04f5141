/**
 * 蓝牙相关类型定义
 */

/**
 * 蓝牙数据项接口
 */
export interface BluetoothDataItem {
  /** 数据点ID */
  dp_id: string;
  /** 数据点数据 */
  dp_data?: string | string[];
  /** 其他属性 */
  [key: string]: string | string[] | undefined;
}

/**
 * 蓝牙电池数据项接口
 */
export interface BluetoothBatteryDataItem extends BluetoothDataItem {
  /** 数据点数据 */
  dp_data: string | string[];
}

/**
 * 蓝牙连接状态枚举
 */
export enum BluetoothConnectionStatus {
  /** 未连接 */
  DISCONNECTED = 'disconnected',
  /** 连接中 */
  CONNECTING = 'connecting',
  /** 已连接 */
  CONNECTED = 'connected',
  /** 连接失败 */
  FAILED = 'failed',
}

/**
 * 蓝牙设备信息
 */
export interface BluetoothDeviceInfo {
  /** 设备ID */
  deviceId: string;
  /** 设备名称 */
  name: string;
  /** MAC地址 */
  address: string;
  /** 信号强度 */
  rssi: number;
  /** 是否已配对 */
  isPaired: boolean;
  /** 连接状态 */
  connectionStatus: BluetoothConnectionStatus;
}

/**
 * 蓝牙扫描结果
 */
export interface BluetoothScanResult {
  /** 发现的设备列表 */
  devices: BluetoothDeviceInfo[];
  /** 扫描是否完成 */
  isComplete: boolean;
  /** 扫描错误 */
  error?: string;
}

/**
 * 蓝牙数据传输结果
 */
export interface BluetoothTransferResult {
  /** 是否成功 */
  success: boolean;
  /** 传输的数据 */
  data?: unknown;
  /** 错误信息 */
  error?: string;
  /** 传输时间戳 */
  timestamp: number;
}

/**
 * 蓝牙命令接口
 */
export interface BluetoothCommand {
  /** 命令ID */
  commandId: string;
  /** 命令类型 */
  type: string;
  /** 命令参数 */
  params?: Record<string, unknown>;
  /** 超时时间 */
  timeout?: number;
}

/**
 * 蓝牙命令响应
 */
export interface BluetoothCommandResponse {
  /** 命令ID */
  commandId: string;
  /** 响应状态 */
  status: 'success' | 'error' | 'timeout';
  /** 响应数据 */
  data?: unknown;
  /** 错误信息 */
  error?: string;
  /** 响应时间戳 */
  timestamp: number;
}

/**
 * 蓝牙数据解析常量
 */
export const BLUETOOTH_CONSTANTS = {
  /** WiFi命令起始索引 */
  WIFI_CMD_START_INDEX: 12,
  /** WiFi命令结束偏移 */
  WIFI_CMD_END_OFFSET: -4,
  /** WiFi数据长度 */
  WIFI_DATA_LENGTH: 64,
  /** 电池数据长度 */
  BATTERY_DATA_LENGTH: 16,
  /** 电池数据头部长度 */
  BATTERY_DATA_HEADER_LENGTH: 4,
  /** 十六进制分组大小 */
  HEX_GROUP_SIZE: 2,
  /** 十六进制基数 */
  HEX_RADIX: 16,
} as const;
