import commonWords from './common.js';
import {prefix} from '../config.js';

export default {
  en: {
    ...commonWords.en,

    // pannel/home page
    // home: {
    [`${prefix}home_wifiConnected_textview_text`]: 'Connected',
    [`${prefix}home_wifiNotConnected_textview_text`]: 'Disconnected',
    [`${prefix}home_otaAlertMsg_textview_text`]:
      'If you do not update the device, it  will not work properly. Please update now',
    [`${prefix}home_finishIn_textview_text`]: 'Finishing in',
    // offline
    [`${prefix}home_offlineSingleDayTitle_textview_text`]:
      '{{deviceName}} device has been offline for {{day}} day',
    [`${prefix}home_offlineDaysTitle_textview_text`]:
      '{{deviceName}} device has been offline for {{days}} days',
    [`${prefix}home_offlineDesc_textview_text`]:
      'Please power on the device, or power off the device and then power it back on',
    [`${prefix}home_offlineCancel_button_text`]: 'Cancel',
    [`${prefix}home_offlineConfirm_button_text`]: 'More solutions',
    // },

    // topStatusView in home page
    // topStatusView: {
    [`${prefix}home_remainingBattery_textview_text`]: 'Remaining Battery',
    [`${prefix}home_batteryLeft_textview_text`]: 'Remaining Energy',
    [`${prefix}home_totalWorkingTime_textview_text`]: 'Total Working Time',
    // },

    // ChargingOverview in home page
    // ChargingOverview: {
    [`${prefix}home_chargingOverview_title_textview_text`]: 'Charging Overview',
    [`${prefix}home_batteryCharging_textview_text`]: 'Charging',
    [`${prefix}home_batteryReady_textview_text`]: 'Ready',
    [`${prefix}home_batteryStandby_textview_text`]: 'Standby',
    [`${prefix}home_batteryEmpty_textview_text`]: 'Empty',
    // },

    // updateItem in home page
    // updateItem: {
    [`${prefix}home_upgradeAvailable_textview_text`]: 'Update Available',
    // },

    // settingView in home page
    // settingView: {
    [`${prefix}home_functionSetting_textview_text`]: 'Function Setting',
    [`${prefix}home_scheduleChargingTitle_textview_text`]: 'Schedule Charging',
    [`${prefix}home_scheduleChargingStartsFrom_textview_text`]: 'Starts from',
    [`${prefix}home_completionReminder_textview_text`]: 'Completion Reminder',
    [`${prefix}home_completionReminderDesc_textview_text`]:
      'Notify me when below number of batteries are ready.',
    [`${prefix}home_dcPriorityCharging_textview_text`]:
      'PGX Power Bank Priority Charging',
    [`${prefix}home_dcPriorityChargingDesc_textview_text`]:
      'Start charging from the first PGX Power Bank.',
    [`${prefix}home_optimizedBatteryCharging_textview_text`]:
      'Battery Optimization',
    [`${prefix}home_optimizedBatteryChargingDesc_textview_text`]:
      'Set the maximum charging percentage to 90% to reduce battery aging.',
    [`${prefix}home_timePickerStartTime_textview_text`]: 'Start time',
    [`${prefix}home_timeToastTip_textview_text`]:
      'The starting time must be set after current time.',
    [`${prefix}home_errorcode_textview_text`]: 'Error Code',
    [`${prefix}home_suggestion_textview_text`]: 'Suggestion',
    // }

    // usageStatistics: {
    [`${prefix}statistics_title_textview_text`]: 'Usage Statistics',
    [`${prefix}statistics_chargingTime_textview_text`]: 'Total Charging Time',
    // },
  },
};
