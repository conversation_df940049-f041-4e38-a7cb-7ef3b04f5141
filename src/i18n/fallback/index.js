import commonWords from './common.js';
import {prefix} from '../config.js';

export default {
  en: {
    ...commonWords.en,

    // pannel/home page
    // home: {
    [`${prefix}home_connected_textview_text`]: 'Connected',
    [`${prefix}home_connecting_textview_text`]: 'Connecting',
    [`${prefix}home_notConnected_textview_text`]: 'Disconnected',
    [`${prefix}home_otaAlertMsg_textview_text`]:
      'If you do not update the device, it will not work properly. Please update now',
    [`${prefix}home_usageHistory_textview_text`]:
      'Battery Discharging Activity',
    // },

    // topStatusView in home page
    // topStatusView: {
    [`${prefix}home_remainingBattery_textview_text`]: 'Remaining Battery',
    [`${prefix}home_batteryLeft_textview_text`]: 'Remaining Energy',
    [`${prefix}home_dischargingTime_textview_text`]: 'Total Discharging Time',
    [`${prefix}home_finishIn_textview_text`]: 'Finish in',
    [`${prefix}home_fullyChargedBaterryStatus_textview_text`]: 'Fully Charged',

    // [`${prefix}home_normalBatteryHealth_textview_text`]: 'normal',
    // [`${prefix}home_abnormalBatteryHealth_textview_text`]: 'abnormal',
    [`${prefix}home_totalWorkingTime_textview_text`]: 'Total discharging time',
    [`${prefix}home_usageHistoryNaTip_textview_text`]: `1. 'Usage History' data is stored on the tool; please connect the tool to this app to synchronize and view updated usage history data.
      
2. 'Total Time' values reflect usage starting from the first time the tool is used, while monthly usage data as shown on the graph only shows data starting from the first time the tool is connected to this app.`,
    [`${prefix}home_usageHistoryEuTip_textview_text`]: `1. 'Usage History' data is stored on the tool; please connect the tool to this app to synchronise and view updated usage history data.

2. 'Total Time' values reflect usage starting from the first time the tool is used, while monthly usage data as shown on the graph only shows data starting from the first time the tool is connected to this app.`,
    [`${prefix}home_usageHistoryTipConfirm_textview_text`]: 'OK',
    // },

    // controlArea in home page
    // controlArea: {
    [`${prefix}home_firmwareVersion_textview_text`]: 'Firmware Version',
    [`${prefix}home_upgradeAvailable_textview_text`]: 'Update Available',
    [`${prefix}home_upToDate_textview_text`]: 'Up to Date',
    [`${prefix}home_otaVersion_textview_text`]: 'Ver',
    [`${prefix}home_registrationInfo_textview_text`]: 'Registration Info',
    [`${prefix}home_registerNow_textview_text`]: 'Register Now',
    [`${prefix}home_accessoryTitle_textview_text`]: 'Accessories',
    [`${prefix}home_accessoryAvailable_textview_text`]: 'available',
    [`${prefix}home_manual_textview_text`]: 'User Manual',
    [`${prefix}home_moreCard_textview_text`]: 'More to come...',
    [`${prefix}home_errorcode_textview_text`]: 'Error Code',
    [`${prefix}home_suggestion_textview_text`]: 'Suggestion',
    // },

    // usageStatistics: {
    [`${prefix}statistics_title_textview_text`]: 'Usage Statistics',
    [`${prefix}statistics_usageInfomation_textview_text`]: 'Usage Information',
    [`${prefix}statistics_chargingTime_textview_text`]: 'Total Charging Time',
    [`${prefix}statistics_disChargingTime_textview_text`]:
      'Total Discharging Time',
    [`${prefix}statistics_healthInformation_textview_text`]:
      'Health Information',
    [`${prefix}statistics_batteryHealthCondition_textview_text`]: 'Condition',
    [`${prefix}statistics_batteryCycleCount_textview_text`]: 'Cycle Count',
    [`${prefix}statistics_batteryHealthNormal_textview_text`]: 'Normal',
    [`${prefix}statistics_batteryHealthAbnormal_textview_text`]:
      'Service Recommended',
    // }
  },
};
