import commonWords from './common.js';
import {prefix} from '../config.js';

export default {
  en: {
    ...commonWords.en,

    // pannel/home page
    // home: {
    [`${prefix}home_connected_textview_text`]: 'Connected',
    [`${prefix}home_connecting_textview_text`]: 'Connecting',
    [`${prefix}home_notConnected_textview_text`]: 'Disconnected',
    [`${prefix}home_otaAlertMsg_textview_text`]:
      'If you do not update the device, it will not work properly. Please update now',
    [`${prefix}home_remainingBattery_textview_text`]: 'Remaining Battery',
    [`${prefix}home_batteryLeft_textview_text`]: 'Remaining Energy',
    [`${prefix}home_totalWorkingTime_textview_text`]: 'Total Working Time',
    [`${prefix}home_finishIn_textview_text`]: 'Finishing in ',
    // },

    // centerInfoView: {
    [`${prefix}home_enableDcCharging_textview_text`]: 'Enable DC-DC Charging',
    [`${prefix}home_lfpBattery_textview_text`]: 'High Capacity Battery',
    [`${prefix}home_portableBattery_textview_text`]: 'Portable Battery',
    [`${prefix}home_priorityCharging_textview_text`]: 'Priority Charging',
    [`${prefix}home_lfpRecharging_textview_text`]: 'Recharging',
    [`${prefix}home_dcInCharging_textview_text`]: 'Charging',
    [`${prefix}home_dcInStandby_textview_text`]: 'Standby',
    [`${prefix}home_dcInReady_textview_text`]: 'Ready',
    [`${prefix}home_dcInFault_textview_text`]: 'Fault',
    [`${prefix}home_emptyBattery_textview_text`]: 'Empty',
    [`${prefix}home_chargingBattery_textview_text`]: 'Charging',
    [`${prefix}home_dischargingBattery_textview_text`]: 'Discharging',
    [`${prefix}home_readyBattery_textview_text`]: 'Ready',
    [`${prefix}home_overheatBattery_textview_text`]: 'Overheat',
    [`${prefix}home_standbyBattery_textview_text`]: 'Standby',
    [`${prefix}home_lowPowerBattery_textview_text`]: 'Low Power',
    [`${prefix}home_errorBattery_textview_text`]: 'Error',
    [`${prefix}home_lowBatteryReminder_textview_text`]:
      'Insufficient battery to enable DC-DC charging',
    [`${prefix}home_connectionDesc_textview_text`]:
      'Firmware update complete. Please turn on the device for connection.',
    [`${prefix}home_connectionOk_button_text`]: 'OK',

    // updateItem in home page
    // updateItem: {
    [`${prefix}home_upgradeAvailable_textview_text`]: 'Update Available',
    // },

    // functionSetting: {
    [`${prefix}home_functionSettingTitle_textview_text`]: 'Function Setting',
    [`${prefix}home_customizeSequenceTitle_textview_text`]:
      'Customize Sequence',
    [`${prefix}home_customizeSequenceDesc_textview_text`]:
      'Set which type of battery to charge first during charging. By default all batteries will charge simultaneously',
    [`${prefix}home_batteryOptimizationTitle_textview_text`]:
      'Battery Optimization',
    [`${prefix}home_batteryOptimizationDesc_textview_text`]:
      'Set the maximum charging percentage to 90% to reduce battery aging',
    // },
    [`${prefix}home_errorcode_textview_text`]: 'Error Code',
    [`${prefix}home_suggestion_textview_text`]: 'Suggestion',
    // },

    // usageStatistics: {
    [`${prefix}statistics_title_textview_text`]: 'Usage Statistics',
    [`${prefix}statistics_acChargingTime_textview_text`]:
      'Total AC Charging Time',
    [`${prefix}statistics_dcChargingTime_textview_text`]:
      'Total DC Charging Time',
    [`${prefix}statistics_chargingEnergy_textview_text`]:
      'Total Battery Charging Energy',
    [`${prefix}statistics_disChargingEnergy_textview_text`]:
      'Total Battery Discharging Energy',
    // }
  },
};
