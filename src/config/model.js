import {numToHexString} from '@utils/tools.js';

/**
 * @see http://wiki.chervon.com.cn/pages/viewpage.action?pageId=11420223
 */
export const map = {
  // property
  working_status: '2017', // 工作状态，0 => 保留, 1 => AC模式充电中， 2 => DC模式放电中， 3 => 待机， 4 => 故障
  ac_charging_progress: '2022', // AC模式下充电进度，0-100，unit: %
  dc_charging_progress: '2048', // DC模式下充电进度，0-100，unit: %
  ac_charging_remaining_time: '2021', // AC充电充满剩余时间，unit: s

  remaining_battery_of_batteries: '2024', // 各电池包剩余电量 unit: mAh, 大包（4Bytes）+ 小包1（4Bytes）+ 小包2（4Bytes）
  rated_capacity_of_batteries: '2040', // 各电池包额定容量 unit: mAh, 大包（4Bytes）+ 小包1（4Bytes）+ 小包2（4Bytes）

  dc_charging_remaining_time_of_batteries: '2025', // 各电池包充满剩余时间 unit: s, 小包1（4Bytes）+ 小包2（4Bytes）

  total_ac_charging_time: '2023', // AC模式累计充电时长 unit: s
  total_dc_charging_time: '2026', // DC模式累计充电时长 unit: s

  total_charging_energy: '2027', // 累计充电能量 unit: Wh
  total_discharging_energy: '2035', // 累计放电能量 unit: Wh
  // past_week_charging_energy_list: '2036', // 过去一周（7天）充电能量 unit: kWh * 7
  // past_year_charging_energy_list: '2037', // 过去一年（12个月）充电能量 unit: kWh * 12
  // past_week_discharging_energy_list: '2038', // 过去一周（7天）放电能量 unit: kWh * 7
  // past_year_discharging_energy_list: '2039', // 过去一年（12个月）放电能量 unit: kWh * 12

  // lfp大包和小包仓位状态信息，可以查询到仓位的电池包状态
  lfp_battery_status: '2018', // 仓位状态（1字节），0-空仓 1-充电中 2-放电中 3-充电满 4-过温等待 5-空闲等待 6-电池包低电量（低于10%） 7-故障
  first_portable_battery_status: '2019', // 小包仓位1状态, 仓位状态（1字节）+ 电池deviceID（18字节,固定长度，若长度不足18字节，不足部分补0）,0-空仓 1-充电中 3-充电满 4-过温等待 5-空闲等待 7-故障
  second_portable_battery_status: '2020', // 小包仓位2状态, 同上

  // service
  // temporary no service avaliable
  dc_dc_mode_switch: '21010', // DC模式开关，0 => 关， 1 => 开
  customize_charging_sequence: '21011', // 自定义充电顺序，0 => 关， 1 => LFP大包优先， 2 => 小包优先
  battery_optimization_switch: '21004', // 电池优化开关，0 => 关，1 => 开

  // event
  fault_message: '41003',
};

// 故障清除指令, 每个单品都不一样
/**
 * @see http://wiki.chervon.com.cn/pages/viewpage.action?pageId=22819347
 */
export const NO_ERROR_CODE = 300000;

export const dpTypeMap = {
  rawKey: '00',
  boolKey: '01',
  valueKey: '02', // int
  stringKey: '03',
  enumKey: '04',
  paramKey: '05',
};

/**
 * 根据物模型定义来确定 type
 * @param {String} dpId
 * @returns {String}
 */
export const getParamType = dpId => {
  let result = dpTypeMap.enumKey;
  switch (dpId) {
    case map.customize_charging_sequence:
      result = dpTypeMap.enumKey;
      break;
    case map.dc_dc_mode_switch:
    case map.battery_optimization_switch:
      result = dpTypeMap.boolKey;
      break;
    default:
      break;
  }
  return result;
};

export const dp_len_1 = numToHexString(1, 4).toUpperCase();
export const dp_len_2 = numToHexString(2, 4).toUpperCase();

export default map;
