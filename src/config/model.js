import {numToHexString} from '@utils/tools.js';

/**
 * @see http://wiki.chervon.com.cn/pages/viewpage.action?pageId=11424071
 */
export const map = {
  // property
  remaining_battery: '1011', // 电量百分比，unit: %
  /**
   * LFP大包设备状态，
   * 0 => 保留
   * 1 => 充电中
   * 2 => 放电中
   * 3 => 充满电
   * 4 => 充电失败
   * 5 => 等待中
   * 6 => 过温等待
   * 7 => 故障
   */
  battery_status: '2028',
  remaining_charging_time: '2032', // 充满剩余时间 unit: s

  battery_capacity: '2030', // 电池包额定容量，unit: Ah，一般固定为40Ah
  usage_history: '2029', // 按月累计使用时长, unit: s
  // ota_status: '1001', // 0 => 不可升级, 1 => 可升级, 暂时用不上

  total_charging_time: '2026', // 电池包充电状态的累计充电总时长，unit: s
  total_discharging_time: '2031', // 总放电时长，unit: s
  // total_working_time: '1016', // 总工作时长，包括充电时间+放电时间+空闲时间 unit: s

  battery_health_condition: '1013', // 电池健康状态，unit: %, 1-100
  battery_cycle_count: '2034', // 电池循环次数，unit: 次

  // service
  // temporary no service avaliable

  // event
  fault_message: '41002',
};

// 故障清除指令, 每个单品都不一样
export const NO_ERROR_CODE = 110000;

export const dpTypeWithDpid = dpId => {
  switch (dpId) {
    default:
      return dpTypeMap.rawKey;
  }
};

export const dpTypeMap = {
  rawKey: '00',
  boolKey: '01',
  valueKey: '02', // int
  stringKey: '03',
  enumKey: '04',
  paramKey: '05',
};

export const dp_len_1 = numToHexString(1, 4).toUpperCase();
export const dp_len_2 = numToHexString(2, 4).toUpperCase();

export default map;
