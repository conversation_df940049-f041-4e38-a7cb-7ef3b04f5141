import {Utils} from '@cvn/rn-panel-kit';
const {NumberUtils} = Utils;

/**
 * 物模型
 * @see http://wiki.chervon.com.cn/pages/viewpage.action?pageId=11420233
 */
export const map = {
  // property
  charging_progress: '2008', // unit: percentage 总充电进度 0-100
  charging_status: '2016', // 0 => 保留, 1 => 充电中, 2 => 充电完成, 3 => 等待计划充电（schedule), 4 => 充电器过温, 5 => 充电器故障, 6 => 充电器高温慢充， 7 => 节能模式
  remaining_charging_time: '2001', // unit: min 充电倒计时 0-99999

  ready_batterys_power: '2009', //  unit: Wh 已充满的电池包总电量 0-999999
  ready_batterys_count: '2010', //  已充满的电池包数量 0-99999
  charging_batterys_count: '2011', //  正在充电的电池包数量 0-99999
  standby_batterys_count: '2012', //  待充电的电池包数量 0-99999
  empty_batterys_count: '2013', //  空电池包数量 0-99999

  three_port_adaptors_count: '2014', // 3头适配器数量 0-99999
  dc_dc_chargers_count: '2015', // dc-dc 数量 0-99999
  // property for statistic
  total_charging_time: '2004', // unit: h 总充电时间 0-99999

  // service
  schedule_charging_switch: '21001', // boolean 充电预约开关，开启后可预约充电，页面底部展开预约充电时间对话框，设备将在设定时间对电池包进行充电
  completion_reminder_switch: '21006', // boolean 充电完成提醒开关，开启后，充完指定电池包数量后会有提醒
  dc_dc_priority_charging_switch: '21009', // boolean dc-dc 优先开关，开启后优先使用 dc-dc 充电
  optimized_battery_charging_switch: '21004', // boolean 电池优化充电开关，开启后，充电上限默认 80%

  schedule_charging_start_time: '21002', // int 充电预约开始时间，格式为时间戳，10:00 以半小时为单位
  completion_reminder_value: '21008', // int 充电完成提醒电池包数量，0-99999

  // event
  error_status_code: '41001',
  // full_battery_status: '41004', // boolean 充满电池包数量达到指定的值
};

/**
 * 故障清除指令, 每个单品都不一样
 * @see http://wiki.chervon.com.cn/display/RD/PGX+Power+Hub
 */
export const NO_ERROR_CODE = 110000;

// 定义的物模型 ID
export const dpIds = Object.values(map);

// 只通过此物模型下发控制，不读取
export const issueOnlyMap = {
  schedule_charging_now_time: '20001', // int 充电预约当前时间，格式为时间戳
};

export const dpTypeMap = {
  rawKey: '00',
  boolKey: '01',
  valueKey: '02', // int
  stringKey: '03',
  enumKey: '04',
  paramKey: '05',
};

export const dp_len_1 = NumberUtils.numToHexString(1, 4).toUpperCase();
export const dp_len_2 = NumberUtils.numToHexString(2, 4).toUpperCase();

/**
 * 根据物模型定义来确定 type
 * @param {String} dpId
 * @returns {String}
 */
export const getParamType = dpId => {
  let result = dpTypeMap.enumKey;
  switch (dpId) {
    case map.schedule_charging_switch:
    case map.optimized_battery_charging_switch:
    case map.completion_reminder_switch:
      result = dpTypeMap.boolKey;
      break;
    case map.charging_schedule_start_time:
    case map.completion_reminder_value:
      result = dpTypeMap.valueKey;
      break;
    default:
      break;
  }
  return result;
};

export default map;
