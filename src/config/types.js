/**
 * ota 升级结果
 * @typedef OtaResult
 * @property {boolean} showRed 是否显示小红点
 * @property {boolean} isForceUpdate 是否强制升级
 * @property {string} customVersion  升级版本号
 * @property {string} deviceId 设备ID
 */

/**
 * @typedef {Object} PayloadData
 * @property {string} productId
 * @property {string} messageType
 * @property {string} createTime
 * @property {"info"|"warning"|"error"|undefined} eventType
 * @property {"true"|"false"|undefined} value
 * @property {string} deviceId
 * @property {string} uuid
 */

/**
 * @typedef {Object} MessageData
 * @property {number} messageType
 * @property {number[]} pushTypes
 * @property {string} title
 * @property {string} uuid
 * @property {string} content
 * @property {PayloadData} payloadData
 */

/**
 * @typedef {Object} TopicStateData
 * @property {Object} delta
 * @property {Object} desired
 * @property {Object} reported
 */

/**
 * @typedef {Object} TopicData
 * @property {Object} metadata
 * @property {TopicStateData} state
 * @property {number} timestamp
 * @property {number} version
 */

/**
 * @typedef {Object} DeviceUnreadMessageResponseData
 * @property {boolean} entry
 * @property {string} message
 * @property {string} requestId
 * @property {string} responseCode
 * @property {boolean} status
 */

/**
 * @typedef {Object} DeviceDetailResponseData
 * @property {DeviceDetailData} entry
 * @property {string} message
 * @property {string} requestId
 * @property {string} responseCode
 * @property {boolean} status
 */

/**
 * @typedef {Object} DeviceDetailData
 * @property {string} accessoryQuantity 附件数量
 * @property {[string, string]} assemblySnList
 * @property {string} commodityModel
 * @property {string} communicateMode
 * @property {number} deviceCodeStatus
 * @property {string} deviceIcon
 * @property {string} deviceId 设备型号
 * @property {string} deviceName 设备名称
 * @property {number} infoStatus
 * @property {0|1} isOnline 0 for offline, 1 for online
 * @property {string} offlineDays 设备离线天数
 * @property {string} mac
 * @property {string} nickName 设备昵称
 * @property {string} period
 * @property {string} productId 产品型号
 * @property {string} productType
 * @property {string} rnBundleName 绑定的 rn bundle 名称
 * @property {string} sn snCode
 * @property {number} sort
 * @property {number} status 状态
 * @property {string} userIdList
 * @property {string} version
 */

/**
 * @typedef {Object} InitialParamsData
 * @property {'dayMonthYear'|'monthDayYear'} appSettingOfDate
 * @property {'12hours'|'24hours'} appSettingOfHour
 * @property {'metric'|'imperial'} appSettingOfUnit
 * @property {string} deviceId
 * @property {string} deviceName
 * @property {string} lang
 * @property {string} mac
 * @property {string} productId
 * @property {'NA'|'EU'} region
 * @property {string} userId
 * @property {DeviceDetailData} deviceDetail
 * @property {'SIT'|'PRE'|'PRD'} env
 */

/**
 * @type {OtaResult}
 */
export let OtaResult;

/**
 * @type {PayloadData}
 */
export let PayloadData;

/**
 * @type {MessageData}
 */
export let MessageData;

/**
 * @type {TopicData}
 */
export let TopicData;

/**
 * @type {DeviceDetailData}
 */
export let DeviceDetailData;
