import React, {useMemo, useState} from 'react';
import {View, StyleSheet} from 'react-native';
import {useTranslation} from 'react-i18next';
import {geti18nText} from '@i18n/config';
import {LargerSwitch} from '@components/Switch';
import LineChart from '@components/LineChart';
import dayjs from 'dayjs';
import CustomText from 'IOTRN/src/components/CustomText';

const isValidValue = value => value >= 0;
const dataFormat = (item, unit = '') => {
  return isValidValue(item) ? `${item}${unit}` : '--';
};

const ChargingEnergyStatisticsCard = ({
  totalChargingEnergy,
  totalDischargingEnergy,
  pastWeekChargingEnergyList,
  pastYearChargingEnergyList,
  pastWeekDischargingEnergyList,
  pastYearDischargingEnergyList,
}) => {
  const {t} = useTranslation('all');

  const [currentChargingIndex, setCurrentChargingIndex] = useState(0);
  const [currentDischargingIndex, setCurrentDischargingIndex] = useState(0);

  const labelSet = [
    Array.from({length: 7}, (_, i) =>
      dayjs()
        .subtract(6 - i, 'day')
        .format('ddd'),
    ),
    Array.from({length: 12}, (_, i) =>
      dayjs()
        .subtract(11 - i, 'month')
        .format('MMM'),
    ),
  ];
  const chargingEnergySet = [
    pastWeekChargingEnergyList,
    pastYearChargingEnergyList,
  ];
  const dischargingEnergySet = [
    pastWeekDischargingEnergyList,
    pastYearDischargingEnergyList,
  ];

  const chargingLabels = useMemo(() => {
    return labelSet[currentChargingIndex];
  }, [currentChargingIndex]);

  const chargingEnergyList = useMemo(() => {
    return chargingEnergySet[currentChargingIndex];
  }, [currentChargingIndex]);

  const dischargingLabels = useMemo(() => {
    return labelSet[currentDischargingIndex];
  }, [currentDischargingIndex]);

  const dischargingEnergyList = useMemo(() => {
    return dischargingEnergySet[currentDischargingIndex];
  }, [currentDischargingIndex]);

  const datacardList = useMemo(
    () => [
      {
        key: 'charging',
        title: t(geti18nText('statistics_chargingEnergy_textview_text')),
        value: totalChargingEnergy,
        unit: 'kWh',
        switchIndex: currentChargingIndex,
        onSwitchIndexChange: setCurrentChargingIndex,
        labels: chargingLabels,
        chartList: chargingEnergyList,
      },
      {
        key: 'discharging',
        title: t(geti18nText('statistics_disChargingEnergy_textview_text')),
        value: totalDischargingEnergy,
        unit: 'kWh',
        switchIndex: currentDischargingIndex,
        onSwitchIndexChange: setCurrentDischargingIndex,
        labels: dischargingLabels,
        chartList: dischargingEnergyList,
      },
    ],
    [
      currentChargingIndex,
      currentDischargingIndex,
      chargingLabels,
      chargingEnergyList,
      dischargingLabels,
      dischargingEnergyList,
      totalChargingEnergy,
      totalDischargingEnergy,
    ],
  );

  return (
    <>
      {datacardList.map(item => (
        <View style={styles.statisticContainer} key={item.key}>
          <View style={styles.textContainer}>
            <CustomText style={styles.textTitle}>{item.title}</CustomText>
            <CustomText style={styles.textValue}>
              {dataFormat(item.value, item.unit)}
            </CustomText>
          </View>

          <LargerSwitch
            activeBackgroundColor="#FFFFFF"
            choices={['Past 7 Days', 'Past 12 Month']}
            value={item.switchIndex}
            style={{
              borderRadius: 10,
              marginBottom: 30,
            }}
            containerHeight={36}
            boxStyle={{
              borderRadius: 8,
            }}
            boxWidth={153}
            boxHeight={30}
            onChange={index => {
              item.onSwitchIndexChange(index);
            }}
          />

          <LineChart
            list={item.chartList}
            labels={item.labels}
            style={{marginBottom: 5}}
            unit="kWh"
          />
        </View>
      ))}
    </>
  );
};

const styles = StyleSheet.create({
  statisticContainer: {
    marginTop: 13,
    marginHorizontal: 15,
    paddingHorizontal: 15,
    paddingTop: 20,
    paddingBottom: 15,
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    overflow: 'hidden',
  },
  textContainer: {
    marginBottom: 29,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  textTitle: {
    fontSize: 15,
    lineHeight: 18,
    fontWeight: '400',
  },
  textValue: {
    fontSize: 15,
    lineHeight: 18,
    fontWeight: '400',
  },
});

export default ChargingEnergyStatisticsCard;
