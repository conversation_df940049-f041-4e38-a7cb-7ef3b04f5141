import React from 'react';
import {View, StyleSheet, ScrollView} from 'react-native';
import {inject, observer} from 'mobx-react/native';
import PageView from '@components/PageView.js';
import PageBase from '@base/index.js';
import {Cell, HeaderView, WhiteSpace} from '@components';
// import ChargingEnergyStatisticsCard from './ChargingEnergyStatisticsCard';
import {withTranslation, useTranslation} from 'react-i18next';
import {geti18nText} from '@i18n/config';
import {isIphoneX} from '@utils/device';
import CustomText from 'IOTRN/src/components/CustomText';

@inject('panel')
@observer
class Statistics extends PageBase {
  componentDidMount() {}

  render() {
    const {t} = this.props;
    const {
      totalAcChargingTime,
      totalDcChargingTime,
      totalChargingEnergy,
      totalDischargingEnergy,

      // pastWeekChargingEnergyList,
      // pastWeekDischargingEnergyList,
      // pastYearChargingEnergyList,
      // pastYearDischargingEnergyList,
    } = this.props.panel.usageStatistics;

    return (
      <PageView>
        <HeaderView
          title={t(geti18nText('statistics_title_textview_text'))}
          onLeftPress={() => {
            this.goBack();
          }}
        />
        <ScrollView>
          <ChargingDataListCard
            totalAcChargingTime={totalAcChargingTime}
            totalDcChargingTime={totalDcChargingTime}
            totalChargingEnergy={totalChargingEnergy}
            totalDischargingEnergy={totalDischargingEnergy}
          />

          {/* <ChargingEnergyStatisticsCard
            totalChargingEnergy={totalChargingEnergy}
            totalDischargingEnergy={totalDischargingEnergy}
            pastWeekChargingEnergyList={pastWeekChargingEnergyList}
            pastWeekDischargingEnergyList={pastWeekDischargingEnergyList}
            pastYearChargingEnergyList={pastYearChargingEnergyList}
            pastYearDischargingEnergyList={pastYearDischargingEnergyList}
          /> */}
        </ScrollView>

        <WhiteSpace size={isIphoneX ? 34 : 0} />
      </PageView>
    );
  }
}

const isValidValue = value => value >= 0;
const dataFormat = (item, unit = '') => {
  return isValidValue(item) ? `${item}${unit}` : '--';
};

const ChargingDataListCard = ({
  totalAcChargingTime,
  totalDcChargingTime,
  totalChargingEnergy,
  totalDischargingEnergy,
}) => {
  const {t} = useTranslation('all');
  const list = [
    {
      key: 'chargingTime',
      title: t(geti18nText('statistics_acChargingTime_textview_text')),
      rightElement: (
        <View style={styles.rightBox}>
          <CustomText
            numberOfLines={1}
            ellipsizeMode="tail"
            style={styles.extraText}>
            {dataFormat(totalAcChargingTime, 'hr')}
          </CustomText>
        </View>
      ),
    },
    {
      key: 'dischargingTime',
      title: t(geti18nText('statistics_dcChargingTime_textview_text')),
      rightElement: (
        <View style={styles.rightBox}>
          <CustomText
            numberOfLines={1}
            ellipsizeMode="tail"
            style={styles.extraText}>
            {dataFormat(totalDcChargingTime, 'hr')}
          </CustomText>
        </View>
      ),
    },
    {
      key: 'chargingEnergy',
      title: t(geti18nText('statistics_chargingEnergy_textview_text')),
      rightElement: (
        <View style={styles.rightBox}>
          <CustomText
            numberOfLines={1}
            ellipsizeMode="tail"
            style={styles.extraText}>
            {dataFormat(totalChargingEnergy, 'kWh')}
          </CustomText>
        </View>
      ),
    },
    {
      key: 'dischargingEnergy',
      title: t(geti18nText('statistics_disChargingEnergy_textview_text')),
      rightElement: (
        <View style={styles.rightBox}>
          <CustomText
            numberOfLines={1}
            ellipsizeMode="tail"
            style={styles.extraText}>
            {dataFormat(totalDischargingEnergy, 'kWh')}
          </CustomText>
        </View>
      ),
    },
  ];
  const size = list.length;
  return (
    <View style={styles.container}>
      {list.map((item, index) => (
        <Cell
          key={item.key}
          title={item.title}
          iconShow={false}
          rightElement={item.rightElement}
          containerStyle={styles.containerStyle}
          hideRightArrow={true}
          showWholeUnderline={index + 1 < size}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 17,
    borderRadius: 10,
    marginHorizontal: 15,
    backgroundColor: '#fff',
  },
  rightBox: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  extraText: {
    color: '#666666',
    fontSize: 13,
    fontWeight: '400',
    lineHeight: 16,
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  containerStyle: {
    marginHorizontal: 17,
  },
});

export default withTranslation('all')(Statistics);
