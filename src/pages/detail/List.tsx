import React, {memo, useEffect, useState} from 'react';
import {Text, View, ScrollView, StyleSheet} from 'react-native';
import {inject, observer} from 'mobx-react';
import {useModal} from '@components/Modal';
import {
  mobile,
  CommonEmitter,
  Utils,
  WhiteSpace,
  PageView,
} from 'cvn-panel-kit';
import {HeaderView, Item} from '@components';
import {Button} from 'react-native-elements';
import {DEVICE_WIDTH, isIphoneX} from '@utils/device';
import Strings from '@i18n';
import {tracer, eleIdMap} from 'IOTRN/src/utils/tracer';
import {onHideDetailListAlert, onShowDetailListAlert} from './utils';
import {jumpToParts} from 'IOTRN/src/mobx/utils/help';
import {goBack, goPage} from 'IOTRN/src/utils/pageAction';
import {
  deviceResetPassword,
  deviceUnbind,
  postToFetchWhetherDeviceHasUnreadMessage,
} from 'IOTRN/src/api';
import {InjectStore} from 'IOTRN/src/types/common';

const {JumpUtils} = Utils;
const commonDeviceNameKey = 'rn_common_detaillist_devicename_textview_text';
const commonDeviceStatusKey = 'rn_61004_detaillist_devicestatus_textview_text';
const commonDeviceNotification =
  'rn_61004_detaillist_device_notification_textview_text';
const commonwificonfigureKey = 'rn_61004_detaillist_wificonfig_textview_text';
const commonRegistratonKey = 'rn_common_detaillist_registration_textview_text';
const commonUpgradeKey = 'rn_common_detaillist_upgrade_textview_text';
const commonDataStaticKey = 'rn_common_detaillist_statistics_textview_text';
const commonAboutKey = 'rn_common_detaillist_about_textview_text';
const changePasswordKey = 'rn_61004_detaillist_changepassword_textview_text';
const commonPartsKey = 'rn_common_detaillist_parts_textview_text';
const commonProductInfoKey = 'rn_common_detaillist_productintro_textview_text';
const commonShareDeviceKey = 'rn_61004_detaillist_share_device_textview_text';
const commonFeedBackKey = 'rn_common_detaillist_feedback_textview_text';

const List = inject('rootStore')(
  observer(({rootStore, navigation}: InjectStore) => {
    const [hasUnreadMessage, setHasUnreadMessage] = useState(false);
    const [baseData, setBaseData] = useState<
      {icon: string; title: string; key: string; regions: string[]}[]
    >([]);
    const {deviceId, productId} = rootStore.deviceStore.state.initialParams;
    useEffect(() => {
      const {canBeShared, region} = rootStore.deviceStore.actions;
      const baseDataSource = [
        {
          icon: require('@assets/list/61004_list_name.png'),
          title: Strings.getLang(commonDeviceNameKey),
          key: commonDeviceNameKey,
          regions: ['NA', 'EU'],
        },
        {
          icon: require('@assets/list/61004_list_wifi_configuration.png'),
          title: Strings.getLang(commonwificonfigureKey),
          key: commonwificonfigureKey,
          regions: ['NA', 'EU'],
        },
        {
          icon: require('@assets/list/61004_list_device_notification.png'),
          title: Strings.getLang(commonDeviceNotification),
          key: commonDeviceNotification,
          regions: ['NA', 'EU'],
        },
        {
          icon: require('@assets/list/61004_list_icon_regis.png'),
          title: Strings.getLang(commonRegistratonKey),
          key: commonRegistratonKey,
          regions: canBeShared ? ['NA', 'EU'] : [],
        },
        {
          icon: require('@assets/list/61004_list_icon_update_list.png'),
          title: Strings.getLang(commonUpgradeKey),
          key: commonUpgradeKey,
          regions: ['NA', 'EU'],
        },
        {
          icon: require('@assets/list/61004_list_data_static.png'),
          title: Strings.getLang(commonDataStaticKey),
          key: commonDataStaticKey,
          regions: ['NA', 'EU'],
        },
        {
          icon: require('@assets/list/61004_list_icon_baike.png'),
          title: Strings.getLang(commonProductInfoKey),
          key: commonProductInfoKey,
          regions: ['NA'],
        },
        {
          icon: require('@assets/list/61004_list_icon_infor.png'),
          title: Strings.getLang(commonAboutKey),
          key: commonAboutKey,
          regions: ['NA', 'EU'],
        },
        {
          icon: require('@assets/list/61004_list_parts.png'),
          title: Strings.getLang(commonPartsKey),
          key: commonPartsKey,
          regions: ['NA'],
        },
        {
          icon: require('@assets/list/61004_list_share_device.png'),
          title: Strings.getLang(commonShareDeviceKey),
          key: commonShareDeviceKey,
          regions: canBeShared ? ['NA', 'EU'] : [],
        },
        {
          icon: require('@assets/list/61004_list_icon_feedback.png'),
          title: Strings.getLang(commonFeedBackKey),
          key: commonFeedBackKey,
          regions: ['NA', 'EU'],
        },
      ];
      const baseDataValue = baseDataSource.filter(item =>
        item.regions.includes(region),
      );
      CommonEmitter.addListener('NAVIGATOR_ON_WILL_FOCUS', () => {
        getDetailList();
        /** 页面返回时需要再次获取并更新红点状态 */
        getWhetherDeviceHasUnreadMessage();
      });
      setBaseData(baseDataValue);
      return () => {
        CommonEmitter.removeAllListeners('NAVIGATOR_ON_WILL_FOCUS');
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
      getDetailList();
      // 发送指令获取wifi信息
      rootStore.deviceConnectionStore.actions.getDeviceWifiInfo();
      getWhetherDeviceHasUnreadMessage();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    /**
     * 获取设备详情
     */
    const getDetailList = () => {
      rootStore.requestDeviceDetail({
        req: deviceId,
      });
    };

    /**
     * 获取设备是否有未读消息
     */
    const getWhetherDeviceHasUnreadMessage = () => {
      postToFetchWhetherDeviceHasUnreadMessage({
        deviceId: deviceId,
      })
        .then(({entry}: {entry: boolean}) => {
          setHasUnreadMessage(entry);
        })
        .catch((error: unknown) => {
          console.error('Failed to fetch unread message status:', error);
          // 设置默认值，避免UI异常
          setHasUnreadMessage(false);
        });
    };

    /**
     * 解绑设备
     */
    const unBindDevice = () => {
      onShowDetailListAlert();
      mobile.simpleConfirmDialog(
        Strings.getLang('rn_common_detaillist_alerttitle_textview_text'),
        Strings.getLang(
          rootStore.deviceStore.actions.canBeShared
            ? 'rn_common_detaillist_alertmessage_deviceShare_textview_text'
            : 'rn_common_detaillist_alertmessage_textview_text',
        ),
        () => {
          const {deviceDetail} = rootStore.deviceStore.state;
          const {shareType} = deviceDetail;
          onHideDetailListAlert('confirm');
          // 主账户 删除设备前需要重置密码
          if (shareType === 1) {
            resetPasswordBeforeDelete();
          } else {
            deleteDevice();
          }
        },
        () => {
          onHideDetailListAlert('cancel');
        },
      );
    };

    const resetPasswordBeforeDelete = () => {
      deviceResetPassword({
        deviceId: deviceId,
        identifier: '24006',
        value: true,
      })
        .then(() => {
          deleteDevice();
        })
        .catch(() => {
          onHideDetailListAlert('deleteFail');
        });
    };

    const deleteDevice = () => {
      deviceUnbind({
        deviceId: deviceId,
      })
        .then(() => {
          onHideDetailListAlert('deleteSuccess');
          rootStore.deviceConnectionStore.actions.setIsNativeGoback(true);
          mobile.back();
        })
        .catch(() => {
          onHideDetailListAlert('deleteFail');
        });
    };

    const onLeftPress = () => {
      tracer.click({eleid: eleIdMap.Return_Button_Click});
      goBack(navigation);
    };
    const {showRed = false} = rootStore.deviceConfigurationStore.state;
    const {deviceDetail: detail, wifiName} = rootStore.deviceStore.state;
    const {
      connected = false, // WIFI 连接状态
      bleConnected = false, // 蓝牙连接状态
    } = rootStore.deviceConnectionStore.state;
    const {statusShowRedDot = false} = rootStore.deviceAbnormalStore.actions;

    const nickName = detail?.nickName ? detail?.nickName : detail?.deviceName;
    const disabled = !bleConnected;
    return (
      <PageView>
        <HeaderView
          title={Strings.getLang('rn_common_detaillist_title_textview_text')}
          useCommonEleId={false}
          onLeftPress={onLeftPress}
        />
        <ScrollView showsVerticalScrollIndicator={false}>
          <BaseCard
            disabled={disabled}
            connected={connected}
            data={baseData}
            bleConnected={bleConnected}
            showRed={showRed}
            hasUnreadMessage={hasUnreadMessage ?? false}
            statusShowRedDot={statusShowRedDot}
            name={detail?.nickName ? detail?.nickName : detail?.deviceName}
            hasRegisted={detail?.infoStatus - 0 === 1}
            onItemClicked={(item: {key: string}) => {
              switch (item.key) {
                case commonAboutKey:
                  tracer.click({
                    eleid: eleIdMap.Equipent_Information_Button_Click,
                  });
                  goPage(navigation, 'ViewDeviceMsg', {detail});
                  break;

                case commonwificonfigureKey: {
                  const productObj = {
                    cvn_product_id: productId,
                  };
                  const productJsonInformation = JSON.stringify(productObj);
                  if (wifiName) {
                    JumpUtils.jumpTo(
                      'ChervonIot://EGO/NetworkConfig/currentWifi',
                      {
                        sourceType: 'RN',
                        deviceId,
                        productJsonInformation,
                        offline: connected,
                        wifiName,
                      },
                    );
                  } else {
                    JumpUtils.jumpTo(
                      'ChervonIot://EGO/NetworkConfig/selectWifi',
                      {
                        sourceType: 'RN',
                        deviceId,
                        productJsonInformation,
                        offline: connected,
                        wifiName,
                      },
                    );
                  }
                  break;
                }
                case commonDeviceNotification:
                  tracer.click({
                    eleid: eleIdMap.Device_Notification_Button_Click,
                  });
                  JumpUtils.jumpTo(
                    'ChervonIot://EGO/MessageCenter/MessageList',
                    {
                      sourceType: 'RN',
                      deviceId,
                      messageType: 2,
                    },
                  );
                  break;
                case commonDataStaticKey:
                  goPage(navigation, 'ViewStatistics');
                  break;
                case commonDeviceNameKey: {
                  tracer.click({
                    eleid: eleIdMap.Equipment_Name_Button_Click,
                  });
                  const defaultName = detail?.nickName
                    ? detail?.nickName
                    : detail?.deviceName;
                  goPage(navigation, 'ViewEditName', {
                    defaultName,
                    callBack: () => {
                      getDetailList();
                    },
                  });
                  break;
                }
                case commonRegistratonKey:
                  tracer.click({
                    eleid: eleIdMap.Common_Device_Registration_Button_Click,
                  });
                  JumpUtils.jumpToRegistration(detail, deviceId);
                  break;
                case commonUpgradeKey: {
                  tracer.click({
                    eleid: eleIdMap.Common_Firmware_Update_Button_Click,
                  });
                  const url = `ChervonIot://EGO/DeviceManage/OTAInfo?deviceId=${deviceId}&singleMcu=${true}&upgradeModel=${0}&isSubset=${false}&deviceWifiIsOnline=${connected}`;
                  mobile.jumpTo(url, () => {});
                  break;
                }
                case commonProductInfoKey: {
                  tracer.click({
                    eleid: eleIdMap.Product_Encyclopedia_Button_Click,
                  });
                  const route = 'ChervonIot://EGO/DeviceManage/productHelp';
                  const params = {
                    productId,
                  };
                  JumpUtils.jumpTo(route, params);

                  break;
                }
                case commonPartsKey:
                  tracer.click({
                    eleid: eleIdMap.Enclosure_Button_Click,
                  });
                  jumpToParts({
                    productId: productId,
                    deviceId: deviceId,
                  });
                  break;
                case commonFeedBackKey: {
                  const {commodityModel, sn} = detail;
                  tracer.click({
                    eleid: eleIdMap.FeedBack_Button_Click,
                  });
                  JumpUtils.jumpTo(
                    'ChervonIot://EGO/UserCenter/feedbackPublish',
                    {
                      sourceType: 'RN',
                      productId,
                      deviceId,
                      nickName,
                      commodityModel,
                      sn,
                    },
                  );
                  break;
                }
                case commonShareDeviceKey: {
                  tracer.click({
                    eleid: eleIdMap.Share_Device_Click,
                  });
                  const deviceName = detail?.deviceName || '';
                  const deviceIcon = detail?.deviceIcon || '';
                  const route = 'ChervonIot://EGO/UserCenter/shareDeviceDetail';
                  const params = {
                    deviceId,
                    deviceName,
                    deviceIcon,
                  };
                  JumpUtils.jumpTo(route, params);
                  break;
                }
              }
            }}
          />
          <Button
            onPress={unBindDevice}
            title={Strings.getLang(
              'rn_common_detaillist_deletedevice_button_text',
            )}
            containerStyle={styles.buttonContainer}
            buttonStyle={styles.button}
            titleStyle={styles.buttonText}
          />
          <WhiteSpace size={isIphoneX ? 34 : 10} />
        </ScrollView>
      </PageView>
    );
  }),
);

export default memo(List);
export const BaseCard = ({
  data,
  name = '',
  hasRegisted = false,
  onItemClicked,
  showRed = false,
  statusShowRedDot = false,
  disabled = false,
  connected = false,
  bleConnected = false,
  hasUnreadMessage = false,
}: BaseCardProp) => {
  const {showModal} = useModal();
  const slicedName = name;
  return (
    <View style={styles.featureContainer}>
      {data.map(
        (item: {key: string; icon: string; title: string}, index: number) => {
          // Real data modification judgment conditions
          let rightExtra = <Text style={styles.rightExtraText} />;
          if (item?.key === commonDeviceNameKey) {
            rightExtra = (
              <Text
                numberOfLines={1}
                style={[styles.rightExtraText, styles.deviceName]}>
                {slicedName}
              </Text>
            );
          }
          if (item?.key === commonRegistratonKey) {
            if (hasRegisted) {
              rightExtra = (
                // <Text style={styles.registerText}>
                <Text style={[styles.registerText, styles.rightExtraText]}>
                  {Strings.getLang(
                    'rn_common_detaillist_registered_textview_text',
                  )}
                </Text>
              );
            } else {
              rightExtra = (
                <Text style={[styles.registerText, styles.rightExtraText]}>
                  {Strings.getLang(
                    'rn_common_detaillist_unregistered_textview_text',
                  )}
                </Text>
              );
            }
          }
          if (item?.key === commonUpgradeKey && showRed) {
            rightExtra = <View style={styles.redDot} />;
          }
          if (item?.key === commonDeviceNotification && hasUnreadMessage) {
            rightExtra = <View style={styles.redDot} />;
          }
          if (item?.key === commonDeviceStatusKey && statusShowRedDot) {
            rightExtra = <View style={styles.redDot} />;
          }
          let newDisable = false;
          if (
            item?.key === commonDeviceStatusKey ||
            item?.key === changePasswordKey
          ) {
            newDisable = disabled;
          } else if (item?.key === commonUpgradeKey) {
            newDisable = !connected && !bleConnected;
          }
          return (
            <Item
              disabled={newDisable}
              leftIconSource={item.icon}
              leftIconStyle={styles.leftIcon}
              onPress={() => {
                if (item?.key === commonwificonfigureKey && !bleConnected) {
                  showModal({
                    content: Strings.getLang(
                      'rn_61004_wificonfig_bledisconnected_textview_text',
                    ),
                    onConfirm: () => {},
                  });
                } else {
                  onItemClicked(item);
                }
              }}
              hideLine={index === data.length - 1}
              key={item.title}
              title={item.title}
              titleStyle={styles.title}
              rightExtra={rightExtra}
            />
          );
        },
      )}
    </View>
  );
};
interface BaseCardProp {
  name: string;
  hasRegisted: boolean;
  onItemClicked: (res: {key: string}) => void;
  showRed: boolean;
  statusShowRedDot: boolean;
  data: {icon: string; title: string; key: string; regions: string[]}[];
  disabled: boolean;
  connected: boolean;
  bleConnected: boolean;
  hasUnreadMessage: boolean;
}
const styles = StyleSheet.create({
  heading: {
    color: 'white',
    fontSize: 22,
    fontWeight: 'bold',
  },
  headerLeft: {
    display: 'flex',
    flexDirection: 'row',
    marginTop: 5,
  },
  headerRight: {
    display: 'flex',
    flexDirection: 'row',
    marginTop: 5,
  },
  featureContainer: {
    marginTop: 10,
    borderRadius: 10,
    marginHorizontal: 15,
    overflow: 'hidden',
  },
  rightExtraText: {
    color: '#666666',
    fontSize: 13,
  },
  deviceName: {
    width: DEVICE_WIDTH - 135 - 66 - 30,
    textAlign: 'right',
  },
  registerText: {
    color: '#77BC1F',
    fontSize: 13,
  },
  redDot: {
    borderRadius: 6.5,
    width: 13,
    height: 13,
    backgroundColor: '#EC6464',
  },
  buttonContainer: {
    marginHorizontal: 15,
    height: 48,
    marginTop: 10,
    borderRadius: 10,
  },
  button: {
    backgroundColor: '#ffffff',
    borderRadius: 2,
    justifyContent: 'flex-start',
    paddingLeft: 23,
    height: 50,
  },
  buttonText: {
    color: '#77BC1F',
    fontSize: 15,
  },
  // card
  leftIcon: {
    width: 28,
    height: 28,
  },
  title: {
    marginLeft: 0,
  },
});
