import React, {useCallback, useEffect, useState} from 'react';
import {Text, View, StyleSheet, ScrollView} from 'react-native';
import {PageView} from 'cvn-panel-kit';
import {Item, HeaderView} from '@components';
import {observer} from 'mobx-react';
import Strings from '@i18n';
import pkg from '../../../package.json';
import {goBack} from 'IOTRN/src/utils/pageAction';

interface DeviceMsgProps {
  route?: {
    params?: {
      detail?: {
        commodityModel?: string;
        sn?: string;
        deviceId?: string;
        assemblySnList?: string[];
        version?: string;
      };
      [key: string]: unknown;
    };
  };
  navigation?: {
    goBack: () => void;
    [key: string]: Function;
  };
}
const outData = [
  {
    title: Strings.getLang('rn_common_devicemsg_modelno_textview_text'),
    key: 'MODEL NO.',
    content: '',
  },
  {
    title: Strings.getLang('rn_common_devicemsg_sn_textview_text'),
    key: 'Serial number',
    content: '',
  },
  {
    title: Strings.getLang('rn_common_devicemsg_deviceid_textview_text'),
    key: 'Device ID',
    content: '',
  },
  {
    title: Strings.getLang('rn_common_devicemsg_assemblysn_textview_text'),
    key: 'Assembly serial number',
    content: '',
  },
  {
    title: Strings.getLang('rn_common_devicemsg_version_textview_text'),
    key: 'Firmware Version',
    content: '',
  },
  {
    title: Strings.getLang('rn_common_devicemsg_rnversion_textview_text'),
    key: 'RN Version',
    content: '',
  },
];

const DeviceMsg = observer((props: DeviceMsgProps) => {
  const [data, setData] = useState<
    {
      title: string;
      key: string;
      content: string;
    }[]
  >([]);
  /**
   * @param {string} params
   */
  const getParam = useCallback(
    (params: string) => {
      const navigationParams = props.route?.params;
      return navigationParams?.[params] ?? '';
    },
    [props.route?.params],
  );

  useEffect(() => {
    const detail = getParam('detail') ?? {};
    const tmpDetail = {...detail} as {
      commodityModel?: string;
      sn?: string;
      deviceId?: string;
      assemblySnList?: string[];
      version?: string;
    };
    const tmpData = outData.map((item, index) => {
      const obj = {...item};
      switch (index) {
        case 0:
          obj.content = tmpDetail.commodityModel ?? '';
          break;
        case 1:
          obj.content = tmpDetail.sn ?? '';
          break;
        case 2:
          obj.content = tmpDetail.deviceId ?? '';
          break;
        case 3:
          obj.content = tmpDetail.assemblySnList?.join() ?? ''; // Assembly serial numb
          break;
        case 4:
          obj.content = tmpDetail.version ?? '';
          break;
        case 5:
          obj.content = pkg.version;
          break;
        default:
          break;
      }

      return obj;
    });
    setData(tmpData);
  }, [getParam]);

  return (
    <PageView>
      <HeaderView
        title={Strings.getLang('rn_common_devicemsg_title_textview_text')}
        onLeftPress={() => {
          if (props?.navigation) {
            goBack(props.navigation);
          }
        }}
      />
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <Card data={data} />
      </ScrollView>
    </PageView>
  );
});

export default DeviceMsg;
interface CardProps {
  data?: {title: string; key: string; content: string}[];
}

export const Card = ({data}: CardProps) => {
  return (
    <View style={styles.cardBox}>
      {data?.map((item, index) => {
        if (index === 3) {
          return (
            <View key="3" style={styles.cusItemBox}>
              <View style={styles.cusItem}>
                <Text style={styles.cusTitle}>{item.title}</Text>
                <Text style={styles.cusContent} numberOfLines={0}>
                  {item.content}
                </Text>
              </View>
              <View style={styles.cusLine} />
            </View>
          );
        } else {
          return (
            <Item
              iconShow={false}
              hideLine={index === data.length - 1 || index === 3}
              lineStyle={styles.lineStyle}
              key={item.title}
              title={item.title}
              rightElement={
                <Text style={styles.rightText}>{item.content}</Text>
              }
            />
          );
        }
      })}
    </View>
  );
};
const styles = StyleSheet.create({
  cardBox: {
    backgroundColor: '#ffffff',
  },
  rightText: {
    color: '#999999',
    fontSize: 12,
    flexShrink: 2,
    marginLeft: 8,
  },
  cusItemBox: {
    backgroundColor: '#ffffff',
  },
  cusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    paddingRight: 16,
    justifyContent: 'space-between',
    backgroundColor: '#ffffff',
  },
  cusTitle: {
    color: '#000000',
    fontSize: 15,
  },
  cusContent: {
    color: '#999999',
    fontSize: 12,
    // width: DEVICE_WIDTH >= 400 ? 150 : 130,
    textAlign: 'right',
    flexShrink: 2,
    marginLeft: 10,
  },
  cusLine: {
    backgroundColor: '#E5E5E5',
    height: 0.4,
    marginLeft: 20,
    marginRight: 16,
  },
  versionText: {
    alignSelf: 'center',
    marginTop: 20,
    color: '#333333',
  },
  lineStyle: {
    marginLeft: 20,
    marginRight: 16,
  },
  scrollContainer: {
    paddingTop: 15,
  },
});
