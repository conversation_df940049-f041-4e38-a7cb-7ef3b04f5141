import React from 'react';
import {View, StyleSheet, ScrollView} from 'react-native';
import {inject, observer} from 'mobx-react/native';
import PageView from '@components/PageView.js';
import PageBase from '@base/index.js';
import {Cell, HeaderView} from '@components';
import {withTranslation} from 'react-i18next';
import {geti18nText} from '@i18n/config';
import CustomText from 'IOTRN/src/components/CustomText';

/**
 * @typedef {Object} InjectedProps
 * @property {panelStores} panel - MobX Store
 * @property {panelActions} panelActions - Mobx Store Action
 * @property {TFunction} t - i18next translation function
 */

@inject('panel')
@observer
class Statistics extends PageBase {
  /**
   * @type {InjectedProps}
   */
  props;

  componentDidMount() {}

  render() {
    const {t} = this.props;
    const bleConnected = this.props.panel.bleConnected;
    const {
      totalChargingTime,
      totalDischargingTime,
      batteryHealthCondition,
      batteryCycleCount,
    } = this.props.panel.usageStatistics;

    const isValidValue = value => bleConnected && value >= 0;
    const dataFormat = (item, unit = '') => {
      return isValidValue(item) ? `${item}${unit}` : '--';
    };
    const conditionFormat = item => {
      return !isValidValue(item)
        ? '--'
        : item >= 60
        ? t(geti18nText('statistics_batteryHealthNormal_textview_text'))
        : t(geti18nText('statistics_batteryHealthAbnormal_textview_text'));
    };
    const formatTimeString = string => {
      return bleConnected ? string : '--';
    };

    const usagelist = [
      {
        key: 'chargingTime',
        title: t(geti18nText('statistics_chargingTime_textview_text')),
        rightElement: (
          <View style={styles.rightBox}>
            <CustomText
              numberOfLines={1}
              ellipsizeMode="tail"
              style={styles.extraText}>
              {formatTimeString(totalChargingTime)}
            </CustomText>
          </View>
        ),
      },
      {
        key: 'dischargingTime',
        title: t(geti18nText('statistics_disChargingTime_textview_text')),
        rightElement: (
          <View style={styles.rightBox}>
            <CustomText
              numberOfLines={1}
              ellipsizeMode="tail"
              style={styles.extraText}>
              {formatTimeString(totalDischargingTime)}
            </CustomText>
          </View>
        ),
      },
    ];

    const healthList = [
      {
        key: 'batteryCycleCount',
        title: t(geti18nText('statistics_batteryCycleCount_textview_text')),
        rightElement: (
          <View style={styles.rightBox}>
            <CustomText
              numberOfLines={1}
              ellipsizeMode="tail"
              style={styles.extraText}>
              {dataFormat(batteryCycleCount)}
            </CustomText>
          </View>
        ),
      },
      {
        key: 'batteryHealthCondition',
        title: t(
          geti18nText('statistics_batteryHealthCondition_textview_text'),
        ),
        rightElement: (
          <View style={styles.rightBox}>
            <CustomText
              numberOfLines={1}
              ellipsizeMode="tail"
              style={styles.extraText}>
              {conditionFormat(batteryHealthCondition)}
            </CustomText>
          </View>
        ),
      },
    ];

    return (
      <PageView>
        <HeaderView
          title={t(geti18nText('statistics_title_textview_text'))}
          onLeftPress={() => {
            this.goBack();
          }}
        />
        <ScrollView>
          <DataListCard
            name={t(geti18nText('statistics_usageInfomation_textview_text'))}
            list={usagelist}
          />

          <DataListCard
            name={t(geti18nText('statistics_healthInformation_textview_text'))}
            list={healthList}
          />
        </ScrollView>
      </PageView>
    );
  }
}

const DataListCard = ({name = '', list = []}) => {
  const size = list.length;
  return (
    <View style={styles.container}>
      <CustomText style={styles.name}>{name}</CustomText>
      <View style={styles.listContainer}>
        {list.map((item, index) => (
          <Cell
            key={item.key}
            title={item.title}
            titleStyle={styles.extraText}
            iconShow={false}
            rightElement={item.rightElement}
            containerStyle={styles.containerStyle}
            hideRightArrow={true}
            showWholeUnderline={index + 1 < size}
            isClickable={false}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 15,
  },
  rightBox: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  name: {
    fontSize: 16,
    fontWeight: '400',
    color: '#3C3936',
    paddingHorizontal: 15,
    marginTop: 20,
    marginBottom: 10,
  },
  listContainer: {
    borderRadius: 10,
    overflow: 'hidden',
    backgroundColor: '#fff',
  },
  extraText: {
    color: '#3C3936',
    fontSize: 16,
    fontWeight: '400',
  },
  titleTextStyle: {},
  containerStyle: {
    marginHorizontal: 17,
  },
});

export default withTranslation('all')(Statistics);
