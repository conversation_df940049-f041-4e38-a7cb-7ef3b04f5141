import React from 'react';
import {View, ScrollView, Platform} from 'react-native';
import PageBase from '@base/index.js';
import PageView from '@components/PageView.js';
import {Cell, HeaderView} from '@components';
import CustomStyleSheet from '@utils/style/index.js';
import {withTranslation} from 'react-i18next';
import {isArray} from '@utils/type';
import {geti18nText} from '@i18n/config';
import pkg from 'IOTRN/package.json';
import CustomText from 'IOTRN/src/components/CustomText';

/**
 * @typedef {import('react-i18next').TFunction} TFunction
 */

/**
 * @typedef {Object} InjectedProps
 * @property {TFunction} t - i18next translation function
 */

class DeviceMsg extends PageBase {
  /**
   * @type {InjectedProps}
   */
  props;

  constructor(props) {
    super(props);
    this.state = {
      list: [],
    };
  }

  componentDidMount() {
    const detail = this.getParams('detail');
    // console.log('detail----', JSON.stringify(detail));
    const detailInfo = {...detail};
    const {t} = this.props;
    const listData = [
      // MODEL NO.
      {
        key: 'commodityModel',
        title: t(geti18nText('devicemsg_modelno_textview_text', true)),
      },
      // Serial number
      {
        key: 'sn',
        title: t(geti18nText('devicemsg_sn_textview_text', true)),
      },
      // Device ID
      {
        key: 'deviceId',
        title: t(geti18nText('devicemsg_deviceid_textview_text', true)),
      },
      // Assembly serial number
      {
        key: 'assemblySnList',
        title: t(geti18nText('devicemsg_assemblysn_textview_text', true)),
      },
      // Firmware Version
      {
        key: 'version',
        title: t(geti18nText('devicemsg_version_textview_text', true)),
      },
      // rn version
      {
        key: 'RN version',
        title: t(geti18nText('devicemsg_rnversion_textview_text', true)),
        value: pkg.version,
      },
    ];
    this.setState({
      list: listData.map(item => ({
        ...item,
        content:
          item.key === 'RN version' ? item.value : detailInfo?.[item.key],
      })),
    });
  }

  render() {
    const {t} = this.props;
    const {list} = this.state;
    return (
      <PageView>
        <HeaderView
          title={t(geti18nText('devicemsg_title_textview_text', true))}
          onLeftPress={() => {
            this.goBack();
          }}
        />
        <ScrollView contentContainerStyle={{paddingTop: 15}}>
          <View style={styles.cardBox}>
            {list.map((item, index) => {
              return (
                <Cell
                  key={item.title}
                  title={item.title}
                  iconShow={false}
                  isClickable={false}
                  titleStyle={styles.title}
                  underlineStyle={styles.underline}
                  containerStyle={styles.itemContainer}
                  hideRightArrow={true}
                  showWholeUnderline={index !== list.length - 1}
                  rightElement={
                    isArray(item.content) ? (
                      <>
                        {item.content.map((part, idx) => (
                          <CustomText
                            key={idx}
                            style={styles.rightText}
                            numberOfLines={1}
                            ellipsizeMode="tail">
                            {part}
                          </CustomText>
                        ))}
                      </>
                    ) : (
                      <CustomText
                        style={styles.rightText}
                        numberOfLines={1}
                        ellipsizeMode="tail">
                        {item.content}
                      </CustomText>
                    )
                  }
                />
              );
            })}
          </View>
        </ScrollView>
      </PageView>
    );
  }
}

export default withTranslation('all')(DeviceMsg);

const styles = CustomStyleSheet.create({
  cardBox: {
    backgroundColor: '#fff',
  },
  itemContainer: {
    marginHorizontal: 20,
  },
  rightText: {
    color: '#999999',
    fontSize: 14,
    fontWeight: '400',
    lineHeight: 20,
  },
  underline: {
    marginTop: Platform.OS === 'ios' ? 1 : 0.5,
  },
});
