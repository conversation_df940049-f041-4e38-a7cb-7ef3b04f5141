import React from 'react';
import {View, ScrollView} from 'react-native';
import CustomStyleSheet from '@utils/style/index.js';
import {inject, observer} from 'mobx-react/native';
import {CommonEmitter} from '@cvn/rn-panel-kit';
import PageBase from '@base/index.js';
import PageView from '@components/PageView.js';
import {Cell, HeaderView, WhiteSpace} from '@components';
import {isIphoneX} from '@utils/device';
import PropTypes from 'prop-types';
import {getImageUrl} from '@utils/image.js';
import {useTranslation, withTranslation} from 'react-i18next';
import {geti18nText} from '@i18n/config';
import CustomText from 'IOTRN/src/components/CustomText';

/**
 * @typedef {import('@/mobx').panelStores} panelStores
 * @typedef {import('@/mobx').panelActions} panelActions
 * @typedef {import('react-i18next').TFunction} TFunction
 */

/**
 * @typedef {Object} InjectedProps
 * @property {panelStores} panel - MobX Store
 * @property {panelActions} panelActions - Mobx Store Action
 * @property {TFunction} t - i18next translation function
 */

@inject('panelActions', 'panel')
@observer
class List extends PageBase {
  /**
   * @type {InjectedProps}
   */
  props;

  componentDidMount() {
    CommonEmitter.addListener('NAVIGATOR_ON_WILL_FOCUS', () => {
      this.getDetailList();
    });
    this.getDetailList();
  }

  componentWillUnmount() {
    CommonEmitter.removeAllListeners('NAVIGATOR_ON_WILL_FOCUS');
  }

  getDetailList = () => {
    this.props.panelActions.fetchDeviceDetail({showLoading: true});
  };

  render() {
    const {t} = this.props;
    const {
      showRed = false,
      deviceDetail: detail,
      deviceName,
      isWifiConnected,
      region,
    } = this.props.panel;
    const isInteractive = isWifiConnected;
    return (
      <PageView>
        <HeaderView
          title={t(geti18nText('detaillist_title_textview_text', true))}
          onLeftPress={() => {
            this.goBack();
          }}
        />
        <ScrollView>
          <DataListCard
            region={region}
            name={deviceName}
            panelActions={this.props.panelActions}
            detail={detail}
            showRed={isInteractive ? showRed : false}
            disabled={!isInteractive}
            goPage={this.goPage}
          />
          <WhiteSpace size={isIphoneX ? 34 : 0} />
        </ScrollView>
      </PageView>
    );
  }
}

export default withTranslation('all')(List);

export const DataListCard = ({
  region = '',
  name = '',
  detail = {},
  showRed = false,
  disabled = false,
  goPage = () => {},
  panelActions = {},
}) => {
  const {t} = useTranslation('all');

  const dataList = [
    {
      key: 'devicename',
      icon: getImageUrl('common_icon_devicename'),
      title: geti18nText('detaillist_devicename_textview_text', true),
      isContentVertical: true,
      rightExtra: (
        <CustomText numberOfLines={1} style={styles.rightExtraText}>
          {name}
        </CustomText>
      ),
      regions: ['NA', 'EU'],
      onClick: () => {
        goPage('ViewEditName', {
          deviceName: name,
        });
      },
    },
    {
      key: 'update',
      icon: getImageUrl('common_icon_otaupdate'),
      title: geti18nText('detaillist_upgrade_textview_text', true),
      rightExtra: showRed ? <View style={styles.redDot} /> : null,
      regions: ['NA', 'EU'],
      disabled: disabled,
      onClick: () => {
        panelActions.jumpToOTA();
      },
    },
    {
      key: 'usage_statistics',
      icon: getImageUrl('common_icon_statistics'),
      disabled: disabled,
      title: geti18nText('detaillist_statistics_textview_text', true),
      regions: ['NA', 'EU'],
      onClick: () => {
        goPage('ViewUsageStatistics');
      },
    },
    {
      key: 'productintro',
      icon: getImageUrl('common_icon_productinfo'),
      title: geti18nText('detaillist_productintro_textview_text', true),
      rightExtra: null,
      regions: ['NA'],
      onClick: () => {
        panelActions.jumpToProductIntro();
      },
    },
    {
      key: 'about',
      icon: getImageUrl('common_icon_about'),
      title: geti18nText('detaillist_about_textview_text', true),
      rightExtra: null,
      regions: ['NA', 'EU'],
      onClick: () => {
        goPage('ViewAbout', {detail});
      },
    },
  ];
  return (
    <View style={styles.featureContainer}>
      {dataList
        .filter(item => item.regions.includes(region))
        .map((item, index) => {
          return (
            <Cell
              key={item.key}
              title={t(item.title)}
              leftIconSource={item.icon}
              leftIconStyle={styles.leftIcon}
              containerStyle={styles.itemContainer}
              isContentVertical={item.isContentVertical}
              rightExtra={item.rightExtra}
              disabled={item.disabled}
              showRightUnderline={index !== dataList.length - 1}
              isClickable={!item.disabled}
              onPress={item.onClick}
            />
          );
        })}
    </View>
  );
};

DataListCard.propTypes = {
  region: PropTypes.string,
  name: PropTypes.string,
  showRed: PropTypes.bool,
  disabled: PropTypes.bool,
  goPage: PropTypes.func,
  panelActions: PropTypes.object,
  detail: PropTypes.object,
};

const styles = CustomStyleSheet.create({
  featureContainer: {
    marginTop: 15,
    borderRadius: 10,
    marginHorizontal: 15,
    overflow: 'hidden',
    backgroundColor: '#fff',
  },
  itemContainer: {
    marginHorizontal: 15,
  },
  rightExtraText: {
    color: '#666666',
    fontSize: 13,
    fontWeight: '400',
    lineHeight: 16,
  },
  registerText: {
    color: '#77BC1F',
    fontSize: 13,
    fontWeight: '400',
  },
  redDot: {
    borderRadius: 5.5,
    width: 11,
    height: 11,
    backgroundColor: '#FF4646',
  },
  leftIcon: {
    marginRight: 12.5,
  },
});
