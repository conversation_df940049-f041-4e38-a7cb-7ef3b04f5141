import {geti18nText} from '@i18n/config';

/**
 * working status
 * @readonly
 */
export const workingStatusModeMap = {
  0: /** @type {'reserved'} */ ('reserved'),
  1: /** @type {'acCharging'} */ ('acCharging'),
  2: /** @type {'dcDischarging'} */ ('dcDischarging'),
  3: /** @type {'standby'} */ ('standby'),
  4: /** @type {'error'} */ ('error'),
};

/**
 * working status of sub-device
 * @readonly
 */
export const subDevicesBatteryWorkingStatusModeMap = {
  0: /** @type {'empty'} */ ('empty'),
  1: /** @type {'charging'} */ ('charging'),
  2: /** @type {'discharging'} */ ('discharging'),
  3: /** @type {'ready'} */ ('ready'),
  4: /** @type {'overheat'} */ ('overheat'),
  5: /** @type {'standby'} */ ('standby'),
  6: /** @type {'lowPower'} */ ('lowPower'),
  7: /** @type {'error'} */ ('error'),
};

export const subDevicesBatteryWorkingStatusTextMap = {
  0: geti18nText('home_emptyBattery_textview_text'),
  1: geti18nText('home_chargingBattery_textview_text'),
  2: geti18nText('home_dischargingBattery_textview_text'),
  3: geti18nText('home_readyBattery_textview_text'),
  4: geti18nText('home_overheatBattery_textview_text'),
  5: geti18nText('home_standbyBattery_textview_text'),
  6: geti18nText('home_lowPowerBattery_textview_text'),
  7: geti18nText('home_errorBattery_textview_text'),
};
