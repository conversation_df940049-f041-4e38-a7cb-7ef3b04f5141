import React from 'react';
import Svg, {Path, Circle} from 'react-native-svg';

const ChargingIcon = ({width = 58, height = 58, ...props}) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 116 116"
    fill="none"
    {...props}>
    <Path fill="#000" d="M38.188 38.189h39.623v39.623H38.188z" opacity={0.01} />
    <Path
      fill="#77BC1F"
      d="M51.726 76.16c-.33 0-.495 0-.825-.165-.66-.33-.99-1.155-.826-1.816l3.632-13.538h-9.245c-.66 0-1.156-.33-1.486-.825a1.5 1.5 0 0 1 0-1.65l9.08-17.5c.166-.496.826-.826 1.321-.826h14.859c.66 0 1.155.33 1.486.99.33.66.165 1.32-.166 1.816l-6.934 8.75h8.916c.66 0 1.32.33 1.485.99.33.661.33 1.322-.165 1.817L53.047 75.665c-.33.33-.825.495-1.32.495Z"
    />
    <Circle cx={58} cy={58} r={55.5} stroke="#77BC1F" strokeWidth={5} />
  </Svg>
);

export default ChargingIcon;
