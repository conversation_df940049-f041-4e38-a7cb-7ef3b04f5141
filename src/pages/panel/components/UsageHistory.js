import React, {useMemo, useState} from 'react';
import {View} from 'react-native';
import {inject} from 'mobx-react';
import LineChart from '@components/LineChart';
import Card from '@components/Card';
import {geti18nText} from '@i18n/config';
import {isArray, map} from 'lodash-es';
import PropTypes from 'prop-types';
import {getImageUrl} from '@utils/image.js';
import {toJS} from 'mobx';
import dayjs from 'dayjs';
import {useTranslation} from 'react-i18next';
import CustomStyleSheet from '@utils/style';
import QuestionIcon from '@components/Icon/QuestionIcon';
import PressableButton from '@components/PressableButton';
import TipPopup from '@components/TipPopup.js';
import CustomText from 'IOTRN/src/components/CustomText';

const UsageHistory = inject(store => ({
  usageData: store.panel.usageData,
  workingTime: store.panel.home.totalDischargingTime,
  region: store.panel.region,
}))(
  ({
    workingTime = 0,
    usageData = [],
    region = 'NA',
    disabled = false,
    style = {},
  }) => {
    const list = toJS(usageData);

    const {t} = useTranslation('all');
    const [showTip, setShowTip] = useState(false);

    const datasetList = useMemo(() => {
      if (disabled) {
        return [];
      }
      if (isArray(list)) {
        return map(list, item => item || 0);
      }
      return [];
    }, [list, disabled]);

    // 过去12个月份的月份
    const labels = Array.from({length: 12}, (_, i) =>
      dayjs()
        .subtract(11 - i, 'month')
        .format('MMM'),
    );

    let tip = t(geti18nText('home_usageHistoryNaTip_textview_text'));

    if (region === 'EU') {
      tip = t(geti18nText('home_usageHistoryEuTip_textview_text'));
    }

    return (
      <>
        <Card
          icon={{
            uri: getImageUrl('common_icon_usagehistory'),
          }}
          style={style}
          title={t(geti18nText('home_usageHistory_textview_text'))}
          disabled={disabled}
          rightElement={
            <PressableButton onPress={() => setShowTip(true)}>
              <QuestionIcon width={22} height={22} />
            </PressableButton>
          }
          subElement={
            disabled ? null : (
              <View style={styles.workTimeContainer}>
                <CustomText style={styles.workTimeDesc}>
                  {t(geti18nText('home_totalWorkingTime_textview_text'))}
                </CustomText>
                <CustomText fontWeight={500} style={styles.workTimeValue}>
                  {workingTime}
                </CustomText>
              </View>
            )
          }>
          <LineChart list={datasetList} labels={labels} />
        </Card>
        <TipPopup
          visible={showTip}
          tipText={tip}
          confirmText={t(
            geti18nText('home_usageHistoryTipConfirm_textview_text'),
          )}
          onPress={() => setShowTip(false)}
        />
      </>
    );
  },
);

UsageHistory.propTypes = {
  workingTime: PropTypes.number,
  usageData: PropTypes.array,
  region: PropTypes.string,
  disabled: PropTypes.bool,
  style: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
};

const styles = CustomStyleSheet.create({
  workTimeContainer: {
    marginStart: 44,
    marginTop: 3.5,
    flexDirection: 'row',
    alignItems: 'center',
  },
  workTimeDesc: {
    fontSize: 14,
    color: '#999999',
    fontWeight: '400',
    marginRight: 5,
    flexShrink: 1,
  },
  workTimeValue: {
    fontSize: 18,
    color: '#77BC1F',
    fontWeight: '500',
  },
});

export default UsageHistory;
