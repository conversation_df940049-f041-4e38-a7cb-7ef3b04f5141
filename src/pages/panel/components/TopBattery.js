import React, {memo} from 'react';
import {View, StyleSheet} from 'react-native';
import {inject} from 'mobx-react';
import CVNRCTChargerCircle from '@cvn/react-native-circle-battery';
import {useTranslation} from 'react-i18next';
import {geti18nText} from '@i18n/config';
import PropTypes from 'prop-types';
import CustomText from 'IOTRN/src/components/CustomText';

const BATTERY_HEALTH_MAP = {
  '-1': 'empty',
  // 0: 'reserved',
  1: 'charging',
  2: 'standby', // discharging 同 standby
  3: 'fullyCharged',
  4: 'error', // chargingFailed 同 error
  5: 'standby',
  6: 'overheat',
  7: 'error',
};

const TopBattery = inject(store => ({
  remainingBattery: store.panel.home.remainingBattery,
  batteryStatusNumber: store.panel.home.batteryStatus,
  remainingChargingTime: store.panel.home.remainingChargingTime,
  batteryCapacity: store.panel.home.batteryCapacity,
}))(
  ({
    remainingChargingTime = 0,
    remainingBattery = -1,
    batteryStatusNumber = -1,
    batteryCapacity = 40,
    navHeaderHeight = 80,
  }) => {
    const {t} = useTranslation('all');
    const {hour, min} = remainingChargingTime;
    const containerStyle = createContainerStyle(navHeaderHeight);
    const batteryStatus = BATTERY_HEALTH_MAP[batteryStatusNumber] || 'empty';
    const isFullyCharged = batteryStatus === 'fullyCharged';
    const isStandby = batteryStatus === 'standby';
    const isShowFinishTime =
      ['charging', 'overheat'].includes(batteryStatus) &&
      (Number(hour) > 0 || Number(min) > 0);
    const isValidValue = value => value >= 0;
    const dataFormat = (item, unit = '') => {
      return isValidValue(item) ? `${item}${unit}` : '--';
    };
    const textList = [];
    if (isFullyCharged) {
      textList.push(
        <View style={styles.itemContainer} key="fully-charged">
          <CustomText fonwitWeight={400} style={styles.extraText}>
            {t(geti18nText('home_fullyChargedBaterryStatus_textview_text'))}
          </CustomText>
        </View>,
      );
    }
    if (isShowFinishTime) {
      textList.push(
        <View style={styles.itemContainer} key="finish-time">
          <CustomText fonwitWeight={400} style={styles.extraText}>
            {t(geti18nText('home_finishIn_textview_text'))}{' '}
            {Number(hour) > 0 && (
              <>
                <CustomText fonwtWeight={500} style={styles.extraValue}>
                  {hour}
                </CustomText>
                hr
              </>
            )}
            {Number(min) > 0 && (
              <>
                <CustomText fonwitWeight={500} style={styles.extraValue}>
                  {min}
                </CustomText>
                min
              </>
            )}
          </CustomText>
        </View>,
      );
    }
    const renderTextsBelowCircleCharger = () => {
      if (isStandby) {
        return null;
      }
      if (textList.length === 0) {
        return (
          <View style={styles.itemContainer}>
            <CustomText fonwitWeight={400} style={styles.extraText}>
              --
            </CustomText>
          </View>
        );
      }
      return textList;
    };
    return (
      <>
        <View style={containerStyle.container}>
          <View style={styles.chargerContainer}>
            <CVNRCTChargerCircle
              value={remainingBattery}
              status={batteryStatus}
              healthStatus={''}
              style={{flex: 1}}
            />
            <View style={styles.extraTextContainer}>
              <CustomText fonwtWeight={500} style={styles.extraTextAh}>
                {dataFormat(batteryCapacity, 'Ah')}
              </CustomText>
            </View>
          </View>
        </View>
        <View style={styles.chargerDescContainer}>
          {renderTextsBelowCircleCharger()}
        </View>
      </>
    );
  },
);

const createContainerStyle = navHeaderHeight =>
  StyleSheet.create({
    container: {
      height: navHeaderHeight + 300,
      paddingTop: navHeaderHeight + 10,
    },
  });

TopBattery.defaultName = 'TopBattery';
TopBattery.propTypes = {
  navHeaderHeight: PropTypes.number,
  batteryCapacity: PropTypes.number,
  batteryStatusNumber: PropTypes.number,
  remainingBattery: PropTypes.number,
  remainingChargingTime: PropTypes.number,
};

export default memo(TopBattery);

const styles = StyleSheet.create({
  chargerContainer: {
    height: 300,
    flex: 1,
  },
  chargerDescContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  itemContainer: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  extraValue: {
    fontSize: 24,
    fontWeight: '500',
    color: '#000',
  },
  extraTextContainer: {
    position: 'absolute',
    top: 4.5,
    right: 20,
    borderRadius: 18.5,
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: 'rgb(19,76,35)',
  },
  extraText: {
    fontSize: 18,
    fontWeight: '400',
    color: '#3C3936',
  },
  extraTextAh: {
    fontSize: 18,
    fontWeight: '500',
    color: '#FFF',
  },
});
