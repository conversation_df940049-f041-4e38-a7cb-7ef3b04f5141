import CustomText from 'IOTRN/src/components/CustomText';
import React from 'react';
import {View, StyleSheet} from 'react-native';

const StatusBatteryItem = ({
  status = 'ready',
  num = 0,
  unit = 'slots',
  mark = null,
  markColor = '#77BC1F',
  showExtraValue = false,
  extraValue,
  disabled = true,
  ...props
}) => {
  const blankText = '--';
  const isValidValue = value => value >= 0;
  const formateValue = (value, unit_1) => {
    return disabled
      ? blankText
      : value > 0
      ? `${value}${unit_1}`
      : `${blankText}${unit_1}`;
  };

  return (
    <View style={[styles.statusBatteryContainer]} {...props}>
      <View style={styles.flexCenter}>
        {showExtraValue ? (
          <View
            style={[
              styles.extraMark,
              disabled && {
                backgroundColor: '#DCDCDC',
              },
            ]}>
            <CustomText
              fontWeight={500}
              style={[
                styles.extraMarkText,
                disabled && {
                  color: '#000',
                },
              ]}>
              {formateValue(extraValue, 'Wh')}
            </CustomText>
          </View>
        ) : null}
        <CustomText fonwtWeight={500} style={styles.name}>
          {status}
        </CustomText>
      </View>
      <View style={[styles.flexCenter, styles.statusContainer]}>
        {disabled || !isValidValue(num) ? (
          <CustomText style={styles.num}>{blankText}</CustomText>
        ) : (
          <>
            <CustomText fontWeight={500} style={styles.num}>
              {num}
            </CustomText>
            <CustomText fontWeight={500} style={styles.unit}>
              {unit}
            </CustomText>
          </>
        )}
      </View>
      <View style={[styles.mark, {backgroundColor: markColor}]}>{mark}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  statusBatteryContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    width: '48%',
    marginBottom: 13,
  },
  flexCenter: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  extraMark: {
    backgroundColor: '#77BC1F',
    borderTopLeftRadius: 10,
    borderBottomRightRadius: 10,
    height: 30,
    paddingHorizontal: 13,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: -8,
  },
  extraMarkText: {
    fontSize: 14,
    // lineHeight: 14,
    fontWeight: '500',
    color: '#FFFFFF',
  },
  name: {
    fontSize: 18,
    // lineHeight: 16,
    fontWeight: '500',
    color: '#000000',
    paddingHorizontal: 15,
    paddingVertical: 5.5,
  },
  statusContainer: {
    paddingTop: 12,
    paddingBottom: 10,
    paddingHorizontal: 15,
  },
  num: {
    fontSize: 30,
    lineHeight: 35,
    fontWeight: '500',
    color: '#000000',
    marginRight: 6,
  },
  unit: {
    fontSize: 16,
    // lineHeight: 14,
    fontWeight: '400',
    color: '#000000',
  },
  mark: {
    position: 'absolute',
    right: 0,
    bottom: 0,
    borderTopLeftRadius: 8.82,
    borderBottomRightRadius: 8.82,
    backgroundColor: '#77BC1F',
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default StatusBatteryItem;
