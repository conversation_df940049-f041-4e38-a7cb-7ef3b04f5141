import React, {memo} from 'react';
import {View} from 'react-native';
import PropTypes from 'prop-types';
import {inject} from 'mobx-react';
import CustomStyleSheet from '@utils/style/index.js';
import {useTranslation} from 'react-i18next';
import {geti18nText} from '@i18n/config';
import {BatteryPanel} from '@components/Battery';
import {DEVICE_WIDTH} from '@utils/device/index.js';
import LightningIcon from './Icons/LightningIcon.js';
import {workingStatusModeMap} from './config.js';
import NoData from '@components/NoData.js';
import CustomText from 'IOTRN/src/components/CustomText.js';

const TopStatusView = inject(store => ({
  workingStatus: store.panel.home.workingStatus,
  acChargingProgress: store.panel.home.acChargingProgress,
  standbyChargingProgress: store.panel.home.standbyChargingProgress,
  acChargingRemainingTime: store.panel.home.acChargingRemainingTime,
  batteryOptimizationSwitch:
    store.panel.functionSetting.batteryOptimizationSwitch,
}))(
  ({
    workingStatus,
    acChargingProgress,
    standbyChargingProgress,
    acChargingRemainingTime,
    batteryOptimizationSwitch,
    navHeaderHeight = 80,
    connected = false,
  }) => {
    // console.log("it's top status comp");
    const {t} = useTranslation('all');

    const {hour = 0, min = 0} = acChargingRemainingTime;
    const dcWorkingStatus = workingStatusModeMap[workingStatus];

    const isShowTotalBatteryPanel = ['acCharging', 'standby', 'error'].includes(
      dcWorkingStatus,
    );
    const isAcCharging = dcWorkingStatus === 'acCharging';
    const isDcDischarging = dcWorkingStatus === 'dcDischarging';

    const containerStyle = createContainerStyle(navHeaderHeight);

    const showFinishTime = (isAcCharging && hour > 0) || min > 0;

    return (
      <View
        style={[
          containerStyle.container,
          showFinishTime
            ? styles.acCloseToBottomWithFinish
            : styles.acCloseToBottom,
        ]}>
        {connected ? (
          isShowTotalBatteryPanel ? (
            <View style={containerStyle.batteryContainer}>
              <BatteryPanel
                width={DEVICE_WIDTH}
                status={dcWorkingStatus === 'error' ? 'error' : 'normal'}
                percent={
                  isAcCharging ? acChargingProgress : standbyChargingProgress
                }
                maxPercent={batteryOptimizationSwitch ? 90 : 0}
              />
              {showFinishTime ? (
                <View style={styles.textContainer}>
                  <CustomText style={styles.extraTextPrefix}>
                    {`${t(geti18nText('home_finishIn_textview_text'))}`}
                    <CustomText style={styles.extraText}>
                      <CustomText fontWeight={500} style={styles.extraNumText}>
                        {hour}
                      </CustomText>
                      hr
                      <CustomText fontWeight={500} style={styles.extraNumText}>
                        {' '}
                        {min}
                      </CustomText>
                      min
                    </CustomText>
                  </CustomText>
                </View>
              ) : null}
              {isAcCharging && (
                <View style={styles.acChargingIcon}>
                  <LightningIcon />
                  <CustomText style={styles.chargingText}>AC</CustomText>
                </View>
              )}
            </View>
          ) : isDcDischarging ? (
            <View style={containerStyle.dcChargingContainer}>
              <View style={styles.dcChargingIcon}>
                <LightningIcon />
                <CustomText style={styles.chargingText}>DC-DC</CustomText>
              </View>
            </View>
          ) : null
        ) : (
          <NoData
            style={containerStyle.noDataContainer}
            showImage={false}
            content="--"
            contentStyle={styles.blankText}
          />
        )}
      </View>
    );
  },
);

TopStatusView.propTypes = {
  workingStatus: PropTypes.number,
  acChargingProgress: PropTypes.number,
  dcChargingProgress: PropTypes.number,
  acChargingRemainingTime: PropTypes.object,
  batteryOptimizationSwitch: PropTypes.number,
  connected: PropTypes.bool,
};

const createContainerStyle = navHeaderHeight =>
  CustomStyleSheet.create({
    container: {
      minHeight: navHeaderHeight + 200,
    },
    batteryContainer: {
      paddingTop: navHeaderHeight,
      marginBottom: -25,
    },
    dcChargingContainer: {
      alignItems: 'center',
      height: navHeaderHeight + 200,
    },
    noDataContainer: {
      paddingTop: navHeaderHeight,
      height: navHeaderHeight + 200,
    },
  });

const styles = CustomStyleSheet.create({
  acCloseToBottom: {
    marginBottom: -75,
  },
  acCloseToBottomWithFinish: {
    marginBottom: -50,
  },
  textContainer: {
    position: 'absolute',
    left: 25,
    bottom: 55,
  },
  extraTextPrefix: {
    fontSize: 16,
    color: '#3C3936',
    fontWeight: '400',
  },
  extraText: {
    fontSize: 16,
    color: '#000',
    fontWeight: '400',
  },
  extraNumText: {
    fontSize: 20,
    fontWeight: '500',
  },
  dcChargingIcon: {
    position: 'absolute',
    bottom: 160,
    borderRadius: 18.5,
    backgroundColor: '#D8D8D6',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 13,
    paddingVertical: 6.5,
  },
  acChargingIcon: {
    position: 'absolute',
    bottom: 205,
    right: 55,
    borderRadius: 18.5,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 14,
    paddingVertical: 5,
  },
  chargingText: {
    fontSize: 16,
    fontWeight: '500',
    lineHeight: 19,
    color: '#000000',
    marginLeft: 4,
  },
  blankText: {
    fontSize: 65,
    fontWeight: '500',
    lineHeight: 48,
    letterSpacing: 1.5,
    color: '#000000',
  },
});

export default memo(TopStatusView);
