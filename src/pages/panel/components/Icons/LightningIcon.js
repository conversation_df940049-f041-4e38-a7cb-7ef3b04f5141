import React from 'react';
import Svg, {Path} from 'react-native-svg';

const LightningIcon = ({width = 13, height = 16, ...props}) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 26 32"
    fill="none"
    {...props}>
    <Path
      fill="#145D2F"
      d="M7.664 32c-.281 0-.422 0-.703-.145-.563-.291-.844-1.019-.703-1.6L9.35 18.326H1.477c-.563 0-.985-.29-1.266-.727a1.36 1.36 0 0 1 0-1.455L7.945.727C8.085.291 8.648 0 9.07 0h12.656c.563 0 .984.29 1.266.873.28.582.14 1.163-.141 1.6l-5.906 7.709h7.593c.563 0 1.125.29 1.266.873.281.581.281 1.163-.14 1.6L8.788 31.563c-.281.29-.703.436-1.125.436Z"
    />
  </Svg>
);
export default LightningIcon;
