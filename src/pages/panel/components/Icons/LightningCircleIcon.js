import React from 'react';
import Svg, {G, Circle, Path, Defs} from 'react-native-svg';

const LightningCircleIcon = ({width = 77, height = 77, ...props}) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 154 154"
    fill="none"
    {...props}>
    <G filter="url(#a)">
      <Circle cx={77} cy={62} r={45} fill="#77BC1F" />
    </G>
    <Path fill="#000" d="M57.568 42.568h39.886v39.886H57.568z" opacity={0.01} />
    <Path
      fill="#fff"
      d="M71.196 80.793c-.332 0-.498 0-.83-.167-.665-.332-.998-1.163-.832-1.828l3.656-13.627h-9.306c-.665 0-1.164-.333-1.496-.831a1.51 1.51 0 0 1 0-1.662l9.14-17.617c.167-.498.832-.83 1.33-.83h14.957c.665 0 1.164.332 1.496.996.333.665.166 1.33-.166 1.829l-6.98 8.808h8.974c.665 0 1.33.332 1.496.997.332.665.332 1.33-.166 1.828L72.526 80.294c-.333.332-.831.499-1.33.499Z"
    />
    <Defs />
  </Svg>
);

export default LightningCircleIcon;
