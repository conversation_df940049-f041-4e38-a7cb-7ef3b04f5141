import React from 'react';
import Svg, {G, Circle, Path} from 'react-native-svg';

const CountDownIcon = ({width = 16, height = 16, ...props}) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 32 32"
    fill="none"
    {...props}>
    <G stroke="#000" strokeWidth={3} opacity={0.4}>
      <Circle cx={16} cy={16} r={14.5} />
      <Path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M23.238 17.473h-8v-8"
      />
    </G>
  </Svg>
);

export default CountDownIcon;
