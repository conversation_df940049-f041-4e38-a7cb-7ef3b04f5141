import React from 'react';
import Svg, {Circle, Path} from 'react-native-svg';

const ReadyStatusIcon = ({width = 20, height = 20, props}) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 40 40"
    fill="none"
    {...props}>
    <Circle cx={20} cy={20} r={18} stroke="#fff" strokeWidth={4} />
    <Path
      stroke="#fff"
      strokeWidth={4}
      d="M29.08 14.208 18.203 25.023l-6.467-6.431"
    />
  </Svg>
);

export default ReadyStatusIcon;
