import React from 'react';
import Svg, {G, Path, Circle} from 'react-native-svg';
import {DEVICE_WIDTH} from '@utils/device/index.js';

const DcChargerIcon = ({width = 58, height = 43.5, ...props}) => {
  const ratio = DEVICE_WIDTH / 375;
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={width * ratio}
      height={height * ratio}
      viewBox="0 0 116 87"
      fill="none"
      {...props}>
      <G opacity={0.8}>
        <Path
          fill="#8F9092"
          d="M8.339.758h6.064v17.435H8.339zM101.332.758h6.064v17.435h-6.064z"
        />
        <Path
          fill="#D9D9D9"
          d="M21.945 0 8.339.758V3.79h15.16L21.946 0ZM93.79 0l13.606.758V3.79h-15.16L93.79 0Z"
        />
        <Path
          fill="#9E9FA2"
          d="M11.83 35.491h92.491v47.321H11.83zM22.78 2.151h-7.9L12.907 4.06v21.753h89.264V4.442l-2.37-2.29h-7.109l-1.975 1.907h-65.96L22.78 2.151Z"
        />
        <Path
          fill="#ABABAB"
          d="M7.43 0H1.174L0 1.137v83.764l1.173 1.137h6.258l1.173-1.137V1.137L7.43 0ZM114.827 0h-6.258l-1.173 1.137v83.764l1.173 1.137h6.258L116 84.901V1.137L114.827 0Z"
        />
        <Path fill="#C6C6C6" d="M8.604 25.811h98.944v11.83H8.604z" />
        <Path
          fill="#818286"
          d="M15.853 72.014H8.34v10.755h7.514l1.582-1.537V73.55l-1.582-1.536ZM99.882 72.014h7.514v10.755h-7.514l-1.583-1.537V73.55l1.582-1.536Z"
        />
        <Path
          fill="#8F9092"
          d="m11.83 47.69-3.226-2.965v13.712l3.226 2.965V47.69ZM104.17 47.69l3.226-2.965v13.712l-3.226 2.965V47.69Z"
        />
        <Path
          fill="#999A9F"
          d="M98.423 27.29H20.344l-2.274 2.273v4.549l2.274 2.274h78.079l2.274-2.274v-4.549l-2.274-2.274Z"
        />
      </G>
      <Path
        fill="#4D4E53"
        fillRule="evenodd"
        d="M39.774 24.671h-4.806c-.26 0-.51.096-.704.27l-.517.464h-5.265c-.22 0-.437-.07-.616-.2l-4.518-3.255c-.458-.33-.73-.861-.73-1.426v-9.98c0-.1.02-.197.061-.287l1.743-3.907c.063-.14.149-.27.254-.384l3.345-3.603c.244-.264.581-.424.94-.447l5.195-.333a1.758 1.758 0 0 1 1.557.751l1.097 1.582c.329.473.868.755 1.444.755h1.52-.046 1.52c.576 0 1.115-.282 1.444-.755l1.097-1.582a1.757 1.757 0 0 1 1.557-.751l5.195.333c.36.023.696.183.94.447l3.345 3.603c.***************.254.384l1.743 3.907c.*************.061.287v9.98c0 .565-.272 1.095-.73 1.426l-4.518 3.256c-.18.13-.395.199-.616.199h-5.265l-.517-.464a1.055 1.055 0 0 0-.704-.27h-4.806"
        clipRule="evenodd"
      />
      <Path
        fill="#77BC1F"
        fillRule="evenodd"
        d="M39.472 4.138h-1.294c-.343 0-.665-.167-.863-.448l-.748-1.063a.703.703 0 0 1 .575-1.108h5.218c.569 0 .902.642.575 1.108l-.748 1.063c-.198.281-.52.448-.863.448h-1.852ZM39.704 6.535h-3.78a.474.474 0 0 0-.1.01l-3.774.813a.475.475 0 0 0-.256.15l-3.607 4.088a.356.356 0 0 0 .147.571l1.087.388 5.32 6.907a.475.475 0 0 1-.05.636l-.66.623a.475.475 0 0 1-.503.095l-1.507-.607a.474.474 0 0 1-.204-.157l-6.672-8.977a.356.356 0 0 1 .024-.455l4.966-5.35a.356.356 0 0 1 .18-.105l4.009-.933c.14-.032.287 0 .4.09l1.309 1.027a.95.95 0 0 0 .586.203h3.085-.016 3.084a.95.95 0 0 0 .587-.203l1.308-1.028a.475.475 0 0 1 .4-.089l4.009.933c.**************.18.104l4.966 5.35a.356.356 0 0 1 .025.456l-6.673 8.977a.474.474 0 0 1-.203.157l-1.508.607a.475.475 0 0 1-.503-.095l-.66-.623a.475.475 0 0 1-.05-.636l5.32-6.907 1.087-.388a.356.356 0 0 0 .147-.571l-3.606-4.088a.475.475 0 0 0-.256-.15l-3.775-.812a.474.474 0 0 0-.1-.01h-3.78"
        clipRule="evenodd"
      />
      <Circle cx={40.054} cy={12.888} r={2.532} stroke="#fff" />
      <Path
        fill="#77BC1F"
        d="M22.618 66.983V48.586a2 2 0 0 0-.585-1.414l-1.861-1.861a2 2 0 0 0-1.414-.586h-.962a2 2 0 0 0-2 2v20.258a2 2 0 0 0 2 2h2.822a2 2 0 0 0 2-2ZM93.874 66.983V48.586a2 2 0 0 1 .586-1.414l1.86-1.861a2 2 0 0 1 1.415-.586h.962a2 2 0 0 1 2 2v20.258a2 2 0 0 1-2 2h-2.823a2 2 0 0 1-2-2Z"
      />
      <Path
        fill="#4D4E53"
        fillRule="evenodd"
        d="M76.16 24.67h-4.805c-.26 0-.51.097-.704.27l-.518.464H64.87c-.222 0-.437-.07-.617-.199l-4.518-3.256c-.458-.33-.73-.86-.73-1.425v-9.98c0-.1.021-.197.061-.287L60.81 6.35c.062-.141.148-.27.253-.384l3.345-3.604c.245-.263.581-.423.94-.446l5.196-.334a1.757 1.757 0 0 1 1.556.752l1.098 1.582c.328.473.868.755 1.444.755h1.519-.045 1.52c.575 0 1.114-.282 1.443-.755l1.098-1.582a1.757 1.757 0 0 1 1.556-.752l5.195.334c.36.023.696.183.94.446l3.346 3.604c.**************.253.384l1.743 3.907c.*************.061.286v9.98c0 .566-.271 1.096-.73 1.426l-4.518 3.256c-.179.13-.395.2-.616.2h-5.265l-.517-.465a1.055 1.055 0 0 0-.704-.27h-4.805"
        clipRule="evenodd"
      />
      <Path
        fill="#77BC1F"
        fillRule="evenodd"
        d="M75.858 4.138h-1.294c-.343 0-.665-.167-.862-.448l-.748-1.064a.703.703 0 0 1 .575-1.107H78.746c.57 0 .902.642.575 1.107l-.748 1.064c-.198.28-.52.448-.863.448h-1.852ZM76.09 6.533h-3.78a.475.475 0 0 0-.1.01l-3.774.813a.474.474 0 0 0-.256.15l-3.607 4.088a.356.356 0 0 0 .147.571l1.087.388 5.32 6.907a.475.475 0 0 1-.05.636l-.66.623a.474.474 0 0 1-.503.095l-1.507-.607a.475.475 0 0 1-.204-.157l-6.673-8.977a.357.357 0 0 1 .025-.455l4.966-5.35a.356.356 0 0 1 .18-.105l4.008-.933c.14-.032.288 0 .401.09l1.308 1.027a.95.95 0 0 0 .587.203h3.085-.017 3.085a.95.95 0 0 0 .587-.203l1.308-1.028a.475.475 0 0 1 .4-.089l4.009.933c.***************.18.104l4.966 5.35a.356.356 0 0 1 .025.456L83.96 20.05a.476.476 0 0 1-.204.157l-1.507.607a.474.474 0 0 1-.503-.095l-.66-.623a.475.475 0 0 1-.05-.636l5.32-6.907 1.087-.388a.356.356 0 0 0 .147-.571l-3.606-4.088a.475.475 0 0 0-.256-.15l-3.775-.812a.475.475 0 0 0-.1-.01h-3.78"
        clipRule="evenodd"
      />
      <Circle cx={76.439} cy={12.887} r={2.532} stroke="#fff" />
      <Path
        fill="#4D4E53"
        fillRule="evenodd"
        d="M58.247 73.507H37.554l-2.299 2.298h-7.28l-4.599-4.595V42.497l4.599-4.595h7.28l2.3 2.297 1.532-1.531h5.365l1.15-.766h25.29l1.15.766h5.364l1.533 1.531 2.299-2.297h7.28l4.599 4.594V71.21l-4.599 4.594h-7.28l-2.3-2.297H58.248Z"
        clipRule="evenodd"
      />
      <Path
        fill="#77BC1F"
        fillRule="evenodd"
        d="M26.787 50.032H37.78l3.032-1.138h10.613l2.274 1.138h10.234l2.274-1.517h6.064l4.549 3.79v9.097l-4.549 3.79h-6.064l-2.274-1.137H53.698l-2.274 1.137H40.81l-3.032-1.895H26.41l-2.275-1.895v-9.096l2.653-2.274Zm1.138 1.516H64.31l1.516-1.137h4.548l3.79 3.411v6.444l-3.79 3.032h-4.548L64.31 62.16H27.925l-2.654-2.274v-6.065l2.654-2.274Z"
        clipRule="evenodd"
      />
      <Circle cx={58.246} cy={56.854} r={2.532} stroke="#fff" />
    </Svg>
  );
};

export default DcChargerIcon;
