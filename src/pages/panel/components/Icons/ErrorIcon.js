import React from 'react';
import Svg, {Circle, G, Rect} from 'react-native-svg';
import {DEVICE_WIDTH} from '@utils/device/index.js';

const ErrorIcon = ({width = 29, height = 29, ...props}) => {
  const ratio = DEVICE_WIDTH / 375;
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={width * ratio}
      height={height * ratio}
      viewBox="0 0 58 58"
      fill="none"
      {...props}>
      <Circle cx={29} cy={29} r={22.189} stroke="#DA322B" strokeWidth={4.623} />
      <G fill="#DA322B">
        <Circle cx={29} cy={40.227} r={3} />
        <Rect width={6} height={19} x={26} y={15.182} rx={3} />
      </G>
    </Svg>
  );
};

export default ErrorIcon;
