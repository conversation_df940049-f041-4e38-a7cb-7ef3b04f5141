import React from 'react';
import Svg, {Rect, Path, Mask} from 'react-native-svg';

const PortableBatteryIcon = ({width = 31, height = 31, ...props}) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 62 62"
    fill="none"
    {...props}>
    <Rect
      width={54.706}
      height={5.471}
      x={3.647}
      y={10.941}
      fill="#3E3936"
      rx={1}
    />
    <Rect
      width={54.706}
      height={5.471}
      x={3.647}
      y={51.971}
      fill="#3E3936"
      rx={1}
    />
    <Path
      fill="#3E3936"
      d="M5.471 14.588h2.735v38.294H5.471zM54.706 14.588h2.735v38.294h-2.735z"
    />
    <Rect
      width={6.206}
      height={9.853}
      x={7.382}
      y={5.559}
      stroke="#3E3936"
      strokeWidth={2}
      rx={1}
    />
    <Rect
      width={6.206}
      height={9.853}
      x={49.324}
      y={5.559}
      stroke="#3E3936"
      strokeWidth={2}
      rx={1}
    />
    <Path
      fill="#66BC1F"
      fillRule="evenodd"
      d="M11.987 25.73s3.795-1.964 7.575 0c3.78 1.963 7.925-.491 7.925-.491v9.539h-15.5V25.73ZM35.221 25.73s3.795-1.964 7.575 0c3.78 1.963 7.925-.491 7.925-.491v9.539h-15.5V25.73Z"
      clipRule="evenodd"
    />
    <Mask id="a" fill="#fff">
      <Rect width={20.059} height={19.147} x={10.029} y={18.235} rx={1} />
    </Mask>
    <Rect
      width={20.059}
      height={19.147}
      x={10.029}
      y={18.235}
      stroke="#3E3936"
      strokeWidth={6}
      mask="url(#a)"
      rx={1}
    />
    <Mask id="b" fill="#fff">
      <Rect width={20.059} height={19.147} x={32.824} y={18.235} rx={1} />
    </Mask>
    <Rect
      width={20.059}
      height={19.147}
      x={32.824}
      y={18.235}
      stroke="#3E3936"
      strokeWidth={6}
      mask="url(#b)"
      rx={1}
    />
  </Svg>
);

export default PortableBatteryIcon;
