import React from 'react';
import Svg, {G, Path} from 'react-native-svg';
import {DEVICE_WIDTH} from '@utils/device/index.js';

const ThreePortAdaptorIcon = ({width = 94.5, height = 41.5, ...props}) => {
  const ratio = DEVICE_WIDTH / 375;
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={width * ratio}
      height={height * ratio}
      viewBox="0 0 189 83"
      fill="none"
      {...props}>
      <G opacity={0.9}>
        <Path fill="#979898" d="M14.955 69.54h156.674v13.459H14.955z" />
        <Path fill="#B4B4B5" d="M14.955 74.025h156.674v5.234H14.955z" />
        <Path
          fill="#848486"
          d="m9.347 12.338 7.477-10.095L26.92 4.486V29.91l-11.964.747-5.608 2.618V12.338ZM179.085 12.338l-7.477-10.095-10.095 2.243V29.91l11.964.747 5.608 2.618V12.338Z"
        />
        <Path
          fill="#979797"
          d="M0 4.486h4.486v26.919H0zM188.432 4.486h-4.486v26.92h4.486z"
        />
        <Path
          fill="#979797"
          d="M.748 4.486H13.46v8.973H.748zM187.684 4.486h-12.712v8.973h12.712z"
        />
        <Path
          fill="#848486"
          d="M16.45 79.635 9.72 78.14 13.46 83h6.73l-3.74-3.365ZM171.594 79.27l7.116-1.13-3.738 4.86h-6.73l3.352-3.73Z"
        />
        <Path
          fill="#979797"
          d="M17.572 26.171h6.73v5.982h-6.73l-2.617 1.87v-6.356l2.617-1.496Z"
        />
        <Path
          fill="#686868"
          d="m36.64 19.069-1.87-1.496V33.65h1.87V19.069ZM50.846 19.069l1.87-1.496V33.65h-1.87V19.069Z"
        />
        <Path
          fill="#848486"
          d="M9.721 27.667h5.234v50.099H9.721zM178.711 27.667h-5.234v50.099h5.234z"
        />
        <Path fill="#C3C3C4" d="M36.64 18.693h14.207V69.54H36.64z" />
        <Path fill="#77BC1F" d="M38.883 18.693h9.721v4.486h-9.721z" />
        <Path fill="#686868" d="M36.64 35.144h2.991V69.54H36.64z" />
        <Path fill="#4D4E53" d="M32.901 26.171h21.685v9.721H32.901z" />
        <Path fill="#686868" d="M47.855 35.144h2.991V69.54h-2.991z" />
        <Path
          fill="#ABABAB"
          d="M12.338 17.572 10.094 3.74l7.104.748v11.59l-4.86 1.495ZM176.094 17.572l2.243-13.833-7.104.748v11.59l4.861 1.495Z"
        />
        <Path
          fill="#919090"
          d="M9.72 78.14v-7.478l7.104 3.739v5.234L9.721 78.14Z"
        />
        <Path
          fill="#ABABAB"
          d="M178.711 78.14v-7.478l-7.104 3.739v5.234l7.104-1.495Z"
        />
        <Path fill="#77BC1F" d="M38.509 29.91h10.468v7.477H38.509z" />
        <Path fill="#6EAA21" d="M39.631 26.92h8.225v2.991h-8.225z" />
        <Path
          fill="#6C6D6F"
          d="m14.581 63.184 9.347-4.487v10.877h-4.113l-1.495.713-1.496 4.113-7.103-3.74 4.86-7.476ZM173.851 63.613l-9.347-4.486v10.468h4.112l1.496 1.122 1.495 4.113 7.104-3.74-4.86-7.477Z"
        />
        <Path
          fill="#CECECE"
          d="M51.968 5.233h-16.45l-5.608 3.74-1.122 7.103h30.284l-.748-7.104-6.356-3.739Z"
        />
        <Path
          fill="#77BC1F"
          d="M47.108 6.355h-5.234l-2.243 1.87V9.72h9.72V8.225l-2.243-1.87Z"
        />
        <Path
          fill="#686868"
          d="m101.32 19.069 1.869-1.496V33.65h-1.869V19.069ZM87.112 19.069l-1.87-1.496V33.65h1.87V19.069Z"
        />
        <Path fill="#C3C3C4" d="M101.319 18.693H87.112V69.54h14.207z" />
        <Path fill="#77BC1F" d="M99.076 18.693h-9.72v4.486h9.72z" />
        <Path fill="#686868" d="M101.319 35.144h-2.991V69.54h2.991z" />
        <Path fill="#4D4E53" d="M105.058 26.171H83.373v9.721h21.685z" />
        <Path fill="#686868" d="M90.103 35.144h-2.99V69.54h2.99z" />
        <Path fill="#77BC1F" d="M99.45 29.91H88.982v7.477H99.45z" />
        <Path fill="#6EAA21" d="M98.328 26.92h-8.225v2.99h8.225z" />
        <Path
          fill="#CECECE"
          d="M85.99 5.233h16.451l5.608 3.74 1.122 7.103H78.887l.748-7.104 6.356-3.739Z"
        />
        <Path
          fill="#77BC1F"
          d="M91.599 6.355h5.234l2.243 1.87V9.72h-9.72V8.225l2.243-1.87Z"
        />
        <Path
          fill="#686868"
          d="m152.914 19.069 1.869-1.496V33.65h-1.869V19.069ZM138.706 19.069l-1.869-1.496V33.65h1.869V19.069Z"
        />
        <Path fill="#C3C3C4" d="M152.914 18.693h-14.207V69.54h14.207z" />
        <Path fill="#77BC1F" d="M150.671 18.693h-9.721v4.486h9.721z" />
        <Path fill="#4D4E53" d="M23.928 13.457h40.378v5.982H23.928z" />
        <Path
          fill="#4D4E53"
          d="m22.806 9.718 7.104-.747-1.122 7.103h-4.113l-.747 1.496v5.234l-2.617-1.87v-4.86l1.495-6.356ZM165.626 9.718l-7.104-.747 1.122 7.103h4.112l.748 1.496v5.234l2.617-1.87v-4.86l-1.495-6.356ZM57.576 5.98h9.721v63.558h-9.721zM108.423 5.98h9.721v63.558h-9.721z"
        />
        <Path fill="#4D4E53" d="M114.031 13.457H73.653v5.982h40.378z" />
        <Path
          fill="#4D4E53"
          d="M80.383 5.98h-9.721v63.558h9.72zM165.625 13.457h-40.378v5.982h40.378z"
        />
        <Path fill="#4D4E53" d="M131.977 5.98h-9.721v63.558h9.721z" />
        <Path
          fill="#9E9E9E"
          d="M34.77 17.57H23.928v20.937l-1.122 1.496V50.47l1.122 2.244v16.824h12.711V35.516H34.77V17.571ZM52.716 17.57H63.56v20.937l1.121 1.496V50.47l-1.121 2.244v16.824H50.847V35.516h1.87V17.571ZM103.189 17.57h10.842v20.937l1.122 1.496V50.47l-1.122 2.244v16.824H101.32V35.516h1.869V17.571ZM85.243 17.57H74.401v20.937l-1.122 1.496V50.47l1.122 2.244v16.824h12.712V35.516h-1.87V17.571ZM154.783 17.57h10.842v20.937l1.122 1.496V50.47l-1.122 2.244v16.824h-12.711V35.516h1.869V17.571ZM136.837 17.57h-10.842v20.937l-1.121 1.496V50.47l1.121 2.244v16.824h12.712V35.516h-1.87V17.571Z"
        />
        <Path fill="#686868" d="M152.914 35.144h-2.991V69.54h2.991z" />
        <Path fill="#4D4E53" d="M156.652 26.171h-21.685v9.721h21.685z" />
        <Path fill="#686868" d="M141.697 35.144h-2.991V69.54h2.991z" />
        <Path fill="#77BC1F" d="M151.044 29.91h-10.468v7.477h10.468z" />
        <Path fill="#6EAA21" d="M149.923 26.92h-8.225v2.99h8.225z" />
        <Path
          fill="#CECECE"
          d="M137.585 5.233h16.45l5.609 3.74 1.121 7.103h-30.284l.748-7.104 6.356-3.739Z"
        />
        <Path
          fill="#77BC1F"
          d="M143.193 6.355h5.235l2.243 1.87V9.72h-9.721V8.225l2.243-1.87Z"
        />
        <Path
          fill="#CECECE"
          d="m10.095 3.739 6.73.747 17.571-.747L31.78 0H14.581L8.225 1.87l1.87 1.869ZM178.337 3.739l-6.73.747-17.572-.747L156.653 0h17.198l6.356 1.87-1.87 1.869Z"
        />
        <Path
          fill="#B4B4B4"
          d="M32.9 1.495H18.32l5.234 8.225h6.356l5.608-3.739h16.45l5.982 3.365h21.685l6.356-3.365h17.198l5.608 3.365h21.685l5.982-3.365h16.824l6.356 3.365h5.608l5.234-8.225h-14.581l-2.617 3.365h-23.554l-2.243 1.495h-16.077l-1.869-1.495-4.113-2.243H83l-4.113 2.243-1.87 1.495H61.316l-2.243-1.495H35.144l-2.243-2.991Z"
        />
        <Path
          fill="#6C6D6F"
          d="m17.198 4.923 1.18-3.427 6.298 8.225H19.56l-2.362-4.798ZM171.233 4.923l-1.18-3.427-6.297 8.225h5.116l2.361-4.798Z"
        />
        <Path
          fill="#8B8B8B"
          d="M74.026 8.974H64.68l1.495 6.057v54.51h5.608v-54.51l2.243-6.057ZM124.874 8.974h-9.347l1.495 6.057v54.51h5.608v-54.51l2.244-6.057Z"
        />
      </G>
    </Svg>
  );
};

export default ThreePortAdaptorIcon;
