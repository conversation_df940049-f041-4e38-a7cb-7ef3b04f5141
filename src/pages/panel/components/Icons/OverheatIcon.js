import React from 'react';
import Svg, {<PERSON>, Path, Rect, Circle} from 'react-native-svg';
import {DEVICE_WIDTH} from '@utils/device/index.js';

const OverheatIcon = ({width = 29, height = 29, ...props}) => {
  const ratio = DEVICE_WIDTH / 375;
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={width * ratio}
      height={height * ratio}
      viewBox="0 0 58 58"
      fill="none"
      {...props}>
      <Mask id="a" fill="#fff">
        <Path
          fillRule="evenodd"
          d="M29 3.625a8.156 8.156 0 0 0-8.156 8.156v16.104c-3.83 2.61-6.344 7.006-6.344 11.99 0 8.008 6.492 14.5 14.5 14.5s14.5-6.492 14.5-14.5c0-4.984-2.514-9.38-6.344-11.99V11.78A8.156 8.156 0 0 0 29 3.625Z"
          clipRule="evenodd"
        />
      </Mask>
      <Path
        fill="#FF8A05"
        d="m20.844 27.885 1.126 1.652.874-.595v-1.057h-2Zm16.312 0h-2v1.057l.874.595 1.126-1.652ZM22.844 11.78c0-3.4 2.756-6.156 6.156-6.156v-4c-5.61 0-10.156 4.547-10.156 10.156h4Zm0 16.104V11.78h-4v16.104h4ZM16.5 39.875c0-4.294 2.164-8.084 5.47-10.338l-2.253-3.305C15.365 29.2 12.5 34.202 12.5 39.875h4Zm12.5 12.5c-6.904 0-12.5-5.596-12.5-12.5h-4c0 9.113 7.387 16.5 16.5 16.5v-4Zm12.5-12.5c0 6.904-5.596 12.5-12.5 12.5v4c9.113 0 16.5-7.387 16.5-16.5h-4Zm-5.47-10.338a12.485 12.485 0 0 1 5.47 10.338h4c0-5.673-2.865-10.676-7.217-13.643l-2.253 3.305Zm-.874-17.756v16.104h4V11.78h-4ZM29 5.625c3.4 0 6.156 2.756 6.156 6.156h4C39.156 6.172 34.61 1.625 29 1.625v4Z"
        mask="url(#a)"
      />
      <Rect
        width={4.531}
        height={22.656}
        x={26.734}
        y={12.688}
        fill="#FF8A05"
        rx={2.266}
      />
      <Circle cx={29} cy={39.875} r={8.156} fill="#FF8A05" />
    </Svg>
  );
};

export default OverheatIcon;
