import React from 'react';
import Svg, {Mask, Path} from 'react-native-svg';

const BatteryOptimizationIcon = ({width = 34, height = 34, ...props}) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 68 68"
    fill="none"
    {...props}>
    <Mask id="a" fill="#fff">
      <Path
        fillRule="evenodd"
        d="M17.127 5.829h14.37v4.857h-14.37V5.829Z"
        clipRule="evenodd"
      />
    </Mask>
    <Path
      fill="#3C3936"
      d="M17.127 5.829v-5h-5v5h5Zm14.37 0h5v-5h-5v5Zm0 4.857v5h5v-5h-5Zm-14.37 0h-5v5h5v-5Zm0 .143h14.37v-10h-14.37v10Zm9.37-5v4.857h10V5.829h-10Zm5-.143h-14.37v10h14.37v-10Zm-9.37 5V5.829h-10v4.857h10Z"
      mask="url(#a)"
    />
    <Path
      stroke="#3C3936"
      strokeWidth={5}
      d="M8.329 12.214H38.3v47.457H8.329z"
    />
    <Path
      fill="#fff"
      fillRule="evenodd"
      stroke="#3C3936"
      strokeWidth={5}
      d="M25.257 42.654V30.442c4.988 0 9.923-1.567 14.805-4.7 4.882 3.133 9.817 4.7 14.805 4.7v12.212c0 8.896-4.935 15.31-14.805 17.089-9.87-1.78-14.805-8.193-14.805-17.089Z"
      clipRule="evenodd"
    />
    <Path
      stroke="#77BC1F"
      strokeLinecap="square"
      strokeWidth={5}
      d="M34.72 42.84h10.685M40.062 37.497v10.686"
    />
  </Svg>
);

export default BatteryOptimizationIcon;
