import React from 'react';
import Svg, {Path} from 'react-native-svg';

const CustomizeSequenceIcon = ({width = 34, height = 34, ...props}) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 68 68"
    fill="none"
    {...props}>
    <Path fill="#fff" d="M38 31h30v37H38z" />
    <Path
      stroke="#3B3936"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={5}
      d="M57.866 44.332A25.915 25.915 0 0 0 60 34C60 19.64 48.36 8 34 8S8 19.64 8 34s11.64 26 26 26a25.88 25.88 0 0 0 13.002-3.48"
    />
    <Path
      fill="#3E3936"
      d="m53.507 33.24-4.155 2.78 1.39 2.078 4.914 7.345a2.5 2.5 0 0 0 4.155-2.78l-4.913-7.345-1.39-2.078Z"
    />
    <Path
      fill="#66BC1F"
      d="M28.78 50c-.25 0-.374 0-.622-.127-.498-.255-.747-.891-.622-1.4l2.736-10.437h-6.966c-.497 0-.87-.254-1.12-.636a1.178 1.178 0 0 1 0-1.273l6.842-13.49c.125-.383.622-.637.995-.637H41.22c.498 0 .87.255 1.12.764.248.509.124 1.018-.125 1.4l-5.224 6.745h6.717c.498 0 .995.255 1.12.764.248.509.248 1.018-.125 1.4L29.775 49.618c-.25.255-.622.382-.995.382Z"
    />
    <Path
      stroke="#3B3936"
      strokeLinecap="round"
      strokeWidth={5}
      d="M58.102 43.77A25.925 25.925 0 0 0 60 34C60 19.64 48.36 8 34 8S8 19.64 8 34s11.64 26 26 26a25.88 25.88 0 0 0 14.601-4.484"
    />
  </Svg>
);

export default CustomizeSequenceIcon;
