import React, {useState, useEffect} from 'react';
import {View, StyleSheet} from 'react-native';
import AgileImage from '@components/AgileImage';
import Popup from '@components/Popup';
import {WhiteSpace} from '@/components/WhiteSpace';
import {Button} from 'react-native-elements';
import {geti18nText} from '@i18n/config';
import {useTranslation} from 'react-i18next';
import {mobile} from '@cvn/rn-panel-kit';
import {JumpUtils} from '@cvn/rn-panel-kit/src/utils';
import {isIphoneX, StatusBarHeight} from '@utils/device/index.js';
import {getImageUrl} from '@utils/image';
import CustomText, {FONT_WEIGHTS} from 'IOTRN/src/components/CustomText';

/**
 * POP UP window when offline last for at least for one day
 */
const OfflinePopUp = ({
  isAvailable = false,
  deviceName = '',
  offlineDays = 0,
  panel = {},
  onPressMore = () => {},
}) => {
  const {t} = useTranslation('all');
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // 关闭强制升级弹窗，避免升级弹窗和 Prompt Modal 同时存在
    if (isAvailable && mobile.closeSimpleConfirmDialog) {
      mobile.closeSimpleConfirmDialog();
    }
    setIsVisible(isAvailable);
  }, [isAvailable]);

  const renderOfflineDaysTitle = (deviceName_1, offlineDays_1) => {
    if (offlineDays === 1) {
      return t(geti18nText('home_offlineSingleDayTitle_textview_text'), {
        deviceName: deviceName_1,
        day: offlineDays_1,
      });
    } else {
      return t(geti18nText('home_offlineDaysTitle_textview_text'), {
        deviceName: deviceName_1,
        days: offlineDays_1,
      });
    }
  };

  return (
    <Popup visible={isVisible} style={{top: -StatusBarHeight}}>
      <View style={styles.container}>
        <View
          style={[
            styles.contentContainer,
            {
              bottom: isIphoneX ? -34 : 0,
            },
          ]}>
          <View style={styles.popupMainContainer}>
            <View style={{width: 120}}>
              <AgileImage
                source={{uri: getImageUrl('pgx1600h_icon_device')}}
                style={styles.popupMain}
              />
              <AgileImage
                source={{uri: getImageUrl('common_wifioffline')}}
                style={styles.popupOffline}
              />
            </View>
          </View>
          <View styles={styles.popupTextContainer}>
            <CustomText
              fontWeight={FONT_WEIGHTS.MEDIUM}
              style={styles.popupTitle}>
              {renderOfflineDaysTitle(deviceName, offlineDays)}
            </CustomText>
            <CustomText
              fontWeight={FONT_WEIGHTS.MEDIUM}
              style={styles.popupDesc}>
              {t(geti18nText('home_offlineDesc_textview_text'))}
            </CustomText>
          </View>
          <View style={styles.buttonsContainer}>
            <Button
              onPress={() => {
                setIsVisible(false);
              }}
              type="outline"
              title={t(geti18nText('home_offlineCancel_button_text'))}
              containerStyle={styles.buttonContaner}
              buttonStyle={[styles.button, styles.cancellButton]}
              titleStyle={[styles.title, styles.cancellTitle]}
            />
            <Button
              onPress={() => {
                onPressMore();
                setIsVisible(false);
                const route = 'ChervonIot://Fleet/NetworkConfig/moreSolutions';
                const {deviceId, productId} = panel;
                const params = {
                  deviceId,
                  productId,
                };
                JumpUtils.jumpTo(route, params);
              }}
              type="solid"
              title={t(geti18nText('home_offlineConfirm_button_text'))}
              containerStyle={styles.buttonContaner}
              buttonStyle={[styles.button, styles.confirmButton]}
              titleStyle={styles.title}
            />
          </View>
          <WhiteSpace size={isIphoneX ? 34 : 0} />
        </View>
      </View>
    </Popup>
  );
};

const styles = StyleSheet.create({
  container: {
    height: '100%',
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  contentContainer: {
    left: 0,
    right: 0,
    bottom: 0,
    position: 'absolute',
    backgroundColor: '#fff',
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  popupMainContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    marginTop: 15,
  },
  popupMain: {
    width: 120,
    height: 120,
  },
  popupOffline: {
    width: 40,
    height: 40,
    position: 'absolute',
    top: 0,
    right: 0,
  },
  popupTextContainer: {
    textAlign: 'center',
  },
  popupTitle: {
    textAlign: 'center',
    color: '#000',
    fontSize: 17,
    fontWeight: '500',
    marginBottom: 12,
  },
  popupDesc: {
    textAlign: 'center',
    color: '#000',
    fontSize: 15,
    fontWeight: '500',
    marginBottom: 39,
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  buttonContaner: {
    width: '48.5%',
  },
  button: {
    paddingVertical: 15,
  },
  cancellButton: {
    borderColor: '#77BC1F',
  },
  confirmButton: {
    backgroundColor: '#77BC1F',
  },
  title: {
    fontSize: 18,
    lineHeight: 21.5,
    fontFamily: 'WorkSans-Medium', // 字体加粗
  },
  cancellTitle: {
    color: '#77BC1F',
  },
});

export default OfflinePopUp;
