import React, {memo} from 'react';
import {inject} from 'mobx-react';
import {View} from 'react-native';
import Cell from '@components/Cell';
import {useTranslation} from 'react-i18next';
import {geti18nText} from '@i18n/config';
import CustomStyleSheet from '@utils/style/index.js';

const updateImgBase64 =
  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACwAAAAsCAYAAAAehFoBAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAASCSURBVHgB7Vg/cxpXEN99h4zkFKHMjCcK+gSWCsnIQTPHJH1wl074EwhXSQfq4kpWl87oE0SuUmV0iTHCotCV6XRS4jqksIIId5vdk2DujgPeAbEbfjMSsPd232/27ds/B7DAAgt8UCDMEaZpZrrdblYpL9OXpVLLtmVZbZgTZiZs5nLZnvJ2kaBEANkRu9iIaBkuHlrNpgMzYGrCQtRVVCGiUhI9Jl4zPNyflrgBU2Bne6voofczf81BcqwTUvGLzx9cXv357ndIiMSE84+3KvzxI/tqOXYBHz//YyLo8HeJ3c9iVkmMf7u6+gCu/nj3KyRAopDIb2/uscqLGDMWkndkLH9yHL1gchHdzvsioKoQUDaqqZQq/fameQTzJuxfLnTPWSUTlBPgszenb1/o2Mjntqq8YyUibqdIbejGtAJNcCb4KUoWSBV0yQrqzbOq6AjJgDjTQ3qpa0OLsFwyduV6UCaerTebFiSE6LDuflhKZj6XM3X0tQgT4m7wt6SmJJ6N4lYXrZAQqaKjO5Gwaa5ngKgYlLlgHMKsoGEvywWdpDby0olyr3u9x4ZNMRZ4ZNdPzzZgDuBQu4hURxvV+Iqo4ojubG8e9G6u/+JArUbISjwkypvjwGnuVUS0Th6Ve+hd7Dx+9FIyE4wj7Jfbm+tzvhRlGLUJgQNzAhN2Rj7jku+idxIlPSDsk+UFIxuYW7TVmE2SQoFyIJziQhAu7O3zIOlBDMfEkxy/zR49Wkqv1JK0iNWTbKZacBK1lL7DwDXjKiKTdIz0/Q3h4BP2+wM/XkPLDuunb8uQEEL2H5dOOE3Zz7+6egpTILYiIuzXG2dVlLTl3tw7D3t3RrJwV2SQalOT3t6UXL0XELVT6ftryu0sFYNkkbusVHqlCgkxRFZAWPrul1XtshtEKv1vFSIl3O2+LyrCcMllxlbSkSaWbB9TkrYsu81cQgWKQK1zllAPQ0IPtVu9iWQHRqcjzWOXHbYDD7W7tThokR1slpw0GWropIcII3oT67kgEdkBg+ljug+FQJdhm3oEmKxsHI1/J7KsPSTzSWcPQAPk9iItLfytPPBCccJxs6tjDA18FiFjewqfRJa1uZkphNbxd1Sg1e1xJdwL/UY8VkvpXg0C6UNS3Jfbjybm4B8KjhMgY68YWDBiymxonU8WCyKbZF+KWbTyGh5YStIHl7tQ18RhcpCEtJAdV4r765KQjVZeGRqk5fRLs1S73s29C7gdv0OLpFlvNBo2aOD7k2yWXLoIiJznX1+ugSbyeR6TXJk8wi2tFDODsDAg7C+WmYq7tXhTxKeAsR7klyKv6o1WWYewlFu2802sHaDM0JB7B69HG41Wy3daqi+U4XAnl3tK6MWkHcxQxPuDJ6A+BU3grZ3sqKcxaPOU/aTRag5OOJSHXzebNX5HsCZHAB8daMn7iuhknoouu5ul1nZymyWeluX4TBjh3f8Bbb43x9IejHqFkBql+brZqvGH/EHcbDVAenkQ2x3ecJkvR/+3S+E0x014GTqd6ihTs76KXWCBBRYA+A/9kRNJvO1QfgAAAABJRU5ErkJggg==';

const UpdateItem = inject(store => ({
  panelActions: store.panelActions,
  showRed: store.panel.showRed,
}))(({panelActions, showRed}) => {
  const {t} = useTranslation('all');
  const item = {
    key: 'updateItem',
    title: t(geti18nText('home_upgradeAvailable_textview_text')),
  };

  return showRed ? (
    <View style={styles.container}>
      <Cell
        key={item.key}
        title={item.title}
        iconShow
        isShowShadow
        leftIconStyle={styles.leftIcon}
        leftIconSource={updateImgBase64}
        itemStyle={styles.itemStyle}
        containerStyle={styles.containerStyle}
        titleStyle={styles.titleStyle}
        onPress={() => {
          panelActions.jumpToOtaUpdate();
        }}
      />
    </View>
  ) : null;
});

const styles = CustomStyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginBottom: 13,
  },
  containerStyle: {
    paddingHorizontal: 15,
    borderRadius: 10,
  },
  itemStyle: {
    paddingVertical: 13,
  },
  leftIcon: {
    width: 22,
    height: 22,
    marginRight: 16,
  },
  titleStyle: {
    fontWeight: '500',
    flexWrap: 'wrap',
    marginRight: 33,
  },
});

export default memo(UpdateItem);
