import React from 'react';
import {View} from 'react-native';
import {inject} from 'mobx-react';
import PropTypes from 'prop-types';
import CustomStyleSheet from '@utils/style/index.js';
import {getImageUrl} from '@utils/image.js';
import {useTranslation} from 'react-i18next';
import PressableItem from '@components/PressableItem';
import {geti18nText} from '@i18n/config';

const ControlArea = ({isBleConnected, panel = {}}) => {
  /**
   * @type {'NA'|'EU'}
   */
  return (
    <View style={styles.container}>
      <UpgradeItem isBleConnected={isBleConnected} />
      {/* <MoreCardItem /> */}
    </View>
  );
};

ControlArea.propTypes = {
  isBleConnected: PropTypes.bool,
  panel: PropTypes.object,
};

const UpgradeItem = inject(store => ({
  panelActions: store.panelActions,
  showRed: store.panel.showRed,
  otaVersion: store.panel.otaVersion,
  currentVersion: store.panel.deviceDetail?.version,
}))(
  ({
    isBleConnected,
    panelActions,
    showRed = false,
    otaVersion,
    currentVersion,
  }) => {
    const version = showRed ? otaVersion : currentVersion;
    const {t} = useTranslation('all');
    const handleUpgrade = () => {
      panelActions.jumpToOtaUpdate();
    };
    const isInteractive = isBleConnected;
    const versionMark = isInteractive
      ? `${t(geti18nText('home_otaVersion_textview_text'))}: ${version || '--'}`
      : '--';
    return (
      <PressableItem
        icon={{
          uri: getImageUrl('common_icon_otaupdate'),
        }}
        content={
          isInteractive
            ? showRed
              ? t(geti18nText('home_upgradeAvailable_textview_text'))
              : t(geti18nText('home_upToDate_textview_text'))
            : t(geti18nText('home_firmwareVersion_textview_text'))
        }
        extra={versionMark}
        onPress={handleUpgrade}
        isShowRedDot={isInteractive ? showRed : false}
        isInteractive={isInteractive}
      />
    );
  },
);

UpgradeItem.propTypes = {
  isBleConnected: PropTypes.bool,
  panelActions: PropTypes.shape({
    jumpToOtaUpdate: PropTypes.func.isRequired,
  }), // Assuming panelActions has a method called jumpToOtaUpdate
  showRed: PropTypes.bool,
  otaVersion: PropTypes.string,
  currentVersion: PropTypes.string,
};

const ManualItem = inject(store => ({
  jumpToProductIntro: store.panelActions.jumpToProductIntro,
}))(({jumpToProductIntro = () => {}}) => {
  const handleManual = () => {
    jumpToProductIntro();
  };
  const {t} = useTranslation('all');
  return (
    <PressableItem
      icon={{
        uri: getImageUrl('common_icon_usermanual'),
      }}
      content={t(geti18nText('home_manual_textview_text'))}
      onPress={handleManual}
      isInteractive={true}
    />
  );
});

ManualItem.propTypes = {
  jumpToProductIntro: PropTypes.func,
};

const styles = CustomStyleSheet.create({
  container: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
    paddingHorizontal: 14,
    marginBottom: 13,
    color: '#000000',
  },
  moreCardContainer: {
    backgroundColor: '#FAFAFA',
  },
  moreCardContentText: {
    color: '#888684',
    opacity: 0.6,
  },
});

export default ControlArea;
