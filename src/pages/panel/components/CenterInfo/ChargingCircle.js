import React from 'react';
import {View, StyleSheet} from 'react-native';
import Svg, {
  G,
  Circle,
  Defs,
  RadialGradient,
  Stop,
  Path,
} from 'react-native-svg';
import {DEVICE_WIDTH} from '@utils/device/index.js';

function createCircularPath(percentage, radius = 190) {
  const fullDegree = Math.PI * 2;
  const currentDegree = (percentage / 100) * fullDegree;

  let path = 'M 296,106';

  if (currentDegree <= Math.PI) {
    path += ` a ${radius},${radius} 0 0,1 ${radius * Math.sin(currentDegree)},${
      radius - radius * Math.cos(currentDegree)
    }`;
  } else if (currentDegree < 2 * Math.PI) {
    path += ` a ${radius},${radius} 0 1,1 ${radius * Math.sin(currentDegree)},${
      radius - radius * Math.cos(currentDegree)
    }`;
  } else if (currentDegree === 2 * Math.PI) {
    path += ` a ${radius},${radius} 0 1,1 0,0`;
  }

  return path;
}

const BATTERY_COLOR_MAP = {
  discharging: '#77BC1F',
  overheat: '#FF8A05',
  error: '#DA322B',
};

const ChargingCircle = ({
  width = 296,
  height = 296,
  percent = 75,
  type = 'discharging',
  children,
  ...props
}) => {
  const ratio = DEVICE_WIDTH / 375;
  const color = BATTERY_COLOR_MAP[type] || '#77BC1F';
  return (
    <View style={styles.container}>
      <Svg
        xmlns="http://www.w3.org/2000/svg"
        width={width * ratio}
        height={height * ratio}
        viewBox="0 0 592 592"
        fill="none"
        {...props}>
        <Circle
          cx={296}
          cy={296}
          r={190}
          stroke="#3E3936"
          strokeOpacity={0.062}
          strokeWidth={28}
        />
        <G filter="url(#a)" opacity={0.4}>
          <Circle cx={296} cy={296} r={146} fill="#fff" />
        </G>
        {percent >= 100 ? (
          <Circle cx={296} cy={296} r={190} stroke={color} strokeWidth={28} />
        ) : percent > 0 ? (
          <Path
            stroke="url(#b)"
            strokeLinecap="square"
            strokeWidth={28}
            // d="M486 296c0 104.934-85.066 190-190 190s-190-85.066-190-190 85.066-190 190-190"
            d={createCircularPath(percent)}
          />
        ) : null}
        <Defs>
          <RadialGradient
            id="b"
            cx={0}
            cy={0}
            r={1}
            gradientTransform="rotate(-90 296 0) scale(190)"
            gradientUnits="userSpaceOnUse">
            <Stop offset={0.753} stopColor={color} stopOpacity={0} />
            <Stop offset={0.998} stopColor={color} />
          </RadialGradient>
        </Defs>
      </Svg>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ChargingCircle;
