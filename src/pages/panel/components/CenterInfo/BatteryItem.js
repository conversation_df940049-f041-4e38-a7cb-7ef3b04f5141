import React from 'react';
import {View, Text} from 'react-native';
import BatteryStatus from './BatteryStatus';
import {CountDownIcon} from '../Icons';
import {subDevicesBatteryWorkingStatusModeMap} from '../config.js';
import CustomStyleSheet from '@utils/style/index.js';
import CustomText from 'IOTRN/src/components/CustomText';

const BatteryItem = ({
  size = 'normal',
  status = '',
  value = 0,
  unit = '%',
  desc = '',
  showExtra = false,
  dcChargingRemainingTime = '--',
  ...props
}) => {
  return (
    <View
      style={[
        styles.batteryItem,
        size === 'small'
          ? styles.smallSizeContainer
          : styles.largeSizeContainer,
      ]}
      {...props}>
      <View style={[styles.leftPart, size === 'small' && {marginBottom: 10}]}>
        <BatteryStatus size={size} status={status} />
      </View>
      {subDevicesBatteryWorkingStatusModeMap[status] !== 'empty' && (
        <>
          <View style={styles.rightPart}>
            <Text>
              <CustomText fontWeight={600} style={styles.value}>
                {value}
              </CustomText>
              <CustomText fontWeight={600} style={styles.unit}>
                {unit}
              </CustomText>
            </Text>
            <CustomText style={styles.desc}>{desc}</CustomText>
          </View>
          {size === 'small' && showExtra && (
            <View style={styles.extra}>
              <CountDownIcon style={{marginRight: 4}} />
              <CustomText style={styles.extraText}>
                {dcChargingRemainingTime}
              </CustomText>
            </View>
          )}
        </>
      )}
    </View>
  );
};

export default BatteryItem;

const styles = CustomStyleSheet.create({
  batteryItem: {
    borderRadius: 10,
    backgroundColor: '#fff',
    flexDirection: 'row',
  },
  smallSizeContainer: {
    flexDirection: 'column',
    padding: 16,
    width: '48.3%',
    shadowOffset: {width: 0, height: 0},
    shadowRadius: 9.5,
    shadowColor: '#000',
    shadowOpacity: 0.05,
  },
  largeSizeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 24,
  },
  leftPart: {
    flexDirection: 'row',
    alignItems: 'center',
    flexShrink: 1,
  },
  rightPart: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  value: {
    color: '#000000',
    fontSize: 42,
    fontWeight: '600',
  },
  unit: {
    color: '#000000',
    fontSize: 20,
    fontWeight: '600',
  },
  desc: {
    borderRadius: 10,
    backgroundColor: '#D8D8D6',
    color: '#3C3936',
    fontSize: 12,
    fontWeight: '400',
    paddingHorizontal: 10,
    paddingVertical: 3,
    overflow: 'hidden',
    marginLeft: 10,
  },
  extra: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    alignItems: 'center',
    marginTop: 9,
  },
  extraText: {
    fontSize: 14,
    lineHeight: 16.5,
    fontWeight: '400',
    color: '#3C3936',
  },
});
