import React from 'react';
import {View} from 'react-native';
import BatteryItem from './BatteryItem';
import NoData from '@components/NoData.js';
import {Divider} from 'react-native-elements';
import CustomStyleSheet from '@utils/style/index.js';
import CustomText from 'IOTRN/src/components/CustomText';

const BatteryList = ({list = [], connected = false, lowBatteryText = ''}) => {
  return (
    <>
      {list.map(item => {
        return (
          <View key={item.key} style={styles.itemBox}>
            {item.showTitle && (
              <View style={styles.header}>
                {/* Fleet UI设计上面去除了这个图标 */}
                {/* {item.icon} */}
                <CustomText fontWeight={600} style={styles.headerTitle}>
                  {item.title}
                </CustomText>
                {item.subTitle && (
                  <>
                    <Divider
                      orientation="vertical"
                      style={{
                        marginHorizontal: 7,
                        marginVertical: 10.5,
                      }}
                    />
                    <CustomText style={styles.headerSubTitle}>
                      {item.subTitle}
                    </CustomText>
                  </>
                )}
              </View>
            )}
            {item.contentList.length > 1 ? (
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}>
                {item.contentList.map(batteryItem => {
                  return connected ? (
                    <BatteryItem {...batteryItem} size="small" />
                  ) : (
                    <NoData
                      key={batteryItem.key}
                      style={[styles.noDataContaner, styles.smallSizeContainer]}
                      showImage={false}
                      content="--"
                      contentStyle={styles.blankText}
                    />
                  );
                })}
              </View>
            ) : (
              <View>
                {item.contentList.map(batteryItem => {
                  return connected ? (
                    <BatteryItem {...batteryItem} />
                  ) : (
                    <NoData
                      key={batteryItem.key}
                      style={styles.noDataContaner}
                      showImage={false}
                      content="--"
                      contentStyle={styles.blankText}
                    />
                  );
                })}
              </View>
            )}
            {item.showLowBattery && (
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginTop: 14,
                  paddingHorizontal: 6,
                }}>
                <View style={styles.redCircle} />
                <CustomText style={styles.lowBatteryText}>
                  {lowBatteryText}
                </CustomText>
              </View>
            )}
          </View>
        );
      })}
    </>
  );
};

export default BatteryList;

const styles = CustomStyleSheet.create({
  itemBox: {
    flexDirection: 'column',
    marginBottom: 15,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 13,
  },
  headerTitle: {
    fontSize: 18,
    lineHeight: 19,
    fontWeight: '600',
    color: '#3C3936',
  },
  headerSubTitle: {
    fontSize: 12,
    fontWeight: '400',
    color: '#000',
    opacity: 0.4,
  },
  noDataContaner: {
    backgroundColor: '#fff',
    height: 90,
    borderRadius: 10,
  },
  smallSizeContainer: {
    height: 140,
    width: '48.3%',
    paddingTop: 40,
  },
  blankText: {
    fontSize: 39,
    lineHeight: 48,
    fontWeight: '600',
    color: '#000000',
  },
  redCircle: {
    width: 7.5,
    height: 7.5,
    borderRadius: 7.5,
    marginRight: 6.5,
    backgroundColor: '#FF4646',
  },
  lowBatteryText: {
    fontSize: 14,
    lineHeight: 16.5,
    fontWeight: '400',
    color: '#000000',
    opacity: 0.6,
  },
});
