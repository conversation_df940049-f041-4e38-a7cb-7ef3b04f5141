import React, {useLayoutEffect, useMemo, useRef} from 'react';
import {View, Image, Animated, ImageBackground, Platform} from 'react-native';
import PropTypes from 'prop-types';
import CustomStyleSheet from '@utils/style/index.js';
import {Switch} from 'react-native-elements';
import LinearGradient from 'react-native-linear-gradient';
import {useTranslation} from 'react-i18next';
import {inject} from 'mobx-react';
import {
  LFPBatteryIcon,
  PortableBatteryIcon,
  OverheatIcon,
  ErrorIcon,
} from '../Icons';
import {
  workingStatusModeMap,
  subDevicesBatteryWorkingStatusModeMap,
} from '../config.js';
import ChargingCircle from './ChargingCircle.js';
import {DEVICE_WIDTH} from '@utils/device/index.js';
import ModelMap from '@config/model.js';
import {milliamValueToStringValue, maxPercentValue} from '@utils/tools.js';
import {getImageUrl} from '@utils/image';
import {geti18nText} from '@i18n/config';
import BatteryList from './BatteryList.js';
import CustomText from 'IOTRN/src/components/CustomText';

const isAndroid = Platform.OS === 'android';

const CenterInfoView = inject(store => ({
  deviceId: store.panel.deviceId,
  workingStatus: store.panel.home.workingStatus,
  _lfpBatteryStatus: store.panel.home.lfpBatteryStatus,

  firstPortableBatteryStatus: store.panel.home.firstPortableBatteryStatus,
  secondPortableBatteryStatus: store.panel.home.secondPortableBatteryStatus,

  remainingBatteryOfBatteries: store.panel.home.remainingBatteryOfBatteries,
  ratedCapacityOfBatteries: store.panel.home.ratedCapacityOfBatteries,
  dcChargingRemainingTimeOfBatteries:
    store.panel.home.dcChargingRemainingTimeOfBatteries,

  dcDcModeSwitch: store.panel.functionSetting.dcDcModeSwitch,
  customizeChargingSequence:
    store.panel.functionSetting.customizeChargingSequence,
  panelActions: store.panelActions,
}))(
  ({
    deviceId,
    workingStatus,
    _lfpBatteryStatus,
    firstPortableBatteryStatus,
    secondPortableBatteryStatus,

    remainingBatteryOfBatteries,
    ratedCapacityOfBatteries,
    dcChargingRemainingTimeOfBatteries,

    dcDcModeSwitch,
    customizeChargingSequence,
    panelActions,
    connected,
    children,
  }) => {
    const {t} = useTranslation('all');
    /**
     * @type {keyof subDevicesBatteryWorkingStatusModeMap}
     */
    const lfpBatteryStatus = _lfpBatteryStatus;

    const [LFPRemianingBattery, ...portalbleRemainingBatteyList] =
      remainingBatteryOfBatteries;
    const [LFPRatedCapacity, ...portalbleRatedCapacityList] =
      ratedCapacityOfBatteries;

    const lfpBatteryPercent = maxPercentValue(
      Math.floor((LFPRemianingBattery / LFPRatedCapacity) * 100) || 0,
    );

    const yTranslate = useRef(new Animated.Value(0)).current;

    const portableBatteryStatusList = [
      firstPortableBatteryStatus.batteryStatus,
      secondPortableBatteryStatus.batteryStatus,
    ];

    const chargingSequenceMap = {
      0: 'off',
      1: 'lfpBattery',
      2: 'portableBattery',
    };

    const chargingSequence = chargingSequenceMap[customizeChargingSequence];

    const mainWorkingStatusMode = workingStatusModeMap[workingStatus];
    const isDcDischarging = mainWorkingStatusMode === 'dcDischarging';
    const isChangingSwitchStyle = isDcDischarging && dcDcModeSwitch;

    const LFPBatteryStatusMode =
      subDevicesBatteryWorkingStatusModeMap[lfpBatteryStatus];

    const isLFPBatteryInLowPower = LFPBatteryStatusMode === 'lowPower';

    const isBatterysAllBlank =
      LFPBatteryStatusMode === 'empty' &&
      portalbleRemainingBatteyList.every(
        (_, index) => portableBatteryStatusList[index] === 'empty',
      );

    const isShowEnableDcModeSwitch = useMemo(() => {
      return (
        ['dcDischarging', 'standby'].includes(mainWorkingStatusMode) &&
        !isBatterysAllBlank &&
        connected
      );
    }, [workingStatus, isBatterysAllBlank, connected]);

    // 大包低电量并且DC开关是关闭状态才disable开关，保证dc开关是开的时候，可以关闭dc模式
    const isDcModeSwitchDisabled = isLFPBatteryInLowPower && !dcDcModeSwitch;

    const translateIn = () => {
      Animated.timing(yTranslate, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    };

    const translateOut = () => {
      Animated.timing(yTranslate, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    };

    /**
     * @type {boolean}
     * 必须满足条件才能动画
     */
    const shouldTranslate = dcDcModeSwitch && isDcDischarging;

    useLayoutEffect(() => {
      if (shouldTranslate) {
        translateIn();
      } else {
        translateOut();
      }
    }, [shouldTranslate]);

    const lfpBattery = useMemo(
      () => ({
        key: 'lffp_battery',
        title: t(geti18nText('home_lfpBattery_textview_text')),
        showTitle: true,
        subTitle:
          chargingSequence === 'lfpBattery'
            ? t(geti18nText('home_priorityCharging_textview_text'))
            : null,
        icon: <LFPBatteryIcon style={{marginRight: 10}} />,
        contentList: [
          {
            key: 'lffp_battery',
            status: lfpBatteryStatus,
            value: lfpBatteryPercent,
            unit: '%',
            desc: `${milliamValueToStringValue(LFPRatedCapacity, 0)}Ah`,
          },
        ],
        showLowBattery:
          isLFPBatteryInLowPower && connected && isShowEnableDcModeSwitch,
      }),
      [
        chargingSequence,
        lfpBatteryStatus,
        lfpBatteryPercent,
        LFPRatedCapacity,
        isLFPBatteryInLowPower,
        connected,
      ],
    );

    const portableBattery = useMemo(
      () => ({
        key: 'portalbe_battery',
        title: t(geti18nText('home_portableBattery_textview_text')),
        showTitle: true,
        subTitle:
          chargingSequence === 'portableBattery'
            ? t(geti18nText('home_priorityCharging_textview_text'))
            : null,
        icon: <PortableBatteryIcon style={{marginRight: 10}} />,
        contentList: portalbleRemainingBatteyList.map(
          (remainingBattery, index) => {
            return {
              key: `portalbe_battery_${index}`,
              status: portableBatteryStatusList[index],
              value: maxPercentValue(
                Math.floor(
                  (remainingBattery / portalbleRatedCapacityList[index]) * 100,
                ) || 0,
              ),
              unit: '%',
              desc: `${milliamValueToStringValue(
                portalbleRatedCapacityList[index],
                1,
              )}Ah`,
              showExtra: false,
              dcChargingRemainingTime:
                dcChargingRemainingTimeOfBatteries[index] || '--',
            };
          },
        ),
        showLowBattery: false,
      }),
      [
        chargingSequence,
        portalbleRemainingBatteyList,
        portalbleRatedCapacityList,
        portableBatteryStatusList,
        dcChargingRemainingTimeOfBatteries,
      ],
    );

    const acChargingBatteryList = [lfpBattery, portableBattery];
    const dcChargingBatteryList = [
      {
        ...portableBattery,
        showTitle: false,
        contentList: portableBattery.contentList.map(battery => ({
          ...battery,
          showExtra:
            subDevicesBatteryWorkingStatusModeMap[battery.status] !== 'ready',
        })),
      },
    ];

    // 纵向transform的距离，考虑不同尺寸的手机
    const ratio = DEVICE_WIDTH / 375;
    const yTranslateDistance = -77 * ratio;

    return (
      <Animated.View
        style={[
          styles.container,
          {
            transform: [
              {
                translateY: yTranslate.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, yTranslateDistance],
                }),
              },
            ],
          },
          {
            marginBottom: shouldTranslate ? yTranslateDistance : 0,
          },
        ]}>
        <TransformBox showLinearGradiant={shouldTranslate}>
          {/* show dc-dc charging when dc-dc charging is enabled */}
          {isShowEnableDcModeSwitch && (
            <View
              style={[
                styles.enableSwitchContainer,
                isChangingSwitchStyle && styles.switchOnContainer,
              ]}>
              <CustomText
                fontWeight={600}
                style={[
                  styles.enableSwitchTitle,
                  isDcModeSwitchDisabled && {
                    opacity: 0.4,
                  },
                ]}>
                {t(geti18nText('home_enableDcCharging_textview_text'))}
              </CustomText>
              <Switch
                color="#000000"
                value={dcDcModeSwitch}
                style={
                  isDcModeSwitchDisabled && {
                    opacity: 0.4,
                  }
                }
                disabled={isDcModeSwitchDisabled}
                onValueChange={flag => {
                  panelActions.editProperty({
                    deviceId,
                    propertyData: {
                      [ModelMap.dc_dc_mode_switch]: flag ? 1 : 0,
                    },
                  });
                }}
              />
            </View>
          )}
          {shouldTranslate && (
            <Animated.View
              style={[
                styles.dcChargerContainer,
                {
                  opacity: yTranslate.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0, 1],
                  }),
                },
              ]}>
              <ChargingCircle
                percent={lfpBatteryPercent}
                type={LFPBatteryStatusMode}>
                <ImageBackground
                  source={{
                    uri: getImageUrl(
                      `12010_static_shadow_${LFPBatteryStatusMode}`,
                    ),
                  }}
                  resizeMode="cover"
                  style={styles.backgroundImage}>
                  <View style={styles.circleContainer}>
                    <CustomText style={styles.circleName}>
                      {t(geti18nText('home_lfpBattery_textview_text'))}
                    </CustomText>
                    <CustomText style={styles.textContainer}>
                      <CustomText fontWeight={600} style={styles.circleValue}>
                        {lfpBatteryPercent}
                      </CustomText>
                      <CustomText fontWeight={600} style={styles.circleUnit}>
                        %
                      </CustomText>
                    </CustomText>
                    <View style={styles.statusIconContainer}>
                      {LFPBatteryStatusMode === 'overheat' ? (
                        <OverheatIcon />
                      ) : LFPBatteryStatusMode === 'error' ? (
                        <ErrorIcon />
                      ) : null}
                    </View>
                  </View>
                </ImageBackground>
              </ChargingCircle>
              <View style={styles.electricityContainer}>
                <Image
                  source={require('@assets/12010_static_current.png')}
                  style={styles.electricity}
                />
              </View>
            </Animated.View>
          )}
          <View style={shouldTranslate ? styles.batteryListContainer : {}}>
            <BatteryList
              list={
                shouldTranslate ? dcChargingBatteryList : acChargingBatteryList
              }
              connected={connected}
              lowBatteryText={t(
                geti18nText('home_lowBatteryReminder_textview_text'),
              )}
            />
            {children}
          </View>
        </TransformBox>
      </Animated.View>
    );
  },
);

CenterInfoView.propTypes = {
  deviceId: PropTypes.string,
  workingStatus: PropTypes.number,
  lfpBatteryStatus: PropTypes.number,
  firstPortableBatteryStatus: PropTypes.object,
  secondPortableBatteryStatus: PropTypes.object,
  remainingBatteryOfBatteries: PropTypes.array,
  ratedCapacityOfBatteries: PropTypes.array,
  dcChargingRemainingTimeOfBatteries: PropTypes.array,
  dcDcModeSwitch: PropTypes.bool,
  customizeChargingSequence: PropTypes.number,
  panelActions: PropTypes.object,
  connected: PropTypes.bool,
  children: PropTypes.node,
};

const TransformBox = ({showLinearGradiant = false, children}) => {
  return showLinearGradiant ? (
    <LinearGradient
      locations={[0.0866, 0.5408]}
      colors={['#F7F7FA', '#F7F7FA']}
      style={styles.linearBoxContainer}>
      {children}
    </LinearGradient>
  ) : (
    <View style={styles.noLinearBoxContainer}>{children}</View>
  );
};

const styles = CustomStyleSheet.create({
  container: {
    marginTop: 20,
  },
  enableSwitchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderRadius: 10,
    paddingHorizontal: 20,
    paddingVertical: isAndroid ? 10 : 13.5,
    marginBottom: 28,
    backgroundColor: '#FFF',
  },
  switchOnContainer: {
    borderRadius: 0,
    paddingHorizontal: 0,
    paddingTop: 6,
    paddingBottom: 24,
    marginHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#D8D8D6',
    marginBottom: 0,
    backgroundColor: 'transparent',
  },
  enableSwitchTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#3C3936',
  },
  dcChargerContainer: {
    marginTop: -25,
  },
  backgroundImage: {
    width: 240,
    height: 240,
    justifyContent: 'center',
    alignContent: 'center',
    position: 'absolute',
  },
  circleContainer: {
    alignItems: 'center',
    marginTop: 10,
  },
  circleName: {
    fontSize: 14,
    fontWeight: '400',
    color: '#000000',
    opacity: 0.4,
  },
  textContainer: {
    paddingTop: isAndroid ? 54 : 0,
  },
  circleValue: {
    fontSize: 60,
    fontWeight: '600',
    color: '#000000',
  },
  circleUnit: {
    fontSize: 18,
    lineHeight: 20,
    fontWeight: '600',
    color: '#000000',
  },
  statusIconContainer: {
    position: 'absolute',
    bottom: -30,
  },
  electricityContainer: {
    alignItems: 'center',
    marginTop: -55,
    marginBottom: 46,
    zIndex: -1,
  },
  electricity: {
    width: 240,
    height: 83,
  },
  batteryListContainer: {
    marginTop: -46,
  },
  linearBoxContainer: {
    // borderTopLeftRadius: 18,
    // borderTopRightRadius: 18,
    paddingHorizontal: 18,
    paddingTop: 25,
  },
  noLinearBoxContainer: {
    paddingHorizontal: 25,
  },
});

export default CenterInfoView;
