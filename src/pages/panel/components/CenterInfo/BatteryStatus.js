import React from 'react';
import {View} from 'react-native';
import {CVNIcon} from '@cvn/rn-panel-kit';
import {useTranslation} from 'react-i18next';
import {
  subDevicesBatteryWorkingStatusModeMap,
  subDevicesBatteryWorkingStatusTextMap,
} from '../config.js';
import CustomStyleSheet from '@utils/style/index.js';
import CustomText from 'IOTRN/src/components/CustomText.js';

const statusImageMap = {
  empty: require('@assets/12010_icon_emptybattery.png'),
  charging: require('@assets/12010_icon_chargingbattery.png'),
  ready: require('@assets/12010_icon_readybattery.png'),
  overheat: require('@assets/12010_icon_overheatbattery.png'),
  standby: require('@assets/12010_icon_standbybattery.png'),
  lowPower: require('@assets/12010_icon_standbybattery.png'),
  error: require('@assets/12010_icon_errorbattery.png'),
};

const statusColorMap = {
  charging: '#000000', // 充电颜色从#77BC1F改为#000000，UI设计稿颜色为#000000
  overheat: '#FF8A05',
  error: '#DA322B',
};

const BatteryStatus = ({status = 0, size = 'normal'}) => {
  const {t} = useTranslation('all');
  const statusText = subDevicesBatteryWorkingStatusModeMap[status] || 'empty';
  const statusI18nText = subDevicesBatteryWorkingStatusTextMap[status];
  return (
    <View style={styles.container}>
      <CVNIcon
        source={statusImageMap[statusText] || null}
        style={size === 'small' ? styles.leftIconSmall : styles.leftIcon}
      />
      <CustomText
        fontWeight={500}
        style={[
          styles.status,
          {
            color: statusColorMap[statusText] || '#000000',
          },
        ]}>
        {t(statusI18nText)}
      </CustomText>
    </View>
  );
};

const styles = CustomStyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 52,
  },
  leftIcon: {
    width: 50,
    height: 50,
    marginRight: 12,
  },
  leftIconSmall: {
    width: 36,
    height: 36,
    marginRight: 8,
  },
  status: {
    color: '#000000',
    fontSize: 18,
    lineHeight: 20,
    fontWeight: '500',
    flexShrink: 1,
  },
});

export default BatteryStatus;
