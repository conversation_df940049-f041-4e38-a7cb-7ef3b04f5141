import React, {useMemo, memo} from 'react';
import {View, Platform} from 'react-native';
import {inject} from 'mobx-react';
import Card from '@components/Card';
import CustomStyleSheet from '@utils/style/index.js';
import {useTranslation} from 'react-i18next';
import {BatteryOptimizationIcon, CustomizeSequenceIcon} from './Icons';
import ModelMap from '@config/model.js';
import {Switch} from 'react-native-elements';
import {LargerSwitch} from '@components/Switch';
import {geti18nText} from '@i18n/config';
import {workingStatusModeMap} from './config';
import CustomText from 'IOTRN/src/components/CustomText';

const isAndroid = Platform.OS === 'android';

const FunctionSetting = inject(store => ({
  deviceId: store.panel.deviceId,
  workingStatus: store.panel.home.workingStatus,
  customizeChargingSequence:
    store.panel.functionSetting.customizeChargingSequence,
  batteryOptimizationSwitch:
    store.panel.functionSetting.batteryOptimizationSwitch,
  panelActions: store.panelActions,
}))(
  ({
    deviceId,
    workingStatus,
    customizeChargingSequence,
    batteryOptimizationSwitch,
    panelActions,
    connected,
  }) => {
    const {t} = useTranslation('all');

    const handleSwitchSequence = flag => {
      panelActions.editProperty({
        deviceId,
        propertyData: {
          [ModelMap.customize_charging_sequence]: flag ? 1 : 0,
        },
      });
    };

    const handleChangeSequence = index => {
      panelActions.editProperty({
        deviceId,
        propertyData: {
          [ModelMap.customize_charging_sequence]: index + 1,
        },
      });
    };

    const handleChangeBatteryOptimization = flag => {
      panelActions.editProperty({
        deviceId,
        propertyData: {
          [ModelMap.battery_optimization_switch]: flag ? 1 : 0,
        },
      });
    };

    const isCustomizeSequenceDisabled =
      !connected || workingStatusModeMap[workingStatus] !== 'acCharging';

    const list = useMemo(() => {
      return [
        {
          key: 'customize_sequence',
          icon: <CustomizeSequenceIcon style={{marginRight: 14}} />,
          title: t(geti18nText('home_customizeSequenceTitle_textview_text')),
          rightElement: (
            <View style={{marginVertical: isAndroid ? -10 : 0}}>
              <Switch
                color="#000000"
                value={customizeChargingSequence > 0}
                disabled={isCustomizeSequenceDisabled}
                onValueChange={handleSwitchSequence}
              />
            </View>
          ),
          description: t(
            geti18nText('home_customizeSequenceDesc_textview_text'),
          ),
          showContent: customizeChargingSequence > 0,
          disabled: isCustomizeSequenceDisabled,
          content: (
            <View style={{marginTop: 30}}>
              <LargerSwitch
                disabled={isCustomizeSequenceDisabled}
                choices={['LFP Battery', 'Portable Battery']}
                value={customizeChargingSequence - 1}
                onChange={handleChangeSequence}
              />
            </View>
          ),
        },
        {
          key: 'battery_optimization',
          icon: <BatteryOptimizationIcon style={{marginRight: 14}} />,
          title: t(geti18nText('home_batteryOptimizationTitle_textview_text')),
          rightElement: (
            <View style={{marginVertical: isAndroid ? -10 : 0}}>
              <Switch
                color="#000000"
                value={batteryOptimizationSwitch}
                disabled={!connected}
                onValueChange={handleChangeBatteryOptimization}
              />
            </View>
          ),
          description: t(
            geti18nText('home_batteryOptimizationDesc_textview_text'),
          ),
          showContent: false,
          disabled: !connected,
        },
      ];
    }, [
      isCustomizeSequenceDisabled,
      customizeChargingSequence,
      batteryOptimizationSwitch,
      connected,
    ]);

    return (
      <View>
        {list.map((item, index) => {
          return (
            <Card
              style={{borderRadius: 10}}
              key={item.key}
              title={item.title}
              // icon={item.icon}
              rightElement={item.rightElement}
              disabled={item.disabled}>
              <CustomText style={styles.description}>
                {item.description}
              </CustomText>
              {item.showContent && item.content}
            </Card>
          );
        })}
      </View>
    );
  },
);

const styles = CustomStyleSheet.create({
  description: {
    fontSize: 14,
    fontWeight: '400',
    color: '#999999',
  },
});

export default memo(FunctionSetting);
