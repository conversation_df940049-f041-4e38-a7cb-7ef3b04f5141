import React from 'react';
import {View, Image, Modal} from 'react-native';
import {Button} from 'react-native-elements';
import {geti18nText} from '@i18n/config';
import {useTranslation} from 'react-i18next';
import CustomStyleSheet from '@utils/style/index.js';
import {getImageUrl} from '@utils/image';
import CustomText from 'IOTRN/src/components/CustomText';

const ConnectionPrompt = ({visible = false, onPress = () => {}}) => {
  const {t} = useTranslation('all');

  return (
    <Modal animationType="slide" transparent visible={visible}>
      <View style={styles.modalContainer}>
        <View style={styles.contentContainer}>
          <Image
            style={styles.image}
            source={{
              uri: getImageUrl('connection_prompt'),
            }}
          />
          <CustomText style={styles.description}>
            {t(geti18nText('home_connectionDesc_textview_text'))}
          </CustomText>
          <Button
            onPress={onPress}
            title={t(geti18nText('home_connectionOk_button_text'))}
            containerStyle={styles.buttonContainer}
            buttonStyle={styles.button}
            titleStyle={styles.buttonTitle}
          />
        </View>
      </View>
    </Modal>
  );
};

const styles = CustomStyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  contentContainer: {
    width: 325,
    backgroundColor: '#FFF',
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingVertical: 15,
  },
  image: {
    width: 295,
    height: 180,
  },
  description: {
    fontSize: 15,
    fontWeight: '400',
    textAlign: 'center',
    marginTop: 30,
  },
  buttonContainer: {
    marginTop: 30,
  },
  button: {
    backgroundColor: '#77BC1F',
    borderRadius: 2,
    borderColor: '#CDCDCD',
    borderWidth: 0.5,
    paddingVertical: 13,
  },
  buttonTitle: {
    color: '#FFF',
    fontSize: 18,
    fontWeight: '500',
  },
});

export default ConnectionPrompt;
