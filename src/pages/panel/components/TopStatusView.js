import React, {memo} from 'react';
import {View, StyleSheet} from 'react-native';
import PropTypes from 'prop-types';
import {inject} from 'mobx-react';
import {useTranslation} from 'react-i18next';
import BatteryPanel from '@components/Battery';
import NoData from '@components/NoData.js';
import {DEVICE_WIDTH} from '@utils/device/index.js';
import {geti18nText} from '@i18n/config';
import CustomText from 'IOTRN/src/components/CustomText';

const TopStatusView = inject(store => ({
  chargingStatus: store.panel.home.chargingStatus,
  chargingProgress: store.panel.home.chargingProgress,
  remainingChargingTime: store.panel.home.remainingChargingTime,
  optimizedBatteryChargingSwitch:
    store.panel.setting.optimizedBatteryChargingSwitch,
}))(
  ({
    chargingStatus,
    chargingProgress,
    remainingChargingTime,
    optimizedBatteryChargingSwitch,
    navHeaderHeight = 80,
    disabled,
  }) => {
    // console.log("it's top status comp");

    const blankText = '--';

    const {hour, min} = remainingChargingTime;

    const {t} = useTranslation('all');

    // ready 表示充满
    const chargingStatusMap = {
      2: 'ready',
    };

    const chargingStatusValue = chargingStatusMap[chargingStatus];

    const isBatteryNotReady = chargingStatusValue !== 'ready';
    const hasRemainingChargingTime = hour !== '--' || min !== '--';

    const isShowRemainingChargingTime =
      isBatteryNotReady && hasRemainingChargingTime;

    const containerStyle = createContainerStyle(navHeaderHeight);

    return (
      <>
        {disabled ? (
          <NoData
            style={containerStyle.noDataContainer}
            showImage={false}
            content={blankText}
            contentStyle={styles.blankText}
          />
        ) : (
          <>
            <View style={containerStyle.batteryContainer}>
              <BatteryPanel
                width={DEVICE_WIDTH}
                percent={chargingProgress}
                maxPercent={optimizedBatteryChargingSwitch ? 90 : 0}
              />
            </View>
            {isShowRemainingChargingTime ? (
              <View style={styles.textContainer}>
                <CustomText style={styles.extraTextPrefix}>
                  {t(geti18nText('home_finishIn_textview_text'))}
                  <CustomText style={styles.extraText}>
                    <CustomText fontWeight={500} style={styles.extraNumText}>
                      {' '}
                      {hour}
                    </CustomText>
                    hr
                    <CustomText fontWeight={500} style={styles.extraNumText}>
                      {' '}
                      {min}
                    </CustomText>
                    min
                  </CustomText>
                </CustomText>
              </View>
            ) : null}
          </>
        )}
      </>
    );
  },
);

TopStatusView.propTypes = {
  home: PropTypes.object,
  disabled: PropTypes.bool,
};

const createContainerStyle = navHeaderHeight => {
  return StyleSheet.create({
    noDataContainer: {
      height: navHeaderHeight + 227.5,
      paddingTop: navHeaderHeight - 20,
    },
    batteryContainer: {
      paddingTop: navHeaderHeight - 20,
      marginBottom: -30,
    },
  });
};

const styles = StyleSheet.create({
  blankText: {
    fontSize: 65,
    lineHeight: 65,
    fontWeight: 'bold',
    letterSpacing: 1.5,
    color: '#000000',
  },
  textContainer: {
    position: 'absolute',
    left: 25,
    bottom: 52.44,
  },
  extraTextPrefix: {
    fontSize: 16,
    color: '#3C3936',
    fontWeight: '400',
  },
  extraText: {
    fontSize: 16,
    color: '#000',
    fontWeight: '400',
  },
  extraNumText: {
    fontSize: 20,
    fontWeight: '500',
  },
});

export default memo(TopStatusView);
