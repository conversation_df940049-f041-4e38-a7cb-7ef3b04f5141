import React, {memo} from 'react';
import {mobile, CommonEmitter, device, Utils} from '@cvn/rn-panel-kit';
import PageBase from '@base/index.js';
import PageView from '@components/PageView.js';
import {HeaderView, WhiteSpace} from '@components';
import {isIphoneX} from '@utils/device/index.js';
import {
  View,
  Platform,
  Animated,
  AppState,
  TouchableOpacity,
} from 'react-native';
import {inject, observer} from 'mobx-react/native';
import CustomStyleSheet from '@utils/style/index.js';
import {
  TopBatteryStatus,
  Notification,
  CenterInfo,
  FunctionSetting,
  UpdateItem,
  ConnectionPrompt,
} from '@pages/panel/components';
import {withTranslation} from 'react-i18next';
import {geti18nText} from '@i18n/config';
import {workingStatusModeMap} from './components/config';
import {BLE_RETRY_Times} from '@config/index.js';
import CustomText from 'IOTRN/src/components/CustomText';
import {throttle} from 'lodash-es';

const {OtaUtils, BleUtils, MessageUtils, LogUtils} = Utils;

const bleChangeTypeMap = {
  0: 'notOpened',
  1: 'opened',
  2: 'notAuthorized',
};

const bleConnectStatusMap = {
  0: 'unConnected',
  1: 'connecting',
  2: 'connected',
  3: 'failed',
};

const bleConnectStatusLocaleMap = {
  unConnected: geti18nText('home_notConnected_textview_text'),
  connecting: geti18nText('home_connecting_textview_text'),
  connected: geti18nText('home_connected_textview_text'),
  failed: geti18nText('home_notConnected_textview_text'),
};

/**
 * @typedef {import('@/mobx').panelStores} panelStores
 * @typedef {import('@/mobx').panelActions} panelActions
 * @typedef {import('react-i18next').TFunction} TFunction
 * @typedef {import('@config/types').MessageData} MessageData
 */

/**
 * @typedef {Object} InjectedProps
 * @property {panelStores} panel - MobX Store
 * @property {panelActions} panelActions - Mobx Store Action
 * @property {TFunction} t - i18next translation function
 */

@inject('panelActions', 'panel')
@observer
class Home extends PageBase {
  /**
   * @type {InjectedProps}
   */
  props;

  constructor(props) {
    super(props);
    this.state = {
      connectStatus: 'connecting',
      headerOpacity: 0, // 头部透明度动画值
      headerMode: 'normal', // 导航栏模式
      navHeaderHeight: 80,
      isShowConnectionPrompt: false, // 显示连接提示弹窗
    };

    // 刚进入和重新开启蓝牙时置位
    this.retryTimes = 0;
    // 是否已经检查过ota升级
    this.otaUpdateDidChecked = false;
    // 是否是ota升级完成
    this.fromOtaDone = false;
    // 是否弹窗打开
    this.isDialogShow = false;
    // LogUtils.init();
    this.appStateSubscription = null;
  }

  componentDidMount() {
    // 处理蓝牙初始状态变化
    this.dealWithInitialBluetoothChange();

    // 第一次进去掉连接请求
    device.requestBleConnect(this.props.panel.mac);

    // 处理蓝牙连接状态变化
    this.dealWithDeviceBleConnectStateChange();

    // 查询设备详情
    this.getDetaiListAfterRNFocus();

    // 处理安卓物理返回键监听
    this.dealWithAndroidBack();

    // 处理ota升级完成
    this.dealWithOtaCompletedChange();

    // 处理RN和原生来回跳转
    this.dealWithRNContainerPushOrPop();

    // app 进入后台后，重连
    this.dealWithAppStateChange();

    // 设备注册
    this.dealWithDeviceRegister();
    // 首页故障消息条
    this.dealWithFaultInfo();
  }

  dealWithFaultInfo = () => {
    this.props.panelActions.fetchFaultInfo();
    this.faultTimer = setInterval(() => {
      this.props.panelActions.fetchFaultInfo();
    }, 15000);
  };
  /**
   * 处理设备注册 后回调
   */
  dealWithDeviceRegister = () => {
    CommonEmitter.addListener('deviceRegisterSuccess', res => {
      const {deviceId, infoStatus} = res;
      const {deviceId: currentDeviceId} = this.props.panel;
      //  console.log('***', res, deviceId, currentDeviceId);
      // 当前设备才处理
      if (deviceId === currentDeviceId) {
        // 更新缓存中的 设备注册信息
        this.props.panelActions.updateDeviceInfoStatus(infoStatus);
      }
    });
  };

  /**
   * 监听 app 前后台状态变化
   * active:前台
   * background:后台
   ** in another app
   ** on the home screen
   ** [Android] on another Activity (even if it was launched by your app)
   * inactive:后台，iOS 会出现这个过渡状态
   * iOS 在进入后台后，重置蓝牙显示状态并发起重连
   */
  dealWithAppStateChange = () => {
    // android 可以保持蓝牙连接，无需考虑重连
    if (Platform.OS === 'android') {
      return;
    }
    this.appStateSubscription = AppState.addEventListener(
      'change',
      nextAppState => {
        //  console.log('++++++ app state ', nextAppState);
        if (nextAppState === 'active') {
          this.retryTimes = 0;
          this.setState({
            connectStatus: 'connecting',
          });
          this.props.panelActions.setBleConnected(false);
          device.requestBleConnect(this.props.panel.mac);
        }
        this.setState({
          connectStatus: 'connecting',
        });
        this.props.panelActions.setBleConnected(false);
        this.props.panelActions.closeDcDcModeSwitch();
        device.requestBleConnect(this.props.panel.mac);
      },
    );
  };

  /**
   * 监听滑动，改变导航栏模式
   */
  dealWithScrollListener = value => {
    const y = value.nativeEvent.contentOffset.y;
    const maxScrollDistance = 40;
    throttle(() => {
      if (y >= maxScrollDistance) {
        this.setState({
          headerOpacity: 1,
        });
      } else {
        const headerOpacity = y / maxScrollDistance;
        this.setState({
          headerOpacity,
        });
      }
    }, 1000)();
  };

  /**
   * 监听ota升级完成，重置ota升级状态
   * ota升级完成之后可能会断开蓝牙连接，此时重连次数如果已经用完，发起重连
   */
  dealWithOtaCompletedChange = () => {
    CommonEmitter.addListener('otaUpdateDidCompleted', () => {
      // console.log('--otaUpdateDidCompleted--');
      this.fromOtaDone = true;

      this.props.panelActions.setOtaInfo({
        showRed: false,
      });
      this.props.panelActions.setIsForceUpdate(false);

      const {
        mac,
        deviceId,
        bleConnected,
        home: {workingStatus},
      } = this.props.panel;
      device.requestBleConnect(mac);
      // 再查询 ALL 物模型
      BleUtils.sendBleCmdToFetchAllModelData(mac);
      // 再次上报总成零件版本号
      OtaUtils.watchBleFirmwareVersionChange({deviceId, mac});

      const dcWorkingStatus = workingStatusModeMap[workingStatus];

      // 只会在 dc 模式下，ota升级之后设备会自动下电，在这里做一个上电的提示
      if (dcWorkingStatus === 'dcDischarging') {
        this.setState({
          isShowConnectionPrompt: true,
        });
      }

      if (!bleConnected) {
        this.resetBleConnect();
      }
    });
  };

  /**
   * 监听 原生->RN之间来回跳转的逻辑
   */
  dealWithRNContainerPushOrPop = () => {
    CommonEmitter.addListener('RNContainerViewWillAppear', () => {
      // console.log('RNContainerViewWillAppear', this.fromOtaDone);
      this.props.panelActions.setIsInRNContainerVC(true);
      // 每当回到RN时，检查下是否还有强制升级，如有，弹框
      const {isForceUpdate: isForce} = this.props.panel;
      if (!this.fromOtaDone && isForce) {
        this.handleAlertAndDot();
      }
    });
    CommonEmitter.addListener('RNContainerViewWillDisAppear', () => {
      // console.log('RNContainerViewWillDisAppear');
      this.props.panelActions.setIsInRNContainerVC(false);
      // 跳出RN vc时，该值赋值为false
      // this.fromOtaDone = false;
    });
  };

  /**
   *  处理监听蓝牙初始状态变化逻辑
   *  0:未开启  1:已开启  2:未授权
   *  未开启：提示用户开启蓝牙
   *  已开启：请求连接设备
   *  未授权：提示用户未授权并请求蓝牙连接
   */
  dealWithInitialBluetoothChange = () => {
    const {t} = this.props;
    CommonEmitter.addListener('bluetoothChange', res => {
      // console.log('bluetoothChange', res);
      const type = res - 0;
      const phoneBleStatus = bleChangeTypeMap[type];
      const mac = this.props.panel.mac;
      switch (phoneBleStatus) {
        case 'notOpened':
          mobile.toast(
            t(geti18nText('ble_notopened_textview_text', true)),
            () => {},
          );
          this.setState({
            connectStatus: 'unConnected',
          });
          this.props.panelActions.setBleConnected(false);
          this.props.panelActions.closeDcDcModeSwitch();
          break;
        case 'opened':
          this.retryTimes = 0; // 重新开启蓝牙时置位
          mobile.toast(
            t(geti18nText('ble_opened_textview_text', true)),
            () => {},
          );
          device.requestBleConnect(mac);
          break;
        case 'notAuthorized':
          mobile.toast(
            t(geti18nText('ble_notauthorizedtitle_textview_text', true)),
            () => {},
          );
          // device.requestBleConnect(mac);
          this.setState({
            connectStatus: 'unConnected',
          });
          this.props.panelActions.setBleConnected(false);
          this.props.panelActions.closeDcDcModeSwitch();
          break;
        default:
          break;
      }
    });
  };

  /**
   *  处理蓝牙连接后状态变化逻辑
   *  0:未连接  1:连接中  2:已连接  3:连接失败
   *  已连接：通过蓝牙获取设备信息，更新UTC时钟，OTA升级
   *  其他状态：尝试重连
   */
  dealWithDeviceBleConnectStateChange = () => {
    CommonEmitter.addListener('deviceBleConnectStateChange', res => {
      // console.log('deviceBleConnectStateChange---', res);
      // res: 0:unConnected  1:connecting  2:connected 3:failed
      const connectStatus = bleConnectStatusMap[Number(res)];
      // console.log('connectStatus', connectStatus);
      const isConnected = connectStatus === 'connected';
      // 重复已连接没有意义，过滤
      if (connectStatus === this.state.connectStatus && isConnected) {
        return;
      }

      this.setState({
        connectStatus,
      });

      if (isConnected) {
        // 同步store保存蓝牙连接状态
        this.props.panelActions.setBleConnected(true);
        // 重置蓝牙重连次数
        this.retryTimes = 0;

        this.getAllBleData();
        this.getUTCDate();
        // ota 检查目前只调用一次，后期根据缓存判断是否强制升级弹窗
        if (!this.otaUpdateDidChecked) {
          this.otaUpdateDidChecked = true;
          this.dealWithOtaUpdate();
        } else {
          this.handleAlertAndDot();
        }
      } else {
        if (this.props.panel.isNativeGoback) {
          return;
        }
        this.props.panelActions.setBleConnected(false);
        this.props.panelActions.closeDcDcModeSwitch();
        this.dealWithReConnect();
      }
    });
  };

  /**
   * 安卓物理返回键处理
   */
  dealWithAndroidBack = () => {
    if (Platform.OS === 'android') {
      // console.log('dealWithAndroidBack');
      CommonEmitter.addListener('keyBackDown', res => {
        if (LogUtils.currentPage === 'ViewPanelHome') {
          this.dealWithWillUnmount();
        }
        this.props.panelActions.setIsNativeGoback(true);
        // console.log('keyBackDown---res--', res);
        this.androidGoBack();
      });
    }
  };

  /**
   * RN容器focus后，再次查询设备详情
   */
  getDetaiListAfterRNFocus = () => {
    CommonEmitter.addListener('NAVIGATOR_ON_WILL_FOCUS', () => {
      // console.log('NAVIGATOR_ON_WILL_FOCUS');
      this.getDetailList();
    });
    this.getDetailList();
  };

  /**
   * 调用接口查询， 获取设备详情数据
   */
  getDetailList = () => {
    this.props.panelActions.fetchDeviceDetail();
  };

  /**
   * 更新UTC时钟
   */
  getUTCDate = () => {
    device.requestUTCDateResolveBlock?.(res => {
      // console.log('current utc date cmd hex:', res);
      const cmdHex = res;
      const {mac} = this.props.panel;
      device.sendCmd(cmdHex, mac);
    });
  };

  /**
   * 发送蓝牙指令，查询ALL
   * 包括电量 / 使用时长 / 剩余安时 / 历史使用时长等
   */
  getAllBleData = () => {
    CommonEmitter.addListener('localDeviceDataChange', res => {
      // console.log('localDeviceDataChange', JSON.parse(res));
      this.props.panelActions.handleBleRes(res);
    });
    const {mac} = this.props.panel;
    BleUtils.sendBleCmdToFetchAllModelData(mac);
  };

  /**
   * 重置蓝牙连接，并尝试重连
   */
  resetBleConnect = () => {
    this.retryTimes = 0;
    this.dealWithReConnect();
  };

  /**
   * 重连
   */
  dealWithReConnect = () => {
    if (this.retryTimes > BLE_RETRY_Times) {
      this.setState({
        connectStatus: 'unConnected',
      });
      this.props.panelActions.setBleConnected(false);
      this.props.panelActions.closeDcDcModeSwitch();
      return;
    }
    device.requestBleConnect(this.props.panel.mac);
    this.retryTimes++;
  };

  componentWillUnmount() {
    this.dealWithWillUnmount();
  }

  dealWithWillUnmount = () => {
    // ble listener
    CommonEmitter.removeAllListeners('bluetoothChange');
    CommonEmitter.removeAllListeners('deviceBleConnectStateChange');
    CommonEmitter.removeAllListeners('localDeviceDataChange');

    // ota listener
    CommonEmitter.removeAllListeners('otaUpdateDidCompleted');

    // rn container
    CommonEmitter.removeAllListeners('NAVIGATOR_ON_WILL_FOCUS');
    CommonEmitter.removeAllListeners('RNContainerViewWillAppear');
    CommonEmitter.removeAllListeners('RNContainerViewWillDisAppear');

    // 设备注册
    CommonEmitter.removeAllListeners('deviceRegisterSuccess');

    // app-state
    this.appStateSubscription?.remove?.();

    // android物理返回键监听
    if (Platform.OS === 'android') {
      CommonEmitter.removeAllListeners('keyBackDown');
    }
    const {deviceId} = this.props.panel;
    OtaUtils.unsubscribeToRelatedOtaTopics(deviceId);
    OtaUtils.removeRelatedEventListners();
    MessageUtils.removeMessageListener();
    this.faultTimer && clearInterval(this.faultTimer);
  };

  /**
   * ota升级
   */
  dealWithOtaUpdate = () => {
    const {deviceId, mac} = this.props.panel;
    OtaUtils.watchBleFirmwareVersionChange({deviceId, mac}, () => {
      // 查询 ota
      OtaUtils.checkAndSubscribeToOTATask({deviceId}, otainfo => {
        const {isForceUpdate, showRed, customVersion = ''} = otainfo;
        console.log('🦺 checkOTATask---', otainfo);
        this.props.panelActions.setOtaInfo({
          showRed,
          otaVersion: customVersion,
        });
        this.props.panelActions.setIsForceUpdate(isForceUpdate);
        this.handleAlertAndDot();
      });
    });
  };

  /**
   * 处理强制升级弹框
   */
  handleAlertAndDot = () => {
    const {t} = this.props;
    const {isForceUpdate: isForce, isInRNContainerVC} = this.props.panel;
    // 弹框
    if (isForce && isInRNContainerVC && this.isDialogShow === false) {
      // console.log('--isForce--', isForce, isInRNContainerVC);
      this.isDialogShow = true;
      mobile.simpleConfirmDialog(
        ' ',
        t(geti18nText('home_otaAlertMsg_textview_text')),
        () => {
          this.isDialogShow = false;
          if (this.props.panel.bleConnected) {
            this.props.panelActions.jumpToOtaUpdate();
          } else {
            mobile.toast(
              t(geti18nText('ble_error_textview_text', true)),
              () => {},
            );
          }
        },
        () => {
          // console.log('cancel');
          this.dealWithGoBack();
        },
      );
    }
  };

  /**
   * 处理页面返回
   */
  dealWithGoBack = () => {
    this.props.panelActions.setIsNativeGoback(true);
    this.dealWithWillUnmount();
    mobile.back();
  };

  render() {
    const {
      connectStatus,
      headerMode,
      headerOpacity,
      navHeaderHeight,
      isShowConnectionPrompt,
    } = this.state;
    const {
      deviceName,
      home: {workingStatus},
      functionSetting: {dcDcModeSwitch},
    } = this.props.panel;

    const {t} = this.props;

    const isBleConnected = connectStatus === 'connected';
    const disabled = !isBleConnected;

    /**
     * 在DC模式下，不显示升级和通知横幅
     * 1. 因为纵向transform会盖住横幅
     * 2. 存在这些横幅，会影响纵向tranform的距离
     */
    const isDcDischarging =
      workingStatusModeMap[workingStatus] === 'dcDischarging';
    const isDcDcModeOn = dcDcModeSwitch === true;
    // 不显示通知和升级横幅
    const isNotShowNotificationAndUpdate = isDcDcModeOn && isDcDischarging;
    return (
      <PageView>
        <View
          style={styles.navHeaderContainer}
          onLayout={({nativeEvent}) => {
            const {height} = nativeEvent.layout;
            this.setState({
              navHeaderHeight: height,
            });
          }}>
          {/* 导航栏 */}
          <HeaderView
            style={[
              styles.navHeader,
              {
                backgroundColor: 'rgba(255, 255, 255, ' + headerOpacity + ')',
              },
            ]}
            mode={headerMode}
            elevated={false}
            onLeftPress={() => {
              this.dealWithGoBack();
            }}
            showMore
            onRightPress={() => {
              this.goPage('ViewDetailList');
            }}
            title={
              <TouchableOpacity
                style={styles.headerTitleContainer}
                onPress={() => {
                  this.goPage('ViewEditName', {
                    deviceName,
                  });
                }}>
                <CustomText
                  fontWeight={500}
                  fontFamily="Rubik"
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  style={styles.headerDeviceName}>
                  {deviceName?.toUpperCase()}
                </CustomText>
                <CustomText
                  fontWeight={400}
                  fontFamily="Rubik"
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  style={styles.headerConnectStatus}>
                  {t(bleConnectStatusLocaleMap[connectStatus])}
                </CustomText>
              </TouchableOpacity>
            }
          />
        </View>
        <Animated.ScrollView
          scrollEventThrottle={1}
          showsVerticalScrollIndicator={false}
          onScroll={this.dealWithScrollListener}>
          {/* 顶部电池板和设备状态 */}
          <TopBatteryStatus
            navHeaderHeight={navHeaderHeight}
            connected={isBleConnected}
          />
          {/* 不在DC模式的时候显示这些横幅 */}
          {isNotShowNotificationAndUpdate ? null : (
            <View style={styles.itemsContainer}>
              {/* 设备通知消息 */}
              <Notification />
              {/* 固件升级 */}
              <UpdateItem disabled={disabled} style={styles.item} />
            </View>
          )}

          {/* 电量信息 */}
          <CenterInfo connected={isBleConnected}>
            {isNotShowNotificationAndUpdate ? (
              <Notification style={styles.notificationBox} />
            ) : null}

            <FunctionSetting connected={isBleConnected} />
          </CenterInfo>

          <WhiteSpace size={isIphoneX ? 34 : 0} />
        </Animated.ScrollView>

        <ConnectionPrompt
          visible={isShowConnectionPrompt}
          onPress={() => {
            this.setState({isShowConnectionPrompt: false});
            this.resetBleConnect();
          }}
        />
      </PageView>
    );
  }
}

export default withTranslation('all')(memo(Home));

const styles = CustomStyleSheet.create({
  navHeaderContainer: {
    position: 'absolute',
    zIndex: 1,
  },
  navHeader: {
    borderBottomWidth: 0,
    backgroundColor: '#F7F7FA',
  },
  headerTitleContainer: {
    alignItems: 'center',
  },
  headerDeviceName: {
    fontSize: 22,
    lineHeight: 28,
    color: '#3C3936',
    fontWeight: '500',
  },
  headerConnectStatus: {
    fontSize: 16,
    color: '#3C3936',
    lineHeight: 28,
  },
  itemsContainer: {
    marginHorizontal: 25,
    marginTop: 40,
  },
  item: {
    marginBottom: 10,
  },
  notificationBox: {
    marginTop: 10,
    marginBottom: 20,
  },
});
