import React, {memo} from 'react';
import {mobile, CommonEmitter, device, Utils} from '@cvn/rn-panel-kit';
import PageBase from '@base/index.js';
import PageView from '@components/PageView.js';
import {HeaderView, WhiteSpace} from '@components';
import {isIphoneX} from '@utils/device/index.js';
import {
  View,
  Platform,
  AppState,
  Animated,
  TouchableOpacity,
} from 'react-native';
import {inject, observer} from 'mobx-react/native';
import CustomStyleSheet from '@utils/style/index.js';
import {
  TopBattery,
  ControlArea,
  Notification,
  UsageHistory,
} from '@pages/panel/components';
import {withTranslation} from 'react-i18next';
import {geti18nText} from '@i18n/config';
import {BLE_RETRY_Times} from '@config';
import {throttle} from 'lodash-es';
import CustomText from 'IOTRN/src/components/CustomText';

const {OtaUtils, BleUtils, MessageUtils, LogUtils} = Utils;

const bleChangeTypeMap = {
  0: 'notOpened',
  1: 'opened',
  2: 'notAuthorized',
};

const bleConnectStatusMap = {
  0: 'unConnected',
  1: 'connecting',
  2: 'connected',
  3: 'failed',
};

const bleConnectStatusLocaleMap = {
  unConnected: geti18nText('home_notConnected_textview_text'),
  connecting: geti18nText('home_connecting_textview_text'),
  connected: geti18nText('home_connected_textview_text'),
  failed: geti18nText('home_notConnected_textview_text'),
};

/**
 * @typedef {import('@/mobx').panelStores} panelStores
 * @typedef {import('@/mobx').panelActions} panelActions
 * @typedef {import('react-i18next').TFunction} TFunction
 * @typedef {import('@config/types').MessageData} MessageData
 */

/**
 * @typedef {Object} InjectedProps
 * @property {panelStores} panel - MobX Store
 * @property {panelActions} panelActions - Mobx Store Action
 * @property {TFunction} t - i18next translation function
 */

@inject('panelActions', 'panel')
@observer
class Home extends PageBase {
  /**
   * @type {InjectedProps}
   */
  props;

  constructor(props) {
    super(props);
    this.state = {
      connectStatus: 'connecting',
      headerMode: 'normal',
      headerOpacity: 0,
      navHeaderHeight: 80,
    };

    // 刚进入和重新开启蓝牙时置位
    this.retryTimes = 0;
    // 是否已经检查过ota升级
    this.otaUpdateDidChecked = false;
    // 是否是ota升级完成
    this.fromOtaDone = false;
    // 是否弹窗打开
    this.isDialogShow = false;

    this.scrollY = new Animated.Value(0);
    this.appStateSubscription = null;

    // LogUtils.init();
  }

  componentDidMount() {
    // 处理蓝牙初始状态变化
    this.dealWithInitialBluetoothChange();

    // 第一次进去掉连接请求
    device.requestBleConnect(this.props.panel.mac);

    // 处理蓝牙连接状态变化
    this.dealWithDeviceBleConnectStateChange();

    this.getDetaiListAfterRNFocus();

    // 处理安卓物理返回键监听
    this.dealWithAndroidBack();

    // 处理ota升级完成
    this.dealWithOtaCompletedChange();

    // 处理RN和原生来回跳转
    this.dealWithRNContainerPushOrPop();

    // app 进入后台后，重连
    this.dealWithAppStateChange();

    this.dealWithScrollListener();
    // 获取消息告警
    this.dealWithFaultInfo();
  }

  /**
   * 定时15秒查询一次告警消息，保证时效性
   */
  dealWithFaultInfo = () => {
    this.props.panelActions.fetchFaultInfo();
    this.faultTimer = setInterval(() => {
      this.props.panelActions.fetchFaultInfo();
    }, 15000);
  };
  /**
   * 监听 app 前后台状态变化
   * iOS 在进入后台后，重置蓝牙显示状态并发起重连
   */
  dealWithAppStateChange = () => {
    this.appStateSubscription = AppState.addEventListener(
      'change',
      nextAppState => {
        //  console.log('++++++ app state ', nextAppState);
        if (nextAppState === 'active') {
          this.retryTimes = 0;
          // console.log('--app state change')
          // android 可以保持蓝牙连接，无需重连
          if (Platform.OS === 'android') {
            return;
          }
          this.setState({
            connectStatus: 'connecting',
          });
          this.props.panelActions.setBleConnected(false);
          device.requestBleConnect(this.props.panel.mac);
        }
      },
    );
  };

  /**
   * 监听滑动，改变导航栏模式
   */
  dealWithScrollListener = () => {
    this.scrollY.addListener(({value}) => {
      const maxScrollDistance = 40;
      throttle(() => {
        if (value >= maxScrollDistance) {
          this.setState({
            headerOpacity: 1,
          });
        } else {
          const headerOpacity = value / maxScrollDistance;
          this.setState({
            headerOpacity,
          });
        }
      }, 1000)();
    });
  };

  /**
   * 监听ota升级完成，重置ota升级状态
   * ota升级完成之后可能会断开蓝牙连接，此时重连次数如果已经用完，发起重连
   */
  dealWithOtaCompletedChange = () => {
    CommonEmitter.addListener('otaUpdateDidCompleted', () => {
      // console.log('--otaUpdateDidCompleted--');
      this.fromOtaDone = true;

      this.props.panelActions.setOtaInfo({
        showRed: false,
      });
      this.props.panelActions.setIsForceUpdate(false);

      const {mac, deviceId, bleConnected} = this.props.panel;
      device.requestBleConnect(mac);
      // 再查询 ALL 物模型
      BleUtils.sendBleCmdToFetchAllModelData(mac);
      // 再次上报总成零件版本号
      OtaUtils.watchBleFirmwareVersionChange({deviceId, mac});

      if (!bleConnected) {
        this.resetBleConnect();
      }
    });
  };

  /**
   * 监听 原生->RN之间来回跳转的逻辑
   */
  dealWithRNContainerPushOrPop = () => {
    CommonEmitter.addListener('RNContainerViewWillAppear', () => {
      // console.log('RNContainerViewWillAppear', this.fromOtaDone);
      this.props.panelActions.setIsInRNContainerVC(true);
      // 每当回到RN时，检查下是否还有强制升级，如有，弹框
      const {isForceUpdate: isForce} = this.props.panel;
      if (!this.fromOtaDone && isForce) {
        this.handleAlertAndDot();
      }
    });
    CommonEmitter.addListener('RNContainerViewWillDisAppear', () => {
      // console.log('RNContainerViewWillDisAppear');
      this.props.panelActions.setIsInRNContainerVC(false);
      // 跳出RN vc时，该值赋值为false
      // this.fromOtaDone = false;
    });
  };

  /**
   *  处理监听蓝牙初始状态变化逻辑
   *  0:未开启  1:已开启  2:未授权
   *  未开启：提示用户开启蓝牙
   *  已开启：请求连接设备
   *  未授权：提示用户未授权并请求蓝牙连接
   */
  dealWithInitialBluetoothChange = () => {
    const {t} = this.props;
    CommonEmitter.addListener('bluetoothChange', res => {
      // console.log('bluetoothChange', res);
      const type = res - 0;
      const phoneBleStatus = bleChangeTypeMap[type];
      const mac = this.props.panel.mac;
      switch (phoneBleStatus) {
        case 'notOpened':
          mobile.toast(
            t(geti18nText('ble_notopened_textview_text', true)),
            () => {},
          );
          this.setState({
            connectStatus: 'unConnected',
          });
          break;
        case 'opened':
          this.retryTimes = 0; // 重新开启蓝牙时置位
          mobile.toast(
            t(geti18nText('ble_opened_textview_text', true)),
            () => {},
          );
          device.requestBleConnect(mac);
          break;
        case 'notAuthorized':
          mobile.toast(
            t(geti18nText('ble_notauthorizedtitle_textview_text', true)),
            () => {},
          );
          // device.requestBleConnect(mac);
          this.setState({
            connectStatus: 'unConnected',
          });
          break;
        default:
          break;
      }
    });
  };

  /**
   *  处理蓝牙连接后状态变化逻辑
   *  0:未连接  1:连接中  2:已连接  3:连接失败
   *  已连接：通过蓝牙获取设备信息，更新UTC时钟，OTA升级
   *  其他状态：尝试重连
   */
  dealWithDeviceBleConnectStateChange = () => {
    CommonEmitter.addListener('deviceBleConnectStateChange', res => {
      // console.log('deviceBleConnectStateChange---', res);
      // res: 0:unConnected  1:connecting  2:connected 3:failed
      const connectStatus = bleConnectStatusMap[Number(res)];
      // console.log('connectStatus', connectStatus);
      const isConnected = connectStatus === 'connected';
      // 重复已连接没有意义，过滤
      if (connectStatus === this.state.connectStatus && isConnected) {
        return;
      }

      this.setState({
        connectStatus,
      });

      if (isConnected) {
        // 同步store保存蓝牙连接状态
        this.props.panelActions.setBleConnected(true);
        // 重置蓝牙重连次数
        this.retryTimes = 0;

        this.getAllBleData();
        this.getUTCDate();
        // ota 检查目前只调用一次，后期根据缓存判断是否强制升级弹窗
        if (!this.otaUpdateDidChecked) {
          this.otaUpdateDidChecked = true;
          this.dealWithOtaUpdate();
        } else {
          this.handleAlertAndDot();
        }
      } else {
        if (this.props.panel.isNativeGoback) {
          return;
        }
        this.props.panelActions.setBleConnected(false);
        this.dealWithReConnect();
      }
    });
  };

  /**
   * 安卓物理返回键处理
   */
  dealWithAndroidBack = () => {
    if (Platform.OS === 'android') {
      // console.log('dealWithAndroidBack');
      CommonEmitter.addListener('keyBackDown', res => {
        if (LogUtils.currentPage === 'ViewPanelHome') {
          this.dealWithWillUnmount();
        }
        this.props.panelActions.setIsNativeGoback(true);
        // console.log('keyBackDown---res--', res);
        this.androidGoBack();
      });
    }
  };

  /**
   * RN容器focus后，再次查询设备详情
   */
  getDetaiListAfterRNFocus = () => {
    CommonEmitter.addListener('NAVIGATOR_ON_WILL_FOCUS', () => {
      // console.log('NAVIGATOR_ON_WILL_FOCUS');
      this.getDetailList();
    });
    this.getDetailList();
  };

  /**
   * 调用接口查询， 获取设备详情数据
   */
  getDetailList = () => {
    this.props.panelActions.fetchDeviceDetail();
  };

  /**
   * 更新UTC时钟
   */
  getUTCDate = () => {
    device.requestUTCDateResolveBlock?.(res => {
      // console.log('current utc date cmd hex:', res);
      const cmdHex = res;
      const {mac} = this.props.panel;
      device.sendCmd(cmdHex, mac);
    });
  };

  /**
   * 发送蓝牙指令，查询ALL
   * 包括电量 / 使用时长 / 剩余安时 / 历史使用时长等
   */
  getAllBleData = () => {
    CommonEmitter.addListener('localDeviceDataChange', res => {
      // console.log('localDeviceDataChange', res);
      this.props.panelActions.handleBleRes(res);
    });
    const {mac} = this.props.panel;
    BleUtils.sendBleCmdToFetchAllModelData(mac);
  };

  /**
   * 重置蓝牙连接，并尝试重连
   */
  resetBleConnect = () => {
    this.retryTimes = 0;
    this.dealWithReConnect();
  };

  /**
   * 重连
   */
  dealWithReConnect = () => {
    if (this.retryTimes > BLE_RETRY_Times) {
      this.setState({
        connectStatus: 'unConnected',
      });
      this.props.panelActions.setBleConnected(false);
      return;
    }
    device.requestBleConnect(this.props.panel.mac);
    this.retryTimes++;
  };

  componentWillUnmount() {
    this.dealWithWillUnmount();
  }

  dealWithWillUnmount = () => {
    // ble listener
    CommonEmitter.removeAllListeners('bluetoothChange');
    CommonEmitter.removeAllListeners('deviceBleConnectStateChange');
    CommonEmitter.removeAllListeners('localDeviceDataChange');

    // ota listener
    CommonEmitter.removeAllListeners('otaUpdateDidCompleted');

    // rn container
    CommonEmitter.removeAllListeners('NAVIGATOR_ON_WILL_FOCUS');
    CommonEmitter.removeAllListeners('RNContainerViewWillAppear');
    CommonEmitter.removeAllListeners('RNContainerViewWillDisAppear');

    // 设备注册
    CommonEmitter.removeAllListeners('deviceRegisterSuccess');

    // app-state
    this.appStateSubscription?.remove();

    // android物理返回键监听
    if (Platform.OS === 'android') {
      CommonEmitter.removeAllListeners('keyBackDown');
    }
    const {deviceId} = this.props.panel;
    OtaUtils.unsubscribeToRelatedOtaTopics(deviceId);
    OtaUtils.removeRelatedEventListners();
    MessageUtils.removeMessageListener();
    this.faultTimer && clearInterval(this.faultTimer);
  };

  /**
   * ota升级
   */
  dealWithOtaUpdate = () => {
    const {deviceId, mac} = this.props.panel;
    OtaUtils.watchBleFirmwareVersionChange({deviceId, mac}, () => {
      // 查询 ota
      OtaUtils.checkAndSubscribeToOTATask({deviceId}, otainfo => {
        const {isForceUpdate, showRed, customVersion = ''} = otainfo;
        console.log('🦺 checkOTATask---', otainfo);
        this.props.panelActions.setOtaInfo({
          showRed,
          otaVersion: customVersion,
        });
        this.props.panelActions.setIsForceUpdate(isForceUpdate);
        this.handleAlertAndDot();
      });
    });
  };

  /**
   * 处理强制升级弹框
   */
  handleAlertAndDot = () => {
    const {t} = this.props;
    const {isForceUpdate: isForce, isInRNContainerVC} = this.props.panel;
    // 弹框
    if (isForce && isInRNContainerVC && this.isDialogShow === false) {
      // console.log('--isForce--', isForce, isInRNContainerVC);
      this.isDialogShow = true;
      mobile.simpleConfirmDialog(
        ' ',
        t(geti18nText('home_otaAlertMsg_textview_text')),
        () => {
          this.isDialogShow = false;
          if (this.props.panel.bleConnected) {
            this.props.panelActions.jumpToOtaUpdate();
          } else {
            mobile.toast(
              t(geti18nText('ble_error_textview_text', true)),
              () => {},
            );
          }
        },
        () => {
          // console.log('cancel');
          this.dealWithGoBack();
        },
      );
    }
  };

  /**
   * 处理页面返回
   */
  dealWithGoBack = () => {
    this.props.panelActions.setIsNativeGoback(true);
    this.dealWithWillUnmount();
    mobile.back();
  };

  render() {
    const {connectStatus, headerOpacity, headerMode, navHeaderHeight} =
      this.state;
    const {deviceName} = this.props.panel;

    const {t} = this.props;

    const isBleConnected = connectStatus === 'connected';

    return (
      <PageView>
        <View
          style={styles.navHeaderContainer}
          onLayout={({nativeEvent}) => {
            const {height} = nativeEvent.layout;
            this.setState({
              navHeaderHeight: height,
            });
          }}>
          <HeaderView
            style={[
              styles.navHeader,
              {
                backgroundColor: 'rgba(255, 255, 255, ' + headerOpacity + ')',
              },
            ]}
            mode={headerMode}
            onLeftPress={() => {
              this.dealWithGoBack();
            }}
            showMore
            onRightPress={() => {
              this.goPage('ViewDetailList');
            }}
            title={
              <TouchableOpacity
                style={styles.headerTitleContainer}
                onPress={() => {
                  this.goPage('ViewEditName', {
                    deviceName,
                  });
                }}>
                <CustomText
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  fontFamily="Rubik"
                  fontWeight={500}
                  style={styles.headerDeviceName}>
                  {deviceName}
                </CustomText>
                <CustomText
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  fontWeight={500}
                  fontFamily="Rubik"
                  style={styles.headerConnectStatus}>
                  {t(bleConnectStatusLocaleMap[connectStatus])}
                </CustomText>
              </TouchableOpacity>
            }
          />
        </View>
        <Animated.ScrollView
          showsVerticalScrollIndicator={false}
          scrollEventThrottle={1}
          onScroll={Animated.event(
            [{nativeEvent: {contentOffset: {y: this.scrollY}}}],
            {useNativeDriver: true},
          )}>
          {/* 顶部电池和状态 */}
          <TopBattery navHeaderHeight={navHeaderHeight} />

          {/* 设备事件消息 */}
          <Notification />

          {/* 操作按钮 */}
          <ControlArea
            isBleConnected={isBleConnected}
            panel={this.props.panel}
          />

          {/* usage history */}
          <UsageHistory
            disabled={!isBleConnected}
            style={styles.usageHistoryContainer}
          />
          <WhiteSpace size={isIphoneX ? 34 : 0} />
        </Animated.ScrollView>
      </PageView>
    );
  }
}

export default withTranslation('all')(memo(Home));

const styles = CustomStyleSheet.create({
  navHeaderContainer: {
    position: 'absolute',
    zIndex: 1,
  },
  navHeader: {
    backgroundColor: '#F7F7F7',
    borderBottomWidth: 0,
  },
  headerTitleContainer: {
    alignItems: 'center',
  },
  headerDeviceName: {
    fontSize: 22,
    color: '#3C3936',
    lineHeight: 28,
  },
  headerConnectStatus: {
    fontSize: 16,
    color: '#000000',
    lineHeight: 28,
  },
  usageHistoryContainer: {
    paddingBottom: 20,
    borderRadius: 16,
  },
});
