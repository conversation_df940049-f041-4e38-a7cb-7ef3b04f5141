/*
 * @Description: 设备工作模式
 */

import tracer, {eleIdMap} from 'IOTRN/src/utils/tracer';
import ModelMap from '@utils/model';
import DeviceConnectionStore from 'IOTRN/src/mobx/deviceConnectionStore';

// 切换设备工作模式
class DeviceWorkModel {
  deviceConnectionStore: DeviceConnectionStore;
  constructor(deviceConnectionStore: DeviceConnectionStore) {
    this.deviceConnectionStore = deviceConnectionStore;
  }

  /**
   * @description: 切换割草机刀片的模式
   * @param {Boolean} mode
   */
  switchPerformanceMode = (mode: boolean) => {
    const {bleConnected} = this.deviceConnectionStore.state;
    let dataParams = false;
    tracer.click({
      eleid: eleIdMap.Mowing_Performance_Mode_Click,
      expand: {mode: `${Number(mode)}`},
    });
    if (bleConnected) {
      dataParams = mode;
    }
    const params = {
      [ModelMap.mowing_performance_mode]: Number(dataParams),
    };
    this.deviceConnectionStore.actions.editProperty({
      propertyData: params,
    });
  };

  /**
   * @description: 切换报警灯模式
   * @param {Number} status
   */
  switchAlarmMode = (status: number) => {
    const {bleConnected} = this.deviceConnectionStore.state;
    let dataParams = status;
    if (bleConnected) {
      dataParams = Number(status);
    }
    tracer.click({
      eleid: eleIdMap.Back_Up_Alert_Switch_Click,
      expand: {mode: `${Number(status)}`},
    });
    const params = {
      [ModelMap.back_up_alert_mode]: dataParams,
    };
    this.deviceConnectionStore.actions.editProperty({
      propertyData: params,
    });
  };

  /**
   * @description: 设置日光灯模式
   * @param {Number} status
   */
  switchDayTimeLight = (status: number) => {
    const params = {
      [ModelMap.day_time_light_mode]: status,
    };
    tracer.click({
      eleid: eleIdMap.Daytime_Light_Mode_Click,
      expand: {mode: `${status}`},
    });
    this.deviceConnectionStore.actions.editProperty({
      propertyData: params,
    });
  };

  /**
   * @description: 设置关灯延时
   * @param {Number} seconds
   */
  switchLightOffDelay = (seconds: {index: number}) => {
    const params = {
      [ModelMap.light_off_delay]: seconds.index,
    };
    this.deviceConnectionStore.actions.editProperty({
      propertyData: params,
    });
  };
}

export default DeviceWorkModel;
