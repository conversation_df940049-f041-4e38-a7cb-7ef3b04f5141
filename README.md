# power-hub

RN project, based on React Native 0.72.15

## 命名规范

RN project high-capacity-battery（React Native 0.63.2）

|  name   | branch |  android  | iOS |
|  ----  | ----  | ----  | ---- |
| 4831  | Feature_4831_STX4500_trimmer |4831_STX4500_trimmer.android | 4831_STX4500_trimmer.ios |
| 4831.1  | Feature_4831.1_BCX4500_brushCutter | 4831.1_BCX4500_brushCutter.android | 4831.1_BCX4500_brushCutter.ios |
| 86001 | Feature_86001_EDX2000_straightShaftEdger | 86001_EDX2000_straightShaftEdger.android | 86001_EDX2000_straightShaftEdger.ios |
| 86002 | Feature_86002_EDX2000-C_curvedShaftEdger | 86002_EDX2000-C_curvedShaftEdger.android | 86002_EDX2000-C_curvedShaftEdger.ios |
| 83001 | Feature_83001_HTX5300-P_stickHedgeTrimmer | 83001_HTX5300-P_stickHedgeTrimmer.android | 83001_HTX5300-P_stickHedgeTrimmer.ios |
| 83002 | Feature_83002_HTX5300-PA_articulatedHedgeTrimmer | 83002_HTX5300-PA_articulatedHedgeTrimmer.android | 83002_HTX5300-PA_articulatedHedgeTrimmer.ios |
| 83004 | Feature_83004_HTX5310-P_extendedHedgeTrimmer | 83004_HTX5310-P_extendedHedgeTrimmer.android | 83004_HTX5310-P_extendedHedgeTrimmer.ios |

## Structure
.
├── App.js
├── app.json
├── bundle.sh                       打包脚本
├── index.js
├── metro.config.js
├── package.json
├── public                          公共dll文件
│   └── dll
├── src
│   ├── api                         api接口
│   ├── assets                      图片等资源文件
│   ├── components                  公共组件
│   ├── config                      配置文件
│   ├── containers                  业务组件
│   ├── i18n                        国际化
│   ├── mobx
│   ├── pages
├── tests
│   ├── components
│   └── pages
└── yarn.lock

## pages

|  页面   | 解释 |
|  ----  | ----  |
| panel  | 控制面板首页 |
| detail  | 面板详情 |
