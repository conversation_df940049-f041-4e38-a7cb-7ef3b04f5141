import React from 'react';
import renderer from 'react-test-renderer';
import Home from '@pages/panel/Home.js';

describe('Home component', () => {
  let component;

  beforeEach(() => {
    component = renderer.create(<Home />);
  });

  it('renders without crashing', () => {
    expect(component.toJSON()).toMatchSnapshot();
  });

  it('initializes with correct state', () => {
    const instance = component.getInstance();
    expect(instance.state.connectStatus).toEqual('unConnected');
  });

  it('handles bluetooth change correctly', () => {
    const instance = component.getInstance();
    const res = 1;
    instance.dealWithInitialBluetoothChange();
    instance.dealWithDeviceBleConnectStateChange(res);
    expect(instance.state.connectStatus).toEqual('connecting');
  });

  it('handles update correctly', () => {
    const instance = component.getInstance();
    const res = {
      isForceUpdate: false,
      showRed: true,
      customVersion: '1.0.0',
    };
    instance.dealWithUpdate(res);
    expect(instance.props.panelActions.setOtaResult).toHaveBeenCalledWith(res);
  });
});