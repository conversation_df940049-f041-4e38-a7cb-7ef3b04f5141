/*
Code Analysis

Main functionalities:
The List class is a React component that extends the PageBase class and displays a list of device details. It listens to the NAVIGATOR_ON_WILL_FOCUS event and fetches the device details using the deviceDetail action. It also provides functionality to unbind the device and navigate to different pages based on the item clicked in the list.

Methods:
- componentDidMount(): adds a listener for the NAVIGATOR_ON_WILL_FOCUS event and fetches the device details using the deviceDetail action.
- componentWillUnmount(): removes the listener for the NAVIGATOR_ON_WILL_FOCUS event.
- getDetailList(): calls the deviceDetail action to fetch the device details.
- handleUnbindDevice(): displays a confirmation dialog and unbinds the device using the deviceUnbind action.
- render(): renders the list of device details using the DataListCard component and provides functionality to navigate to different pages based on the item clicked in the list.

Fields:
- None.
*/

import React from 'react';
import {render} from '@testing-library/react-native';
import {Provider} from 'mobx-react';
import List from '@pages/detail/List';


describe('List class', () => {

  // Tests that the component renders with correct props and data
  it("test_rendering_with_correct_props", () => {
      const panelActions = {
          deviceDetail: jest.fn(),
          deviceUnbind: jest.fn(),
      };
      const panel = {
          showRed: false,
          deviceDetail: {
              nickName: 'Test Device',
              infoStatus: 1,
          },
          bleConnected: true,
          deviceId: '12345',
          productId: '67890',
      };
      const {getByText, getByTestId} = render(
          <Provider panelActions={panelActions} panel={panel}>
              <List />
          </Provider>,
      );
      expect(getByText('Test Device')).toBeTruthy();
      expect(getByText('Registered')).toBeTruthy();
      expect(getByTestId('devicename-item')).toBeTruthy();
  });

  // Tests that clicking on each item in the DataListCard navigates to the correct page
  it("test_navigating_to_correct_page", () => {
      const panelActions = {
          deviceDetail: jest.fn(),
          deviceUnbind: jest.fn(),
      };
      const panel = {
          showRed: false,
          deviceDetail: {
              nickName: 'Test Device',
              infoStatus: 1,
          },
          bleConnected: true,
          deviceId: '12345',
          productId: '67890',
      };
      const {getByTestId} = render(
          <Provider panelActions={panelActions} panel={panel}>
              <List />
          </Provider>,
      );
      fireEvent.press(getByTestId('devicename-item'));
      expect(panelActions.deviceDetail).toHaveBeenCalled();
  });

  // Tests that clicking on the unbind device button successfully unbinds the device
  it("test_unbinding_device", () => {
      const panelActions = {
          deviceDetail: jest.fn(),
          deviceUnbind: jest.fn(),
      };
      const panel = {
          showRed: false,
          deviceDetail: {
              nickName: 'Test Device',
              infoStatus: 1,
          },
          bleConnected: true,
          deviceId: '12345',
          productId: '67890',
      };
      const {getByText} = render(
          <Provider panelActions={panelActions} panel={panel}>
              <List />
          </Provider>,
      );
      fireEvent.press(getByText('Unbind Device'));
      expect(panelActions.deviceUnbind).toHaveBeenCalled();
  });

  // Tests the behaviour when no device detail is available
  it("test_no_device_detail_available", () => {
      const panelActions = {
          deviceDetail: jest.fn(),
          deviceUnbind: jest.fn(),
      };
      const panel = {
          showRed: false,
          deviceDetail: null,
          bleConnected: true,
          deviceId: '12345',
          productId: '67890',
      };
      const {getByText} = render(
          <Provider panelActions={panelActions} panel={panel}>
              <List />
          </Provider>,
      );
      expect(getByText('Device Name')).toBeTruthy();
      expect(getByText('Unregistered')).toBeTruthy();
  });

  // Tests the behaviour when the device is not registered
  it("test_device_not_registered", () => {
      const panelActions = {
          deviceDetail: jest.fn(),
          deviceUnbind: jest.fn(),
      };
      const panel = {
          showRed: false,
          deviceDetail: {
              nickName: 'Test Device',
              infoStatus: 0,
          },
          bleConnected: true,
          deviceId: '12345',
          productId: '67890',
      };
      const {getByText} = render(
          <Provider panelActions={panelActions} panel={panel}>
              <List />
          </Provider>,
      );
      expect(getByText('Device Name')).toBeTruthy();
      expect(getByText('Unregistered')).toBeTruthy();
  });

  // Tests the behaviour when the device is not connected to BLE
  it("test_device_not_connected_to_BLE", () => {
      const panelActions = {
          deviceDetail: jest.fn(),
          deviceUnbind: jest.fn(),
      };
      const panel = {
          showRed: false,
          deviceDetail: {
              nickName: 'Test Device',
              infoStatus: 1,
          },
          bleConnected: false,
          deviceId: '12345',
          productId: '67890',
      };
      const {getByText} = render(
          <Provider panelActions={panelActions} panel={panel}>
              <List />
          </Provider>,
      );
      expect(getByText('Device Name')).toBeTruthy();
      expect(getByText('Registered')).toBeTruthy();
  });
});
