import {convertTo12HourFormat, convertTo24HourFormat} from '@utils/time';

describe('test convertTo12HourFormat', () => {
  it('should return 12:23 AM', () => {
    expect(convertTo12HourFormat('00:23')).toBe('12:23 AM');
  });

  it('should return 6:23 PM', () => {
    expect(convertTo12HourFormat('06:23')).toBe('6:23 AM');
  });

  // eslint-disable-next-line jest/no-identical-title
  it('should return 12:23 AM', () => {
    expect(convertTo12HourFormat('12:23')).toBe('12:23 PM');
  });

  it('should return 3:23 PM', () => {
    expect(convertTo12HourFormat('15:23')).toBe('3:23 PM');
  });

  it('should return 23:23 AM', () => {
    expect(convertTo12HourFormat('23:23')).toBe('11:23 PM');
  });
});

describe('test convertTo24HourFormat', () => {
  it('should return 00:23', () => {
    expect(convertTo24HourFormat('12:23 AM')).toBe('00:23');
  });

  it('should return 06:23', () => {
    expect(convertTo24HourFormat('6:23 AM')).toBe('06:23');
  });

  // eslint-disable-next-line jest/no-identical-title
  it('should return 00:23', () => {
    expect(convertTo24HourFormat('12:23 PM')).toBe('12:23');
  });

  it('should return 15:23', () => {
    expect(convertTo24HourFormat('3:23 PM')).toBe('15:23');
  });

  it('should return 23:23', () =>
    expect(convertTo24HourFormat('11:23 PM')).toBe('23:23'));
});
