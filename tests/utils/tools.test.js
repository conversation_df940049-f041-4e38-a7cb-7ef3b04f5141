import {
  secondToHour,
  secondToHourAndMin,
  milliamValueToValue,
  milliamValueToStringValue,
} from '@utils/tools';

describe('test secondToHour in tools', () => {
  // Tests that an input of 3600 seconds returns 1 hour
  it('test_happy_path_1', () => {
    expect(secondToHour(3600)).toBe(1);
  });

  // Tests that an input of 7200 seconds returns 2 hours
  it('test_happy_path_2', () => {
    expect(secondToHour(7200)).toBe(2);
  });

  // Tests that an input of 18000 seconds returns 5 hours
  it('test_happy_path_3', () => {
    expect(secondToHour(18000)).toBe(5);
  });

  // Tests that an input of 0 seconds returns 0 hours
  it('test_edge_case_1', () => {
    expect(secondToHour(0)).toBe(0);
  });

  // Tests that the function always returns a number with 2 decimal places
  it('test_general_behaviour_1', () => {
    expect(secondToHour(3601)).toBeCloseTo(1.0, 2);
  });
});

describe('test secondToHourAndMin in tools', () => {
  // Tests that the function returns the expected object for an input of 3600
  it('test_happy_path_3600', () => {
    const result = secondToHourAndMin(3600);
    expect(result).toEqual({hour: '01', min: '00'});
  });

  // Tests that the function returns the expected object for an input of 3661
  it('test_happy_path_3661', () => {
    const result = secondToHourAndMin(3661);
    expect(result).toEqual({hour: '01', min: '01'});
  });

  // Tests that the function returns the expected object for an input of 7200
  it('test_happy_path_7200', () => {
    const result = secondToHourAndMin(7200);
    expect(result).toEqual({hour: '02', min: '00'});
  });

  // Tests that the function returns the expected object for an input of 0
  it('test_edge_case_0', () => {
    const result = secondToHourAndMin(0);
    expect(result).toEqual({hour: '00', min: '00'});
  });

  // Tests that the function returns the expected object for an input of 86399
  it('test_edge_case_86399', () => {
    const result = secondToHourAndMin(86399);
    expect(result).toEqual({hour: '23', min: '59'});
  });
});

describe('test milliamValueToValue in tools', () => {
  // Tests that milliamValueToValue returns the correct value when milliam is 1000 and fixCount is 2
  it('test_happy_path_milliam_1000_fixCount_2', () => {
    const result = milliamValueToValue(1000, 2);
    expect(result).toBe(1);
  });

  // Tests that milliamValueToValue returns the correct value when milliam is 5000 and fixCount is 0
  it('test_happy_path_milliam_5000_fixCount_0', () => {
    const result = milliamValueToValue(5000, 0);
    expect(result).toBe(5);
  });

  // Tests that milliamValueToValue returns the correct value when milliam is 123456 and fixCount is 4
  it('test_happy_path_milliam_123456_fixCount_4', () => {
    const result = milliamValueToValue(123456, 4);
    expect(result).toBe(123.456);
  });

  // Tests that milliamValueToValue returns the correct value when milliam is 0 and fixCount is 2
  it('test_edge_case_milliam_0_fixCount_2', () => {
    const result = milliamValueToValue(0, 2);
    expect(result).toBe(0);
  });

  // Tests that milliamValueToValue returns the correct value when milliam is 1000 and fixCount is 0
  it('test_edge_case_milliam_1000_fixCount_0', () => {
    const result = milliamValueToValue(1000, 0);
    expect(result).toBe(1);
  });

  // Tests that milliamValueToValue returns the correct value when milliam is undefined and fixCount is 2
  it('test_edge_case_milliam_undefined_fixCount_2', () => {
    const result = milliamValueToValue(undefined, 2);
    expect(result).toBe(NaN);
  });
});

describe('test milliamValueToStringValue in tools', () => {
  // Tests that the function returns the correct string for a positive integer milliam value
  it('test_positive_integer_milliam', () => {
    const result = milliamValueToStringValue(5000);
    expect(result).toBe('5.00');
  });

  // Tests that the function returns the correct string for a positive integer fixCount value
  it('test_positive_integer_fix_count', () => {
    const result = milliamValueToStringValue(5000, 4);
    expect(result).toBe('5.0000');
  });

  // Tests that the function returns '0' for a milliam value of 0
  it('test_zero_milliam', () => {
    const result = milliamValueToStringValue(0, 0);
    expect(result).toBe('0');
  });

  // Tests that the function returns the correct integer value for a fixCount of 0
  it('test_zero_fix_count', () => {
    const result = milliamValueToStringValue(5000, 0);
    expect(result).toBe('5');
  });

  // Tests that the function returns the correct string for a fixCount greater than the number of decimal places in the result
  it('test_fix_count_greater_than_decimal_places', () => {
    const result = milliamValueToStringValue(5000, 5);
    expect(result).toBe('5.00000');
  });

  // Tests that the function returns the correct string for a negative milliam value
  it('test_negative_milliam', () => {
    const result = milliamValueToStringValue(-5000);
    expect(result).toBe('-5.00');
  });
});
