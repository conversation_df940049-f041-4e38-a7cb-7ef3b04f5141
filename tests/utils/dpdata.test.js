import {parseDpData} from '@utils/dpdata';
import ModelMap from '@config/model.js';

describe('test dpdata', () => {
  it('test matching dp_id of working_status', () => {
    const item = {
      dp_id: ModelMap.working_status,
      dp_data: '0101',
    };
    expect(parseDpData(item)).toBe(257);
  });
  it('test matching dp_id of ac_charging_progress', () => {
    const item = {
      dp_id: ModelMap.ac_charging_progress,
      dp_data: '2B',
    };
    expect(parseDpData(item)).toBe(43);
  });
  it('test matching dp_id of ac_charging_remaining_time', () => {
    const item = {
      dp_id: ModelMap.ac_charging_remaining_time,
      dp_data: '4D',
    };
    expect(parseDpData(item)).toBe(77);
  });
  // Tests that the function returns the expected result when the item's dp_id matches one of the keys in the map object
  it('test matching dp_id of remaining_battery_of_batteries', () => {
    const item = {
      dp_id: ModelMap.remaining_battery_of_batteries,
      dp_data: '010203040102030401020304',
    };
    const expected = [67305985, 67305985, 67305985];
    const result = parseDpData(item);
    expect(result).toEqual(expected);
  });

  it('test matching dp_id of rated_capacity_of_batteries', () => {
    const item = {
      dp_id: ModelMap.rated_capacity_of_batteries,
      dp_data: '010203040102030401020304',
    };
    const expected = [67305985, 67305985, 67305985];
    const result = parseDpData(item);
    expect(result).toEqual(expected);
  });

  it('test matching dp_id of dc_charging_remaining_time_of_batteries', () => {
    const item = {
      dp_id: ModelMap.dc_charging_remaining_time_of_batteries,
      dp_data: '0102030401020304',
    };
    const expected = [67305985, 67305985];
    const result = parseDpData(item);
    expect(result).toEqual(expected);
  });

  it('test matching dp_id of first_portable_battery_status', () => {
    const item = {
      dp_id: ModelMap.first_portable_battery_status,
      dp_data: '0101020304050607080000000000000000',
    };
    const expected = {
      battery_status: 1,
      deviceId: 578437695752307200,
    };
    const result = parseDpData(item);
    expect(result).toEqual(expected);
  });
  // Tests that the function returns the expected result when the item's dp_data is a string
  it('test_string_dp_data', () => {
    const item = {
      dp_id: '1234',
      dp_data: '01020304',
    };
    const expected = 67305985;
    const result = parseDpData(item);
    expect(result).toEqual(expected);
  });

  // Tests that the function returns the expected result when the item's dp_data is an array of objects with param_data_value property
  it('test_array_dp_data', () => {
    const item = {
      dp_id: '1234',
      dp_data: [
        {
          param_id: '',
          param_type: '',
          param_data_len: '',
          param_data_value: '01020304',
        },
      ],
    };
    const expected = 67305985;
    const result = parseDpData(item);
    expect(result).toEqual(expected);
  });

  // Tests that the function returns the dp_data when the item is undefined
  it('test_undefined_item', () => {
    const item = undefined;
    const result = parseDpData(item);
    expect(result).toBeUndefined();
  });

  // Tests that the function returns the dp_data when the item's dp_id is not a string
  it('test_non_string_dp_id', () => {
    const item = {
      dp_id: 1234,
      dp_data: '01020304',
    };
    const expected = 67305985;
    const result = parseDpData(item);
    expect(result).toEqual(expected);
  });

  // Tests that the function returns the dp_data when the item's dp_data is an empty string or an empty array
  it('test_empty_dp_data', () => {
    const item1 = {
      dp_id: '1234',
      dp_data: '',
    };
    const item2 = {
      dp_id: '1234',
      dp_data: [],
    };
    const expected = 0;
    const result1 = parseDpData(item1);
    const result2 = parseDpData(item2);
    expect(result1).toEqual(expected);
    expect(result2).toEqual(expected);
  });
});
